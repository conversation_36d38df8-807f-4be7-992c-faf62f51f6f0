import { useState, useEffect, useRef, useCallback } from 'react'
import * as SecureStore from 'expo-secure-store'
import { AppState } from 'react-native'
import { useDispatch } from 'react-redux'
import { useAppSelector } from './reduxHooks'
import { login } from '@/redux/slices/auth/signIn'

type UseGuestUser = boolean

const useGuestUser = (): UseGuestUser => {
  const dispatch = useDispatch()
  const [isGuest, setIsGuest] = useState<boolean>(true)
  const { user } = useAppSelector((state) => state.signInReducer)
  const isMounted = useRef(true)

  const checkLoginStatus = useCallback(async () => {
    const isLoggedIn = await SecureStore.getItemAsync('isLoggedIn')
    const oauthToken = await SecureStore.getItemAsync('oauthToken')
    let shouldPersistUser = false
    let isActualLoggedIn = false

    if (oauthToken && isLoggedIn) {
      // check expired time
      const { expireTime } = JSON.parse(oauthToken)
      if (Date.now() < expireTime) {
        shouldPersistUser = true
        isActualLoggedIn = true
      } else {
        await SecureStore.deleteItemAsync('isLoggedIn')
      }
    }

    setIsGuest(isActualLoggedIn === false)

    // if user is logged in and user is not in redux store
    if (isLoggedIn && Object.keys(user).length === 0 && shouldPersistUser) {
      console.debug('login but not in redux')
      const userInfo = JSON.parse(isLoggedIn)
      dispatch(
        login({
          user: userInfo,
        }),
      )
    }
  }, [user])

  useEffect(() => {
    checkLoginStatus()

    const subscription = AppState.addEventListener('change', (state) => {
      console.debug('checking!!', state)
      if (state === 'active') {
        checkLoginStatus()
      }
    })

    return () => {
      isMounted.current = false
      subscription.remove()
    }
  }, [checkLoginStatus])

  return isGuest
}

export default useGuestUser
