import React, { useEffect, useState } from 'react'
import { Text, View, ScrollView, Pressable, Image, Linking, FlatList, TouchableOpacity } from 'react-native'
import hubStyle from '@/screens/Hub/hub.style'
import AssetListStyle from './AssetList.style'
import { themeColor } from '@/theme/theme'
import BackNavigation from '@/screens/Utilities/CustomHeader/BackNavigation'
import { NativeStackScreenProps } from '@react-navigation/native-stack'
import { RootStackParamList } from '@/types/navigations'
import useAuth from '@/hooks/useAuth'
import * as FileSystem from 'expo-file-system';
import { ProductState } from '@/types/productFilter'
import { apiService } from '@/redux/slices/util/apiRequest'
import AssetItem from './AssetItem'
import { useAccount } from 'wagmi'

import HubAcssetSleketon from '@/src/skeletons/screens/hub/HubAssetSleketon'
import CustomSVG from '@/src/components/CustomSVG/CustomSVG'
import RightIcon from "@/assets/svgs/hub/icon_right.svg"
import downLoadIcon from '@/assets/svgs/hub/icon_download.svg'

type Props = NativeStackScreenProps<RootStackParamList, 'AssetList'>

const URL = `${process.env.EXPO_PUBLIC_BASE_API_URL}/hub/nfts/owner`
const DOWNLOAD_URL = `${process.env.EXPO_PUBLIC_BASE_API_URL}/hub/account/nfts/download`;
const AssetList = (props: Props) => {
    const [checked, setChecked] = useState(false)
    const [products, setProducts] = useState<ProductState[]>([])
    const [loading, setLoading] = useState<boolean>(true)
    const [page, setPage] = useState<number>(1)
    const [hasMore, setHasMore] = useState<boolean>(true)
    const [selectedIds, setSelectedIds] = useState<string>('')
    const { walletToken, lng } = useAuth()
    const { address } = useAccount()

    const { walletQuery, query } = apiService

    const handleFetchData = async () => {
        if (!walletToken) return
        setLoading(true)
        try {
            const response = await walletQuery(`${URL}/${address}`, 'GET', {
                'X-Wallet-Token': walletToken,
            })
            if (response.success === 1) {
                setProducts(response.data.data)
                setHasMore(false)
            } else {
                setHasMore(false)
            }
        } catch (error) {
            console.log('error', error)
            setHasMore(false)
        } finally {
            setLoading(false)
        }
    }

    useEffect(() => {
        handleFetchData()
    }, [page])


    const handleSelectedIdsChange = (id: Number, isChecked: Boolean) => {
        setSelectedIds(prevIds => {
            const idStr = id.toString();
            const idsArray = prevIds ? prevIds.split(',') : [];
            const idExists = idsArray.includes(idStr);
    
            if (isChecked) {
                if (!idExists) {
                    return prevIds ? `${prevIds},${id}` : `${id}`;
                }
            } else {
                if (idExists) {
                    return idsArray.filter(item => item !== idStr).join(',');
                }
            }
            return prevIds;
        });
    };

    const downloadFile = async () => {
        try {
            const response = await query(`${DOWNLOAD_URL}?nft=${selectedIds}`, 'GET', {
                'X-Oc-Merchant-Language': lng,
            })
            if (response.success === 1) {
                Linking.openURL(response.data);
            }
        } catch (error) {
            console.log('error', error);
        }
    }

    const handleChecked = () => {
        const newChecked = !checked
        setChecked(newChecked)
        if (newChecked && products.length > 0) {
            const allIds = products.map(product => product.id).join(',');
            setSelectedIds(allIds)
        } else {
            setSelectedIds('')
        }
    }

    const loadMoreData = () => {
        if (hasMore && !loading) {
            setPage((prevPage) => prevPage + 1)
        }
    }

    return (
        <View style={{ backgroundColor: themeColor.hubBackground, flex: 1, }}>
            <BackNavigation
                navigationProps={props.navigation}
                routeName="ASSET LIST"
                hubRouting={true}
                shareLink={''}
            />
            <View style={hubStyle.container}>
                <View style={hubStyle.hubLine} />
                <Pressable style={AssetListStyle.downloadContainer} onPress={downloadFile}>
                    <View style={AssetListStyle.downloadBtn}>
                        <CustomSVG svgIcon={downLoadIcon} />
                        <Text style={AssetListStyle.downloadText}>download</Text>
                    </View>
                    <View style={hubStyle.flexRow}>
                        <Text style={AssetListStyle.checkboxText}>All</Text>
                        <TouchableOpacity style={AssetListStyle.checkboxContainer} onPress={() => handleChecked()}>
                            <View style={AssetListStyle.checkbox} />
                            {checked && <Text style={AssetListStyle.checkmark}>
                                <CustomSVG svgIcon={RightIcon} />
                            </Text>}
                        </TouchableOpacity>
                    </View>
                </Pressable>
                <View style={AssetListStyle.assetContainer}>
                    {loading ? <HubAcssetSleketon /> : <FlatList
                        data={products}
                        keyExtractor={(item) => item.id.toString()}
                        renderItem={({ item }) => (
                            <AssetItem item={item} onIdsChange={handleSelectedIdsChange} isAllSelect={checked} />
                        )}
                        refreshing={loading}
                        // onEndReached={loadMoreData}
                        onEndReachedThreshold={0.5}
                        showsVerticalScrollIndicator={false}
                        ListFooterComponent={() => (
                            <View style={{ padding: 10, alignItems: 'center' }}>
                                {hasMore ? (
                                    <Text style={{ color: '#7d7d7d' }}>Load more...</Text>
                                ) : (
                                    <Text style={{ color: '#7d7d7d' }}>No more data available</Text>
                                )}
                            </View>
                        )}
                    />}
                </View>

            </View>
        </View>
    )
}
export default AssetList
