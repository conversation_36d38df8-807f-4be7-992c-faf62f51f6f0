import { StyleSheet } from 'react-native'
import dpr from '@/screens/Utilities/CustomStyleAttribute/dpr'
import { themeColor } from '@/theme/theme'

const AssetListStyle = StyleSheet.create({
    downloadContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginVertical: dpr(30),
    },
    downloadBtn: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: dpr(10),
        paddingHorizontal: dpr(20),
        borderRadius: dpr(26),
        backgroundColor: "#E8E6E3",
        marginRight: dpr(10)
    },
    downloadText: {
        fontSize: dpr(16),
        fontWeight: '600',
        color: '#2C2C2C',
    },
    checkboxText: {
        color: "#fff",
        marginRight: dpr(10),
    },
    assetContainer: {
        flex: 1,
    },
    assetCell: {
        backgroundColor: '#E8E6E3',
        padding: dpr(16),
        borderRadius: dpr(20),
        marginVertical: dpr(5),
    },
    assetTop: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start'
    },
    assetImage: {
        width: dpr(60),
        height: dpr(60),
        borderRadius: dpr(10),
    },
    assetMain: {
        paddingTop: dpr(12),
        paddingBottom: dpr(20),
        borderBottomWidth: dpr(1),
        borderColor: '#383939',
    },
    assetName: {
        fontSize: dpr(20),
        fontWeight: '600'
    },
    assetNo: {
        fontSize: dpr(14),
        color: '#999'
    },
    assetContent: {
        marginTop: dpr(16),
    },
    assetInfo: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: dpr(12),
    },
    assetInfoTitle: {
        fontSize: dpr(14),
        color: '#666'
    },
    checkboxContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    checkbox: {
        width: dpr(24),
        height: dpr(24),
        borderRadius: dpr(4),
        borderWidth: 1,
        borderColor: 'rgba(60, 60, 60, 0.2',
        backgroundColor: 'white',
        justifyContent: 'center',
        alignItems: 'center',
    },
    checked: {
        
    },
    checkmark: {
        position: 'absolute',
        fontSize: 18,
    },
})
export default AssetListStyle