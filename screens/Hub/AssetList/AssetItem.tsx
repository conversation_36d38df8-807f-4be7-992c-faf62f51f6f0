import React, { FC, useEffect, useState } from "react";
import { View, Text, Image, TouchableOpacity } from "react-native";
import AssetListStyle from "./AssetList.style";
import { ProductState } from "@/types/productFilter";
import { truncateMiddle } from "@/helper/txtSliceFmt";

import RightIcon from "@/assets/svgs/hub/icon_right.svg"
import CustomSVG from "@/src/components/CustomSVG/CustomSVG";

interface AssetItemProps {
    item: ProductState
    isAllSelect: boolean
    onIdsChange: (id: Number, isChecked: Boolean) => void
}
const AssetItem: FC<AssetItemProps> = ({ item, isAllSelect, onIdsChange }) => {
    const [checked, setChecked] = useState(false)
    const handleChecked = () => {
        const newChecked = !checked;
        console.log("newChecked:", newChecked);
        onIdsChange(item.id, newChecked);
        setChecked(newChecked);
    };

    useEffect(() => {
        setChecked(isAllSelect)
    }, [isAllSelect]);

    return (
        <View style={AssetListStyle.assetCell}>
            <View style={AssetListStyle.assetTop}>
                <Image source={{ uri: item.image }} style={AssetListStyle.assetImage} />
                <TouchableOpacity style={AssetListStyle.checkboxContainer} onPress={handleChecked}>
                    <View style={[AssetListStyle.checkbox]} />
                    { checked && <Text style={AssetListStyle.checkmark}>
                        <CustomSVG svgIcon={RightIcon} />
                    </Text>}
                </TouchableOpacity>
            </View>
            <View style={AssetListStyle.assetMain}>
                <Text style={AssetListStyle.assetName}>{item.name}</Text>
                <Text style={AssetListStyle.assetNo}>Product No: {item.id}</Text>
            </View>
            <View style={AssetListStyle.assetContent}>
                <View style={AssetListStyle.assetInfo}>
                    <Text style={AssetListStyle.assetInfoTitle}>Contract Address</Text>
                    <Text>{truncateMiddle(item.contract_address || '')}</Text>
                </View>
                <View style={AssetListStyle.assetInfo}>
                    <Text style={AssetListStyle.assetInfoTitle}>Token Standard</Text>
                    <Text>{item.token_type}</Text>
                </View>
                <View style={AssetListStyle.assetInfo}>
                    <Text style={AssetListStyle.assetInfoTitle}>Token ID</Text>
                    <Text>{item.token_id}</Text>
                </View>
                <View style={AssetListStyle.assetInfo}>
                    <Text style={AssetListStyle.assetInfoTitle}>Chain</Text>
                    <Text>{item.chain}</Text>
                </View>
            </View>
        </View>
    )
};
export default AssetItem;