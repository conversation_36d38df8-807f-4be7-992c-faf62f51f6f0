import { FC, useState } from "react"
import { View, Text, Image, Pressable, Animated } from "react-native"
import hubStyle from '@/screens/Hub/hub.style'
import HallStyle from "./index.style"
import { useNavigation } from '@react-navigation/native'
import CustomSVG from '@/src/components/CustomSVG/CustomSVG'
import { ExhibitionHallDataState } from '@/types/exhibition'
import Share from 'react-native-share'
import CustomModal from '@/src/components/CustomModal/CustomModal'
import CustomModalContent, { ModalContent } from '@/src/components/CustomModal/CustomModalContent'
import { deleteExhibitionHall } from '@/redux/slices/exhibition/exhibitionHall'
import useCustomToast from "@/hooks/useCustomToast"
import { useAppDispatch, useAppSelector } from "@/hooks/reduxHooks"
import useAuth from "@/hooks/useAuth"

import IconEdit from "@/assets/svgs/hub/icon_edit.svg"
import IconShare from "@/assets/svgs/hub/icon_share.svg"
import IconDelete from "@/assets/svgs/hub/icon_delete.svg"
import { NativeStackNavigationProp } from "@react-navigation/native-stack"
import { RootStackParamList } from "@/types/navigations"


interface HallItemProps {
    item: ExhibitionHallDataState
    index: number
    isPressed: boolean
    onPress: (id: number) => void
}

type DeleteHallResult = {
    payload: {
        hallId: number
        response: {
            success: number
        };
    };
};
const HallItem: FC<HallItemProps> = ({ item, index, isPressed, onPress }) => {
    const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>()
    const [visible, setVisible] = useState<boolean>(false)
    const showToast = useCustomToast()
    const dispatch = useAppDispatch()
    const { lng } = useAuth()
    const [modalConfig, setModalConfig] = useState<ModalContent | null>(null)
    const { loading } = useAppSelector((state) => state.exhibitionHall)
    const handleEditHall = (id: number) => {
        onPress(0)
        navigation.navigate('EditHall', { id })
    }

    const handleDelete = (id: number) => {
        setModalConfig({
            message: "Are you sure you want to delete this Exhibition Hall?",
            type: 'info',
            buttons: [
                {
                    text: "No",
                    onPress: () => {
                        setVisible(false)
                    },
                    type: 'secondary',
                },
                {
                    text: "Yes",
                    onPress: async () => {
                        handleDeleteHall()
                    },
                    type: 'cancel',
                },
            ],
        })
        setVisible(true)
    }

    const handleDeleteHall = async () => {
        if (loading) return
        try {
            const result = await dispatch(deleteExhibitionHall({ hallId: item.exhibition_hall_id, lng })) as DeleteHallResult
            const { payload } = result
            if (payload.response.success === 1) {
                showToast({
                    text1: "Exhibition Hall deleted successfully.",
                    type: 'common',
                    position: 'top',
                    props: { variant: 'success' },
                })
            } else {
                showToast({
                    text1: "Failed to delete the Exhibition Hall. Please try again.",
                    type: 'common',
                    position: 'top',
                    props: { variant: 'error' },
                })
            }
        } catch (error) {
            console.log(error);
        } finally {
            setVisible(false)
        }

    }

    const handleShareHall = async () => {
    const shareLink = `${process.env.EXPO_PUBLIC_HUB_DOMAIN}/exhibition/${item.exhibition_hall_id}`
        await Share.open({ url: shareLink })
    }

    const CancelModal = () => {
        setVisible(false)
        onPress(0)
    }



    return (
        <Pressable style={[HallStyle.hallItem, isPressed && { backgroundColor: "#fff" }]} key={index} onPress={() => onPress(item.exhibition_hall_id)}>
            {isPressed && <View style={HallStyle.shadeBox} />}
            <View style={HallStyle.itemBanner}>
                <Image source={{ uri: item.thumb_avatar }} style={HallStyle.avatarImg} />
                <Image source={{ uri: item.thumb_image }} style={HallStyle.bannerImg} />
            </View>
            <Text style={[HallStyle.hallName, !isPressed && { color: "#fff" }]}>{item.name}</Text>
            <Text style={[HallStyle.hallDesc, !isPressed && { color: "#fff" }]}>{item.description}</Text>
            {isPressed && <Animated.View style={[HallStyle.hallBtnCell, hubStyle.flexRow]}>
                <Pressable style={HallStyle.iconContainer} onPress={() => handleEditHall(item.exhibition_hall_id)}>
                    <CustomSVG svgIcon={IconEdit} />
                </Pressable>
                <Pressable style={HallStyle.iconContainer} onPress={handleShareHall}>
                    <CustomSVG svgIcon={IconShare} />
                </Pressable>
                <Pressable style={HallStyle.iconContainer} onPress={() => handleDelete(item.exhibition_hall_id)}>
                    <CustomSVG svgIcon={IconDelete} />
                </Pressable>
            </Animated.View>}
            <CustomModal isVisible={visible} onCloseModal={CancelModal}>
                {modalConfig && <CustomModalContent {...modalConfig} />}
            </CustomModal>
        </Pressable>
    )
}
export default HallItem