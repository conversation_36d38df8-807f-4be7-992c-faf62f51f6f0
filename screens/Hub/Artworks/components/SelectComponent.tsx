import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Image, Pressable, ScrollView, TextInput } from 'react-native';
import dpr from '@/screens/Utilities/CustomStyleAttribute/dpr'
import CustomSVG from '@/src/components/CustomSVG/CustomSVG';
import ArrowIcon from "@/assets/svgs/hub/icon_arrow.svg"
import { NFTItems } from '@/types/hubDetailState'
import { artworksDataState } from "@/types/exhibition"
import { useAccount } from 'wagmi';
import useAuth from '@/hooks/useAuth';
import { apiService } from '@/redux/slices/util/apiRequest'

interface SelectProps {
  placeholder?: string;
  hallId: number
  value?: string;
  onChange?: (item: NFTItems) => void;
}

const URL = `${process.env.EXPO_PUBLIC_BASE_API_URL}/hub/nfts/owner`
const SelectComponent = ({ placeholder, value, hallId, onChange }: SelectProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [options, setOptions] = useState<NFTItems[]>([]);
  const [filteredOptions, setFilteredOptions] = useState<NFTItems[]>([])
  const [scrollHeight, setScrollHeight] = useState<number>(60)
  const [selectedOption, setSelectedOption] = useState<NFTItems | null>(null);
  const [searchText, setSearchText] = useState('')
  const { address } = useAccount()
  const { lng, walletToken } = useAuth()
  const { walletQuery } = apiService
  const toggleDropdown = () => {
    setIsOpen(!isOpen)
  };

  const handleSelect = (item: NFTItems) => {
    setSearchText('')
    setIsOpen(false);
    onChange && onChange(item)
  };

  useEffect(() => {
    queryOptions()
  }, [hallId])

  const queryOptions = async () => {
    try {
      const response = await walletQuery(`${URL}/${address}&exhibition_hall_id=${hallId}`, 'GET', {
        'X-Wallet-Token': walletToken,
      })
      if (response.success === 1) {
        const data = response.data.data.filter((item: NFTItems) => item.is_hall_nft_exist != 1)
        setOptions(data)
      }
    } catch (error) {
      console.log('error', error);
    }
  }

  useEffect(() => {
    if (searchText.length > 0) {
      setFilteredOptions(
        options.filter((item) => item.name.toLowerCase().includes(searchText.toLowerCase())),
      )
    }
  }, [searchText])

  return (
    <View style={styles.container}>
      <Pressable style={styles.selectBox}
        onPress={toggleDropdown}>
        {/* <Text style={styles.selectedText}>
          {selectedOption ? selectedOption.name : placeholder || '请选择'}
        </Text> */}
        <TextInput
          onPress={() => {
            setIsOpen(true)
          }}
          value={searchText}
          placeholder={'Please Select'}
          onChangeText={setSearchText}
          style={styles.selectedText}
        />
        <CustomSVG
          svgIcon={ArrowIcon}
          style={[styles.arrow, isOpen && styles.arrowOpen]}
        />
      </Pressable>

      {isOpen && (
        <ScrollView style={styles.dropdown}>
          <View style={{ flex: 1 }}>
            {searchText.length > 0 ? (
              filteredOptions.length > 0 ? (
                filteredOptions.map((item) => (
                  <TouchableOpacity
                    key={item.token_id}
                    style={styles.option}
                    onPress={() => {
                      handleSelect(item)
                    }}>
                    <Image source={{ uri: item.image_cache }} style={styles.itemImage} />
                    <Text style={styles.optionText}>{item.name}</Text>
                  </TouchableOpacity>
                ))
              ) : (
                <Text>No data</Text>
              )
            ) : (
              options.map((item) => (
                <TouchableOpacity
                  key={item.token_id}
                  style={styles.option}
                  onPress={() => {
                    handleSelect(item)
                  }}>
                  <Image source={{ uri: item.image_cache }} style={styles.itemImage} />
                  <Text style={styles.optionText}>{item.name}</Text>
                </TouchableOpacity>
              ))
            )}
          </View>
        </ScrollView>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    marginBottom: dpr(5),
    width: dpr(323),
    height: dpr(323)
  },
  selectBox: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: dpr(16),
    paddingHorizontal: dpr(20),
    borderWidth: 1,
    borderColor: '#00000010',
    borderRadius: dpr(10),
    backgroundColor: '#fff',
  },
  selectedText: {
    fontSize: 16,
    color: '#333',
  },
  arrow: {
    transform: [{ rotate: '0deg' }]
  },
  arrowOpen: {
    transform: [{ rotate: '180deg' }]
  },
  dropdown: {
    position: 'absolute',
    top: 60,
    left: 0,
    right: 0,
    maxHeight: 256,
    borderWidth: 1,
    borderColor: '#00000010',
    borderRadius: dpr(10),
    backgroundColor: '#fff',
    zIndex: 1000,
    padding: dpr(18),
  },
  option: {
    padding: dpr(10),
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    flexDirection: 'row',
    alignItems: 'center',
    gap: dpr(10)
  },
  optionText: {
    fontSize: 16,
    color: '#333',
  },
  itemImage: {
    width: dpr(40),
    height: dpr(40),
    borderRadius: dpr(10),
  }
});

export default SelectComponent;