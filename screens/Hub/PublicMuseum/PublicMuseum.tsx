import React, { useCallback, useEffect, useRef, useState } from 'react'
import { FlatList, Modal, Pressable, Text, View } from 'react-native'
import { HubBottomTabScreenProps } from '@/types/navigations'
import MenuNavigation from '@/screens/Utilities/CustomHeader/MenuNavigation'
import hubStyle from '@/screens/Hub/hub.style'
import ProductSearchInput from '@/screens/Filter/FilterHome/ProductSearchInput'
import { nftListing, productListingStyle } from '@/screens/Arts/productListing.style'
import SearchFilter1, { SortByArray } from '@/screens/Filter/SearchFilter1/SearchFilter1'
import CustomActiveIndicator from '@/src/components/CustomLoader/CustomActiveIndicator'
import EmptyContent from '@/screens/Utilities/EmptyContent/EmptyContent'
import NoWishlistIcon from '@/assets/svgs/empty content/noWishlist.svg'
import dpr from '@/screens/Utilities/CustomStyleAttribute/dpr'
import SingleNftView from '@/src/components/SingleNftView/SingleNftView'
import { apiService } from '@/redux/slices/util/apiRequest'
import useAuth from '@/hooks/useAuth'
import HubNFTSleketon from '@/src/skeletons/screens/hub/HubNFTSleketon'
import HubFilters, { FilterRawList } from '@/screens/Filter/Filters/HubFilters'
import { themeColor } from '@/theme/theme'
import CustomBottomSheet from '@/src/components/CustomBottomSheet/CustomBottomSheet'
import selectSingleItemStyle from '@/src/components/SelectItemBottomSheet/selectSingleItem.style'
import { useAppDispatch, useAppSelector } from '@/hooks/reduxHooks'
import {
  setHubAttrfilters,
  setHubFilters,
  setHubUIFilters,
  toggleHubFilterSelection,
} from '@/redux/slices/hub/hubPublicFilter'
import { gapStyle } from '@/screens/Utilities/CommonStyles/gap.style'
import HubFilterTabs from '@/src/components/HubFilterTabs/HubFilterTabs'
import store, { RootState } from '@/redux/store/store'
import { HubSearchFilter } from '@/types/hubDetailState'

interface BottomSheetRef {
  close: () => void
}

interface Filter {
  name: string
  contract_name: string
  target_wallet: string
  sort: string
  limit: string
  order: string
  page: string
}

const URL = `${process.env.EXPO_PUBLIC_BASE_API_URL}/hub/nfts/public-museum`

const initFilter = {
  name: '',
  contract_name: '',
  target_wallet: '',
  sort: 'a.sort_order',
  limit: '12',
  order: 'DESC',
  page: '1',
}

const PublicMuseum = (props: HubBottomTabScreenProps<'PublicMuseum'>) => {
  const { navigation } = props
  const { lng, currency } = useAuth()
  const dispatch = useAppDispatch()
  const { query } = apiService

  const { hubAttrFilters, hubUIFilters } = useAppSelector((state) => state.hubPublicFilterReducer)

  const [products, setProducts] = useState<any>([])
  const [loading, setLoading] = useState(true)
  const [loadMore, setLoadMore] = useState(false)
  const [smGridView, setSmGridView] = useState(false)
  const [pagination, setPagination] = useState({
    total: 0,
    page: 0,
    limit: 0,
  })

  //filter
  const sortByRef = useRef<BottomSheetRef | null>(null)
  const sortByArray = [
    { name: 'Sort By', key: 'a.sort_order', sortOrder: null },
    { name: 'Most Liked', key: 'a.like_total', sortOrder: null },
    { name: 'Recently Created', key: 'a.date_added', sortOrder: null },
  ]
  const [searchText, setSearchText] = useState('')
  const [filterModal, setFilterModal] = useState(false)
  const [sortBy, setSortBy] = useState<SortByArray>(sortByArray[0])
  const [filters, setFilters] = useState(initFilter)

  const selectSingleItemStyles = selectSingleItemStyle()

  const filterParams = (param: Filter) => {
    return new URLSearchParams(
      Object.entries(param).filter(([_, value]) => value !== ''),
    ).toString()
  }

  const handleFetchData = useCallback(
    async (param: Filter) => {
      const queryParam = filterParams(param)
      try {
        setLoading(true)
        const searchFilterObj =
          hubAttrFilters.length > 0 ? `&attributes=${JSON.stringify(hubAttrFilters)}` : {}
        const apiURL =
          queryParam.length > 0
            ? `${URL}?${queryParam}${Object.keys(searchFilterObj).length > 0 ? searchFilterObj : ''}`
            : URL
        const result = await query(apiURL, 'GET', {
          'X-Oc-Merchant-Language': lng,
          'X-Oc-Currency': currency,
        })
        const { data: resultData, success } = result || {}
        if (success === 1) {
          const { data, attributes, pagination: resultPagination } = resultData
          setProducts(data)
          setPagination({
            total: resultPagination.total,
            page: resultPagination.page,
            limit: resultPagination.limit,
          })
          setFilters((prev) => ({ ...prev, page: String(resultPagination?.page) }))
          // setFilterRawList(attributes)
          if (attributes.length > 0) {
            await dispatch(setHubUIFilters(attributes))
          }
          await dispatch(setHubFilters(attributes))
        }
      } catch (error) {
      } finally {
        setLoading(false)
      }
    },
    [filters, hubAttrFilters],
  )

  const handleMoreData = async () => {
    let isMounted = true
    if (isMounted && !loading && pagination.total / Number(pagination.limit) > pagination.page) {
      try {
        setLoadMore(true)
        const tmpFilters = Object.assign({}, filters)
        tmpFilters.page = String(Number(tmpFilters.page) + 1)
        const newURL = new URLSearchParams(
          Object.entries(tmpFilters).filter(([_, value]) => value !== ''),
        ).toString()
        const searchFilterObj =
          hubAttrFilters.length > 0 ? `&attributes=${JSON.stringify(hubAttrFilters)}` : {}
        const apiURL =
          newURL.length > 0
            ? `${URL}?${newURL}${Object.keys(searchFilterObj).length > 0 ? searchFilterObj : ''}`
            : URL

        const result = await query(apiURL, 'GET', {
          'X-Oc-Merchant-Language': lng,
          'X-Oc-Currency': currency,
        })
        const { data: resultData, success } = result || {}
        if (success === 1) {
          const { data, pagination: resultPagination } = resultData
          const tmpProducts = Object.assign([], products)
          setProducts(tmpProducts.concat(data))
          setPagination({
            total: resultPagination.total,
            page: resultPagination.page,
            limit: resultPagination.limit,
          })
          setFilters((prev) => ({ ...prev, page: String(resultPagination.page) }))
        }
      } catch (error) {
        console.error(error)
      } finally {
        setLoadMore(false)
      }
    }
    return () => {
      isMounted = false
    }
  }

  const handleRefresh = async () => {
    const tmpFilters = Object.assign({}, filters)
    tmpFilters.page = '1'
    setFilters((prev) => ({ ...prev, page: '1' }))
    handleFetchData(tmpFilters)
  }

  const handleSortBy = (item: SortByArray) => {
    setSortBy(item)
    const newFilters = {
      ...filters,
      sort: item.key,
      page: '1',
    }
    setFilters(newFilters)
    handleFetchData(newFilters)
  }

  const resetFilter = async () => {
    setFilters(initFilter)
    setSearchText('')
    setFilterModal(false)
    setSortBy(sortByArray[0])
    handleFetchData(initFilter)

    await dispatch(setHubUIFilters([] as FilterRawList[]))
    await dispatch(setHubAttrfilters([] as HubSearchFilter[]))
  }

  const handleSelectItem = async (parentCat: string, id: number) => {
    await dispatch(toggleHubFilterSelection({ parentCat, id }))

    const selectHubUIFilters = (state: RootState) => state.hubPublicFilterReducer.hubUIFilters
    const currentHubUIFilters = selectHubUIFilters(store.getState())

    updateApiFilter(currentHubUIFilters)
  }

  const updateApiFilter = async (currentHubUIFilters: FilterRawList[]) => {
    //compose the filter object (attributes=[{"trait_type":"origin","active":true,"attributes":["Hong Kong"]}])
    const filterSearchObj = [] as HubSearchFilter[]
    currentHubUIFilters.map((item) => {
      item.attributes.map((subItem) => {
        const isCatExist = filterSearchObj.find(
          (filterObj) => filterObj.trait_type === item.trait_type,
        )
        if (subItem.active) {
          if (isCatExist) {
            isCatExist.attributes.push(String(subItem.value))
          } else {
            filterSearchObj.push({
              trait_type: item.trait_type,
              active: item.active,
              attributes: [String(subItem.value)],
            })
          }
        }
      })
    })
    await dispatch(setHubAttrfilters(filterSearchObj))
  }

  useEffect(() => {
    if (hubAttrFilters) {
      const newFilters = {
        ...filters,
        page: '1',
      }
      setFilters(newFilters)
      handleFetchData(newFilters)
    } else {
      handleFetchData(filters)
    }
  }, [hubAttrFilters])

  useEffect(() => {
    const newFilters = {
      ...filters,
      page: '1',
      name: searchText,
    }
    setFilters(newFilters)
    handleFetchData(newFilters)
  }, [searchText])

  return (
    <View style={hubStyle.container}>
      <MenuNavigation navigationProps={navigation} routeName={'Public Museum'.toUpperCase()} />
      <ProductSearchInput isHub={true} searchText={searchText} setSearchText={setSearchText} />
      <View style={gapStyle.mb20}>
        <HubFilterTabs hubUIFilters={hubUIFilters} handleSelectItem={handleSelectItem} />
      </View>
      <View
        style={[
          productListingStyle.filterComponentsContainer,
          { backgroundColor: themeColor.hubBackground },
        ]}>
        <SearchFilter1
          setFilterModal={setFilterModal}
          sortByRef={sortByRef}
          sortBy={sortBy}
          sortByArray={sortByArray}
          listView={smGridView}
          setListView={setSmGridView}
          isHub={true}
        />
      </View>
      <View>
        <Text style={productListingStyle.result}>{`${products?.length || 0} results`}</Text>
      </View>
      <View
        style={[
          productListingStyle.const,
          productListingStyle.py,
          { backgroundColor: themeColor.hubBackground, paddingHorizontal: dpr(0) },
        ]}>
        <View>
          {loading ? (
            <HubNFTSleketon />
          ) : products?.length > 0 ? (
            <FlatList
              key={`grid-${smGridView ? 'single' : 'double'}`}
              data={products}
              keyExtractor={(_, i) => 'key-A' + i}
              renderItem={({ item }) => (
                <SingleNftView
                  item={item}
                  smGridView={smGridView}
                  isSharable={true}
                  canDownload={false}
                  canDownloadImage={false}
                  canDownloadVideo={false}
                  screen="PublicMuseum"
                />
              )}
              showsVerticalScrollIndicator={false}
              numColumns={smGridView ? 3 : 2}
              columnWrapperStyle={nftListing(smGridView ? 10 : 20)}
              refreshing={loading}
              onRefresh={handleRefresh}
              onEndReachedThreshold={0}
              onEndReached={handleMoreData}
            />
          ) : (
            <FlatList
              data={null}
              renderItem={null}
              refreshing={loading}
              onRefresh={handleRefresh}
              showsVerticalScrollIndicator={false}
              ListHeaderComponent={() => (
                <EmptyContent Icon={NoWishlistIcon} text={'No Product Found'} />
              )}
            />
          )}
        </View>
      </View>
      {loadMore && <CustomActiveIndicator />}
      <Modal
        onRequestClose={() => setFilterModal(false)}
        visible={filterModal}
        animationType="slide">
        <HubFilters
          setFilterModal={setFilterModal}
          resetFilter={resetFilter}
          hubUIFilters={hubUIFilters}
          setHubAttrfilters={setHubAttrfilters}
          setHubUIFilters={setHubUIFilters}
          toggleHubFilterSelection={toggleHubFilterSelection}
        />
      </Modal>
      <CustomBottomSheet bsRef={sortByRef} isScrollable={true} isHeaderComponent={false}>
        <FlatList
          data={sortByArray}
          renderItem={({ item }) => (
            <Pressable
              style={selectSingleItemStyles.cont}
              onPress={() => {
                handleSortBy(item)
                sortByRef?.current?.close()
              }}>
              <Text style={selectSingleItemStyles.text}>{item?.name}</Text>
            </Pressable>
          )}
          keyExtractor={(_, key) => `bs${key}`}
        />
      </CustomBottomSheet>
    </View>
  )
}

export default PublicMuseum
