import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { Modal, Pressable, Text, View } from 'react-native'
import { NativeStackScreenProps } from '@react-navigation/native-stack'
import { RootParamList, useFocusEffect } from '@react-navigation/native'
import { FlatList } from 'react-native-gesture-handler'
import hubStyle from '../hub.style'
import { exhibitionListStyle } from './exhibitionList.style'
import { HubSearchFilter } from '@/types/hubDetailState'
import BackNavigation from '@/screens/Utilities/CustomHeader/BackNavigation'
import useLangTranslation from '@/hooks/useLangTranslation'
import { commonStyles } from '@/screens/Utilities/CommonStyles/common.styles'
import { gapStyle } from '@/screens/Utilities/CommonStyles/gap.style'
import ProductSearchInput from '@/screens/Filter/FilterHome/ProductSearchInput'
import ProgressiveImage from '@/src/components/ProgressiveImage'
import { useAppDispatch, useAppSelector } from '@/hooks/reduxHooks'
import {
  getExhibitionDetail,
  getExhibitionHallDetail,
} from '@/redux/slices/exhibition/exhibitionDetail'
import { nftListing, productListingStyle } from '@/screens/Arts/productListing.style'
import { themeColor } from '@/theme/theme'
import SearchFilter1, { SortByArray } from '@/screens/Filter/SearchFilter1/SearchFilter1'
import selectSingleItemStyle from '@/src/components/SelectItemBottomSheet/selectSingleItem.style'
import HubNFTSleketon from '@/src/skeletons/screens/hub/HubNFTSleketon'
import SingleNftView from '@/src/components/SingleNftView/SingleNftView'
import EmptyContent from '@/screens/Utilities/EmptyContent/EmptyContent'
import CustomActiveIndicator from '@/src/components/CustomLoader/CustomActiveIndicator'
import HubFilters, { FilterRawList } from '@/screens/Filter/Filters/HubFilters'
import CustomBottomSheet from '@/src/components/CustomBottomSheet/CustomBottomSheet'
import NoWishlistIcon from '@/assets/svgs/empty content/noWishlist.svg'
import NoFilter from '@/screens/Filter/Filters/NoFilter'
import {
  setHubAttrfilters,
  setHubFilters,
  setHubUIFilters,
  toggleHubFilterSelection,
} from '@/redux/slices/hub/hubExhibitionFilter'
import HubFilterTabs from '@/src/components/HubFilterTabs/HubFilterTabs'
import store, { RootState } from '@/redux/store/store'
import HubExhibitionDetailSleketon from '@/src/skeletons/screens/hub/HubExhibitionDetailSleketon'

type Props = NativeStackScreenProps<RootParamList, 'ExhibitionDetail'>

const URL = `${process.env.EXPO_PUBLIC_BASE_API_URL}/hub/exhibition-hall/data`

interface BottomSheetRef {
  close: () => void
}

interface Filter {
  name: string
  contract_name: string
  target_wallet: string
  sort: string
  limit: string
  order: string
  page: string
}

const ExhibitionDetail = (props: Props) => {
  const { hallId } = props.route.params
  const { trans } = useLangTranslation()
  const dispatch = useAppDispatch()
  const { exhibitionDetail } = useAppSelector((state) => state.exhibitionDetailReducer)
  const { hubAttrFilters, hubUIFilters } = useAppSelector(
    (state) => state.hubExhibitionFilterReducer,
  )
  const [searchText, setSearchText] = useState('')

  const [products, setProducts] = useState<any>([])
  const [loading, setLoading] = useState(false)
  const [loadMore, setLoadMore] = useState(false)
  const [smGridView, setSmGridView] = useState(false)
  const [pagination, setPagination] = useState({
    total: 0,
    page: 0,
    limit: 0,
  })

  //filter
  const initFilter = {
    name: '',
    contract_name: '',
    target_wallet: '',
    sort: 'a.sort_order',
    limit: '12',
    order: 'DESC',
    page: '1',
  }
  const sortByRef = useRef<BottomSheetRef | null>(null)
  const sortByArray = [
    { name: 'Sort By', key: 'a.sort_order', sortOrder: null },
    { name: 'Most Liked', key: 'a.like_total', sortOrder: null },
    { name: 'Recently Created', key: 'a.date_added', sortOrder: null },
  ]
  const [filterModal, setFilterModal] = useState(false)
  const [sortBy, setSortBy] = useState<SortByArray>(sortByArray[0])
  const [filters, setFilters] = useState(initFilter)

  const selectSingleItemStyles = selectSingleItemStyle()

  const filterParams = (param: Filter) => {
    return new URLSearchParams(
      Object.entries(param).filter(([_, value]) => value !== ''),
    ).toString()
  }

  const handleFetchData = useCallback(
    async (param: Filter) => {
      const queryParam = filterParams(param)
      try {
        setLoading(true)
        const searchFilterObj =
          hubAttrFilters.length > 0 ? `&attributes=${JSON.stringify(hubAttrFilters)}` : {}
        const apiURL =
          queryParam.length > 0
            ? `${URL}/${hallId}?${queryParam}${Object.keys(searchFilterObj).length > 0 ? searchFilterObj : ''}`
            : URL

        const result = await dispatch(getExhibitionHallDetail({ url: apiURL }))
        const { data: resultData, success } = result.payload || {}
        if (success === 1) {
          setProducts(resultData?.data)
          setPagination({
            total: resultData?.pagination?.total,
            page: resultData?.pagination?.page,
            limit: resultData?.pagination?.limit,
          })
          setFilters((prev) => ({ ...prev, page: String(resultData?.pagination?.page) }))
          if (resultData?.attributes?.length > 0) {
            await dispatch(setHubUIFilters(resultData?.attributes))
          }
          await dispatch(setHubFilters(resultData?.attributes))
        }
      } catch (error) {
      } finally {
        setLoading(false)
      }
    },
    [filters, hubAttrFilters, hallId],
  )

  const handleMoreData = async () => {
    let isMounted = true
    if (isMounted && !loading && pagination.total / Number(pagination.limit) > pagination.page) {
      try {
        setLoadMore(true)
        const tmpFilters = Object.assign({}, filters)
        tmpFilters.page = String(Number(tmpFilters.page) + 1)
        const newURL = new URLSearchParams(
          Object.entries(tmpFilters).filter(([_, value]) => value !== ''),
        ).toString()
        const searchFilterObj =
          hubAttrFilters.length > 0 ? `&attributes=${JSON.stringify(hubAttrFilters)}` : {}
        const apiURL =
          newURL.length > 0
            ? `${URL}/${hallId}?${newURL}${Object.keys(searchFilterObj).length > 0 ? searchFilterObj : ''}`
            : URL

        const result = await dispatch(getExhibitionHallDetail({ url: apiURL }))
        const { data: resultData, success } = result.payload || {}
        if (success === 1) {
          const tmpProducts = Object.assign([], products)
          setProducts(tmpProducts.concat(resultData?.data))
          setPagination({
            total: resultData?.pagination?.total,
            page: resultData?.pagination?.page,
            limit: resultData?.pagination?.limit,
          })
          setFilters((prev) => ({ ...prev, page: String(pagination?.page) }))
        }
      } catch (error) {
        console.error(error)
      } finally {
        setLoadMore(false)
      }
    }
    return () => {
      isMounted = false
    }
  }

  const handleRefresh = async () => {
    const tmpFilters = Object.assign({}, filters)
    tmpFilters.page = '1'
    setFilters((prev) => ({ ...prev, page: '1' }))
    handleFetchData(tmpFilters)
  }

  const handleSortBy = (item: SortByArray) => {
    setSortBy(item)
    const newFilters = {
      ...filters,
      sort: item.key,
      page: '1',
    }
    setFilters(newFilters)
    handleFetchData(newFilters)
  }

  const resetFilter = async () => {
    setFilters(initFilter)
    setSearchText('')
    setFilterModal(false)
    setSortBy(sortByArray[0])
    handleFetchData(initFilter)

    await dispatch(setHubUIFilters([] as FilterRawList[]))
    await dispatch(setHubAttrfilters([] as HubSearchFilter[]))
  }

  const getInitData = async () => {
    await dispatch(getExhibitionDetail({ hallId }))
    handleFetchData(filters)
  }

  const handleSelectItem = async (parentCat: string, id: number) => {
    await dispatch(toggleHubFilterSelection({ parentCat, id }))

    const selectHubUIFilters = (state: RootState) => state.hubExhibitionFilterReducer.hubUIFilters
    const currentHubUIFilters = selectHubUIFilters(store.getState())

    updateApiFilter(currentHubUIFilters)
  }

  const updateApiFilter = async (currentHubUIFilters: FilterRawList[]) => {
    //compose the filter object (attributes=[{"trait_type":"origin","active":true,"attributes":["Hong Kong"]}])
    const filterSearchObj = [] as HubSearchFilter[]
    currentHubUIFilters.map((item) => {
      item.attributes.map((subItem) => {
        const isCatExist = filterSearchObj.find(
          (filterObj) => filterObj.trait_type === item.trait_type,
        )
        if (subItem.active) {
          if (isCatExist) {
            isCatExist.attributes.push(String(subItem.value))
          } else {
            filterSearchObj.push({
              trait_type: item.trait_type,
              active: item.active,
              attributes: [String(subItem.value)],
            })
          }
        }
      })
    })
    await dispatch(setHubAttrfilters(filterSearchObj))
  }

  useFocusEffect(
    useCallback(() => {
      getInitData()
    }, []),
  )

  useEffect(() => {
    return () => {
      resetFilter()
    }
  }, [hallId])

  useEffect(() => {
    if (hubAttrFilters) {
      const newFilters = {
        ...filters,
        page: '1',
      }
      setFilters(newFilters)
      handleFetchData(newFilters)
    } else {
      handleFetchData(filters)
    }
  }, [hubAttrFilters])

  useEffect(() => {
    const newFilters = {
      ...filters,
      page: '1',
      name: searchText,
    }
    setFilters(newFilters)
    handleFetchData(newFilters)
  }, [searchText])

  const HeaderInfo = useMemo(() => {
    return (
      <View style={gapStyle.mb20}>
        <View style={exhibitionListStyle.item}>
          <View style={exhibitionListStyle.imageContainer}>
            <View style={exhibitionListStyle.detailAvatarContainer}>
              <ProgressiveImage
                source={{ uri: exhibitionDetail?.avatar }}
                style={exhibitionListStyle.avatar}
              />
            </View>
            <Text style={[exhibitionListStyle.itemTitle, exhibitionListStyle.hubOwner]}>
              {exhibitionDetail.hub_customer_name}
            </Text>
            <ProgressiveImage
              source={{ uri: exhibitionDetail?.top_banner }}
              style={exhibitionListStyle.posterImg}
            />
          </View>
          <View style={exhibitionListStyle.textSection}>
            <Text style={exhibitionListStyle.itemTitle}>{exhibitionDetail?.name}</Text>
            <Text style={exhibitionListStyle.itemDate}>
              {exhibitionDetail?.description?.trim().replace(/\r\n\r\n/g, '') || ''}
            </Text>
          </View>
        </View>
        <ProductSearchInput isHub={true} searchText={searchText} setSearchText={setSearchText} />
        <View style={gapStyle.mb20}>
          <HubFilterTabs hubUIFilters={hubUIFilters} handleSelectItem={handleSelectItem} />
        </View>
        <View
          style={[
            productListingStyle.filterComponentsContainer,
            { backgroundColor: themeColor.hubBackground },
          ]}>
          <SearchFilter1
            setFilterModal={setFilterModal}
            sortByRef={sortByRef}
            sortBy={sortBy}
            sortByArray={sortByArray}
            listView={smGridView}
            setListView={setSmGridView}
            isHub={true}
          />
        </View>
        <View>
          <Text style={productListingStyle.result}>{`${products?.length ?? 0} results`}</Text>
        </View>
      </View>
    )
  }, [exhibitionDetail, products])

  return (
    <View style={hubStyle.containerFull}>
      <BackNavigation
        navigationProps={props.navigation}
        routeName={trans('Exhibition Hall').toUpperCase()}
        capitalize={false}
        hubRouting={true}
        shareLink={exhibitionDetail?.share_link || ''}
      />
      <View style={[commonStyles.hrLineFull, gapStyle.mt0]} />
      <View
        style={[
          productListingStyle.const,
          productListingStyle.py,
          { backgroundColor: themeColor.hubBackground },
        ]}>
        <View>
          {loading ? (
            <HubExhibitionDetailSleketon />
          ) : products?.length > 0 ? (
            <FlatList
              key={`grid-${smGridView ? 'single' : 'double'}`}
              data={products}
              keyExtractor={(_, i) => 'key-A' + i}
              renderItem={({ item }) => (
                <SingleNftView
                  item={item}
                  smGridView={smGridView}
                  isSharable={true}
                  canDownload={true}
                  canDownloadImage={item?.is_show_image}
                  canDownloadVideo={item?.is_show_video}
                  screen="ExhibitionDetail"
                  exhibitionHallId={hallId}
                />
              )}
              showsVerticalScrollIndicator={false}
              numColumns={smGridView ? 3 : 2}
              columnWrapperStyle={nftListing(smGridView ? 10 : 20)}
              refreshing={loading}
              onRefresh={handleRefresh}
              onEndReachedThreshold={0}
              onEndReached={handleMoreData}
              ListHeaderComponent={HeaderInfo}
              ListFooterComponentStyle={gapStyle.pb30}
            />
          ) : (
            <FlatList
              data={null}
              renderItem={null}
              refreshing={loading}
              onRefresh={handleRefresh}
              showsVerticalScrollIndicator={false}
              ListHeaderComponent={HeaderInfo}
              ListFooterComponent={<EmptyContent Icon={NoWishlistIcon} text={'No Product Found'} />}
              ListFooterComponentStyle={gapStyle.pb30}
            />
          )}
        </View>
        {loadMore && <CustomActiveIndicator />}
        <Modal
          onRequestClose={() => setFilterModal(false)}
          visible={filterModal}
          animationType="slide">
          {hubUIFilters.length > 0 ? (
            <HubFilters
              setFilterModal={setFilterModal}
              resetFilter={resetFilter}
              hubUIFilters={hubUIFilters}
              setHubAttrfilters={setHubAttrfilters}
              setHubUIFilters={setHubUIFilters}
              toggleHubFilterSelection={toggleHubFilterSelection}
            />
          ) : (
            <NoFilter setFilterModal={setFilterModal} />
          )}
        </Modal>
        <CustomBottomSheet bsRef={sortByRef} isScrollable={true} isHeaderComponent={false}>
          <FlatList
            data={sortByArray}
            renderItem={({ item }) => (
              <Pressable
                style={selectSingleItemStyles.cont}
                onPress={() => {
                  handleSortBy(item)
                  sortByRef?.current?.close()
                }}>
                <Text style={selectSingleItemStyles.text}>{item?.name}</Text>
              </Pressable>
            )}
            keyExtractor={(_, key) => `bs${key}`}
          />
        </CustomBottomSheet>
      </View>
    </View>
  )
}

export default ExhibitionDetail
