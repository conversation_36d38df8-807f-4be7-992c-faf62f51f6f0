import React, { useCallback, useEffect, useState } from 'react'
import { FlatList, View } from 'react-native'
import { NativeStackScreenProps } from '@react-navigation/native-stack'
import { RootParamList } from '@react-navigation/native'
import HallPoster from './HallPoster'
import { exhibitionListStyle } from './exhibitionList.style'
import hubStyle from '@/screens/Hub/hub.style'
import ProductSearchInput from '@/screens/Filter/FilterHome/ProductSearchInput'
import { productListingStyle } from '@/screens/Arts/productListing.style'
import CustomActiveIndicator from '@/src/components/CustomLoader/CustomActiveIndicator'
import EmptyContent from '@/screens/Utilities/EmptyContent/EmptyContent'
import NoWishlistIcon from '@/assets/svgs/empty content/noWishlist.svg'
import dpr from '@/screens/Utilities/CustomStyleAttribute/dpr'
import { apiService } from '@/redux/slices/util/apiRequest'
import useAuth from '@/hooks/useAuth'
import HubNFTSleketon from '@/src/skeletons/screens/hub/HubNFTSleketon'
import { themeColor } from '@/theme/theme'
import BackNavigation from '@/screens/Utilities/CustomHeader/BackNavigation'
import useLangTranslation from '@/hooks/useLangTranslation'
import { commonStyles } from '@/screens/Utilities/CommonStyles/common.styles'
import { gapStyle } from '@/screens/Utilities/CommonStyles/gap.style'
import { ExhibitionList } from '@/types/exhibition'
import { HubBottomTabScreenProps } from '@/types/navigations'
import MenuNavigation from '@/screens/Utilities/CustomHeader/MenuNavigation'

type Props = HubBottomTabScreenProps<'ExhibitionHall'>

interface Filter {
  name: string
  limit: string
  order: string
  page: string
}

const URL = `${process.env.EXPO_PUBLIC_BASE_API_URL}/hub/exhibition-hall`

const initFilter = {
  name: '',
  limit: '12',
  order: 'DESC',
  page: '1',
}

const ExhibitionHall = (props: Props) => {
  const { navigation } = props
  const { lng, currency } = useAuth()
  const { trans } = useLangTranslation()
  const { query } = apiService

  const [products, setProducts] = useState<any>([])
  const [loading, setLoading] = useState(true)
  const [loadMore, setLoadMore] = useState(false)
  const [pagination, setPagination] = useState({
    total: 0,
    page: 0,
    limit: '',
  })

  const [searchText, setSearchText] = useState('')
  const [filters, setFilters] = useState(initFilter)

  const filterParams = (param: Filter) => {
    return new URLSearchParams(
      Object.entries(param).filter(([_, value]) => value !== ''),
    ).toString()
  }

  const handleFetchData = useCallback(
    async (param: Filter) => {
      const queryParam = filterParams(param)
      try {
        setLoading(true)

        const apiURL = queryParam.length > 0 ? `${URL}?${queryParam}` : URL
        const result = await query(apiURL, 'GET', {
          'X-Oc-Merchant-Language': lng,
          'X-Oc-Currency': currency,
        })
        const { data: resultData, success } = result || {}
        if (success === 1) {
          const { data, pagination: resultPagination } = resultData as ExhibitionList
          setProducts(data)
          setPagination({
            total: resultPagination.total,
            page: resultPagination.page,
            limit: resultPagination.limit,
          })
          setFilters((prev) => ({ ...prev, page: String(resultPagination?.page) }))
        }
      } catch (error) {
      } finally {
        setLoading(false)
      }
    },
    [filters],
  )

  const handleMoreData = async () => {
    let isMounted = true
    if (isMounted && !loading && pagination.total / Number(pagination.limit) > pagination.page) {
      try {
        setLoadMore(true)
        const tmpFilters = Object.assign({}, filters)
        tmpFilters.page = String(Number(tmpFilters.page) + 1)
        const newURL = new URLSearchParams(
          Object.entries(tmpFilters).filter(([_, value]) => value !== ''),
        ).toString()

        const apiURL = newURL.length > 0 ? `${URL}?${newURL}` : URL

        const result = await query(apiURL, 'GET', {
          'X-Oc-Merchant-Language': lng,
          'X-Oc-Currency': currency,
        })
        const { data: resultData, success } = result || {}
        if (success === 1) {
          const { data, pagination: resultPagination } = resultData as ExhibitionList
          const tmpProducts = Object.assign([], products)
          setProducts(tmpProducts.concat(data))
          setPagination({
            total: resultPagination.total,
            page: resultPagination.page,
            limit: resultPagination.limit,
          })
          setFilters((prev) => ({ ...prev, page: String(resultPagination.page) }))
        }
      } catch (error) {
        console.error(error)
      } finally {
        setLoadMore(false)
      }
    }
    return () => {
      isMounted = false
    }
  }

  const handleRefresh = async () => {
    const tmpFilters = Object.assign({}, filters)
    tmpFilters.page = '1'
    setFilters((prev) => ({ ...prev, page: '1' }))
    handleFetchData(tmpFilters)
  }

  useEffect(() => {
    const newFilters = {
      ...filters,
      page: '1',
      name: searchText,
    }
    setFilters(newFilters)
    handleFetchData(newFilters)
  }, [searchText])

  return (
    <View style={hubStyle.containerFull}>
      <View style={commonStyles.paddingSection}>
        <MenuNavigation
          navigationProps={props.navigation}
          routeName={trans('Exhibition Hall').toUpperCase()}
        />
      </View>
      <View style={commonStyles.paddingSection}>
        <ProductSearchInput isHub={true} searchText={searchText} setSearchText={setSearchText} />
      </View>
      <View style={hubStyle.container}>
        <View
          style={[
            productListingStyle.const,
            { backgroundColor: themeColor.hubBackground, paddingHorizontal: dpr(0) },
          ]}>
          <View>
            {loading ? (
              <HubNFTSleketon />
            ) : products?.length > 0 ? (
              <FlatList
                data={products}
                keyExtractor={(_, i) => 'key-A' + i}
                renderItem={({ item }) => <HallPoster item={item} navigation={navigation} />}
                showsVerticalScrollIndicator={false}
                numColumns={1}
                initialNumToRender={4}
                refreshing={loading}
                onRefresh={handleRefresh}
                onEndReachedThreshold={0}
                onEndReached={handleMoreData}
                contentContainerStyle={exhibitionListStyle.listColumnWrapper}
              />
            ) : (
              <FlatList
                data={null}
                renderItem={null}
                refreshing={loading}
                onRefresh={handleRefresh}
                showsVerticalScrollIndicator={false}
                ListHeaderComponent={() => (
                  <EmptyContent Icon={NoWishlistIcon} text={'No Product Found'} />
                )}
              />
            )}
          </View>
        </View>
        {loadMore && <CustomActiveIndicator />}
      </View>
    </View>
  )
}

export default ExhibitionHall
