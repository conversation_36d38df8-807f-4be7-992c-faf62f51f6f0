import React, { useCallback, useEffect, useRef, useState } from 'react'
import { FlatList, Modal, Pressable, Text, View } from 'react-native'
import { useAccount } from 'wagmi'
import BottomSheet from '@gorhom/bottom-sheet'
import ConnectionPending from '../PrivateMuseum/ConnectionPending'
import { HubBottomTabScreenProps } from '@/types/navigations'
import MenuNavigation from '@/screens/Utilities/CustomHeader/MenuNavigation'
import hubStyle from '@/screens/Hub/hub.style'
import { themeColor } from '@/theme/theme'
import ProductSearchInput from '@/screens/Filter/FilterHome/ProductSearchInput'
import { nftListing, productListingStyle } from '@/screens/Arts/productListing.style'
import HubNFTSleketon from '@/src/skeletons/screens/hub/HubNFTSleketon'
import CustomActiveIndicator from '@/src/components/CustomLoader/CustomActiveIndicator'
import CustomBottomSheet from '@/src/components/CustomBottomSheet/CustomBottomSheet'
import { apiService } from '@/redux/slices/util/apiRequest'
import useAuth from '@/hooks/useAuth'
import SearchFilter1, { SortByArray } from '@/screens/Filter/SearchFilter1/SearchFilter1'
import selectSingleItemStyle from '@/src/components/SelectItemBottomSheet/selectSingleItem.style'
import dpr from '@/screens/Utilities/CustomStyleAttribute/dpr'
import HubFilters, { FilterRawList } from '@/screens/Filter/Filters/HubFilters'
import SingleNftView from '@/src/components/SingleNftView/SingleNftView'
import EmptyContent from '@/screens/Utilities/EmptyContent/EmptyContent'
import NoWishlistIcon from '@/assets/svgs/empty content/noWishlist.svg'
import { useAppDispatch, useAppSelector } from '@/hooks/reduxHooks'
import {
  setHubAttrfilters,
  setHubUIFilters,
  toggleHubFilterSelection,
} from '@/redux/slices/hub/hubShowroomFilter'
import { setHubFilters } from '@/redux/slices/hub/hubPrivateFilter'
import HubFilterTabs from '@/src/components/HubFilterTabs/HubFilterTabs'
import { gapStyle } from '@/screens/Utilities/CommonStyles/gap.style'
import store, { RootState } from '@/redux/store/store'

export interface HubSearchFilter {
  trait_type: string
  active: boolean
  attributes: string[]
}

interface Filter {
  name: string
  contract_name: string
  target_wallet: string
  sort: string
  limit: string
  order: string
  page: string
}

const URL = `${process.env.EXPO_PUBLIC_BASE_API_URL}/hub/nfts/owner`

const initFilter = {
  name: '',
  contract_name: '',
  target_wallet: '',
  sort: 'a.sort_order',
  limit: '12',
  order: 'DESC',
  page: '1',
}

const Showroom = (props: HubBottomTabScreenProps<'Showroom'>) => {
  const { isConnected, address } = useAccount()
  const { walletToken } = useAuth()
  const { walletQuery } = apiService
  const dispatch = useAppDispatch()

  const { hubAttrFilters, hubUIFilters } = useAppSelector((state) => state.hubShowroomFilterReducer)

  const [products, setProducts] = useState<any>([])
  const [loading, setLoading] = useState(true)
  const [loadMore, setLoadMore] = useState(false)
  const [smGridView, setSmGridView] = useState(false)
  const [pagination, setPagination] = useState({
    total: 0,
    page: 0,
    limit: 0,
  })

  //filter
  const sortByRef = useRef<BottomSheet | null>(null)
  const sortByArray = [
    { name: 'Sort By', key: 'a.sort_order', sortOrder: null },
    { name: 'Most Liked', key: 'a.like_total', sortOrder: null },
    { name: 'Recently Created', key: 'a.date_added', sortOrder: null },
  ]
  const [searchText, setSearchText] = useState('')
  const [filterModal, setFilterModal] = useState(false)
  const [sortBy, setSortBy] = useState<SortByArray>(sortByArray[0])
  const [filters, setFilters] = useState(initFilter)
  const [refreshConnectionPending, setRefreshConnectionPending] = useState(false)

  //wallet connect ref
  const selectSingleItemStyles = selectSingleItemStyle()

  const filterParams = (param: Filter) => {
    return new URLSearchParams(
      Object.entries(param).filter(([_, value]) => value !== ''),
    ).toString()
  }

  const handleFetchData = useCallback(
    async (param: Filter) => {
      if (!walletToken) {
        return
      }

      const queryParam = filterParams(param)
      try {
        setLoading(true)
        const searchFilterObj =
          hubAttrFilters.length > 0 ? `&attributes=${JSON.stringify(hubAttrFilters)}` : {}
        const apiURL =
          queryParam.length > 0
            ? `${URL}/${address}?${queryParam}${Object.keys(searchFilterObj).length > 0 ? searchFilterObj : ''}`
            : `${URL}/${address}`
        const result = await walletQuery(apiURL, 'GET', {
          'X-Wallet-Token': walletToken,
        })
        const { data: resultData, success } = result || {}
        if (success === 1) {
          const { data, attributes, pagination: resultPagination } = resultData
          setProducts(data)
          setPagination({
            total: resultPagination.total,
            page: resultPagination.page,
            limit: resultPagination.limit,
          })
          setFilters((prev) => ({ ...prev, page: String(resultPagination.page) }))

          if (attributes.length > 0) {
            await dispatch(setHubUIFilters(attributes))
          }
          await dispatch(setHubFilters(attributes))
        }
      } catch (error) {
      } finally {
        setLoading(false)
      }
    },
    [filters, address, walletToken, hubAttrFilters],
  )

  const handleMoreData = async () => {
    let isMounted = true
    if (isMounted && !loading && pagination.total / Number(pagination.limit) > pagination.page) {
      try {
        setLoadMore(true)
        const tmpFilters = Object.assign({}, filters)
        tmpFilters.page = String(Number(tmpFilters.page) + 1)
        const newURL = new URLSearchParams(
          Object.entries(tmpFilters).filter(([_, value]) => value !== ''),
        ).toString()
        const searchFilterObj =
          hubAttrFilters.length > 0 ? `&attributes=${JSON.stringify(hubAttrFilters)}` : {}
        const apiURL =
          newURL.length > 0
            ? `${URL}/${address}?${newURL}${Object.keys(searchFilterObj).length > 0 ? searchFilterObj : ''}`
            : `${URL}/${address}`

        const result = await walletQuery(apiURL, 'GET', {
          'X-Wallet-Token': walletToken,
        })
        const { data: resultData, attributes, success } = result || {}
        if (success === 1) {
          const { data, page, pagination: resultPagination } = resultData
          const tmpProducts = Object.assign([], products)
          setProducts(tmpProducts.concat(data))
          setPagination({
            total: resultPagination.total,
            page: resultPagination.page,
            limit: resultPagination.limit,
          })
          setFilters((prev) => ({ ...prev, page: String(page) }))
        }
      } catch (error) {
        console.error(error)
      } finally {
        setLoadMore(false)
      }
    }
    return () => {
      isMounted = false
    }
  }

  const handleRefresh = async () => {
    const tmpFilters = Object.assign({}, filters)
    tmpFilters.page = '1'
    setFilters((prev) => ({ ...prev, page: '1' }))
    handleFetchData(tmpFilters)
  }

  const handleSortBy = (item: SortByArray) => {
    setSortBy(item)
    const newFilters = {
      ...filters,
      sort: item.key,
      page: '1',
    }
    setFilters(newFilters)
    handleFetchData(newFilters)
  }

  const resetFilter = async () => {
    setFilters(initFilter)
    setSearchText('')
    setFilterModal(false)
    setSortBy(sortByArray[0])
    handleFetchData(initFilter)

    await dispatch(setHubUIFilters([] as FilterRawList[]))
    await dispatch(setHubAttrfilters([] as HubSearchFilter[]))
  }

  const handleSelectItem = async (parentCat: string, id: number) => {
    await dispatch(toggleHubFilterSelection({ parentCat, id }))

    const selectHubUIFilters = (state: RootState) => state.hubShowroomFilterReducer.hubUIFilters
    const currentHubUIFilters = selectHubUIFilters(store.getState())

    updateApiFilter(currentHubUIFilters)
  }

  const updateApiFilter = async (currentHubUIFilters: FilterRawList[]) => {
    //compose the filter object (attributes=[{"trait_type":"origin","active":true,"attributes":["Hong Kong"]}])
    const filterSearchObj = [] as HubSearchFilter[]
    currentHubUIFilters.map((item) => {
      item.attributes.map((subItem) => {
        const isCatExist = filterSearchObj.find(
          (filterObj) => filterObj.trait_type === item.trait_type,
        )
        if (subItem.active) {
          if (isCatExist) {
            isCatExist.attributes.push(String(subItem.value))
          } else {
            filterSearchObj.push({
              trait_type: item.trait_type,
              active: item.active,
              attributes: [String(subItem.value)],
            })
          }
        }
      })
    })
    await dispatch(setHubAttrfilters(filterSearchObj))
  }

  useEffect(() => {
    if (hubAttrFilters) {
      const newFilters = {
        ...filters,
        page: '1',
      }
      setFilters(newFilters)
      handleFetchData(newFilters)
    } else {
      handleFetchData(filters)
    }
  }, [hubAttrFilters])

  useEffect(() => {
    const newFilters = {
      ...filters,
      page: '1',
      name: searchText,
    }
    setFilters(newFilters)
    handleFetchData(newFilters)
  }, [searchText])

  useEffect(() => {
    if (isConnected && walletToken) {
      handleFetchData(filters)
    }

    if (!isConnected || !walletToken) {
      setRefreshConnectionPending(true)
    } else {
      setRefreshConnectionPending(false)
    }
  }, [isConnected, walletToken])

  if (!isConnected || !walletToken) {
    return <ConnectionPending headerName="Showroom" key={refreshConnectionPending ? 1 : 0} />
  }

  return (
    <View style={{ backgroundColor: themeColor.hubBackground, flex: 1 }}>
      <View style={hubStyle.container}>
        <MenuNavigation navigationProps={props.navigation} routeName={'Showroom'.toUpperCase()} />
        <ProductSearchInput isHub={true} searchText={searchText} setSearchText={setSearchText} />
        <View style={gapStyle.mb20}>
          <HubFilterTabs hubUIFilters={hubUIFilters} handleSelectItem={handleSelectItem} />
        </View>
        <View
          style={[
            productListingStyle.filterComponentsContainer,
            { backgroundColor: themeColor.hubBackground },
          ]}>
          <SearchFilter1
            setFilterModal={setFilterModal}
            sortByRef={sortByRef}
            sortBy={sortBy}
            sortByArray={sortByArray}
            listView={smGridView}
            setListView={setSmGridView}
            isHub={true}
          />
        </View>
        <View>
          <Text style={productListingStyle.result}>{`${products?.length ?? 0} results`}</Text>
        </View>
        <View
          style={[
            productListingStyle.const,
            productListingStyle.py,
            { backgroundColor: themeColor.hubBackground, paddingHorizontal: dpr(0) },
          ]}>
          <View>
            {loading ? (
              <HubNFTSleketon />
            ) : products?.length > 0 ? (
              <FlatList
                key={`grid-${smGridView ? 'single' : 'double'}`}
                data={products}
                keyExtractor={(_, i) => 'key-A' + i}
                renderItem={({ item }) => (
                  <SingleNftView
                    item={item}
                    smGridView={smGridView}
                    isSharable={false}
                    isShowRoom={true}
                    canDownload={true}
                    canDownloadImage={true}
                    canDownloadVideo={true}
                    screen="Showroom"
                  />
                )}
                showsVerticalScrollIndicator={false}
                numColumns={smGridView ? 3 : 2}
                columnWrapperStyle={nftListing(smGridView ? 10 : 20)}
                refreshing={loading}
                onRefresh={handleRefresh}
                onEndReachedThreshold={0}
                onEndReached={handleMoreData}
              />
            ) : (
              <FlatList
                data={null}
                renderItem={null}
                refreshing={loading}
                onRefresh={handleRefresh}
                showsVerticalScrollIndicator={false}
                ListHeaderComponent={() => (
                  <EmptyContent Icon={NoWishlistIcon} text={'No Product Found'} />
                )}
              />
            )}
          </View>
        </View>
        {loadMore && <CustomActiveIndicator />}
        <Modal
          onRequestClose={() => setFilterModal(false)}
          visible={filterModal}
          animationType="slide">
          <HubFilters
            setFilterModal={setFilterModal}
            resetFilter={resetFilter}
            hubUIFilters={hubUIFilters}
            setHubAttrfilters={setHubAttrfilters}
            setHubUIFilters={setHubUIFilters}
            toggleHubFilterSelection={toggleHubFilterSelection}
          />
        </Modal>
        <CustomBottomSheet bsRef={sortByRef} isScrollable={true} isHeaderComponent={false}>
          <FlatList
            data={sortByArray}
            renderItem={({ item }) => (
              <Pressable
                style={selectSingleItemStyles.cont}
                onPress={() => {
                  handleSortBy(item)
                  sortByRef?.current?.close()
                }}>
                <Text style={selectSingleItemStyles.text}>{item?.name}</Text>
              </Pressable>
            )}
            keyExtractor={(_, key) => `bs${key}`}
          />
        </CustomBottomSheet>
      </View>
    </View>
  )
}

export default Showroom
