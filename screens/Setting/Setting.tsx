import React from 'react'
import { View, Text, Pressable } from 'react-native'
import { NativeStackScreenProps } from '@react-navigation/native-stack'
import { ScrollView } from 'react-native-gesture-handler'
import { settingStyle } from './setting.style'
import BackNavigation from '../Utilities/CustomHeader/BackNavigation'
import useLangTranslation from '@/hooks/useLangTranslation'
import CustomSVG from '@/src/components/CustomSVG/CustomSVG'
import RightIcon from '@/assets/svgs/settings/right_arrow.svg'
import LanguageIcon from '@/assets/svgs/settings/language.svg'
import CurrencyIcon from '@/assets/svgs/settings/currency.svg'
import FaqIcon from '@/assets/svgs/settings/faqs.svg'
import privacy from '@/assets/svgs/settings/privacy.svg'
import TncIcon from '@/assets/svgs/settings/t&c.svg'
import PndIcon from '@/assets/svgs/settings/payment.svg'
import NotiIcon from '@/assets/svgs/settings/notification.svg'
import dpr from '@/screens/Utilities/CustomStyleAttribute/dpr'
import { RootStackParamList } from '@/types/navigations'
import { useAppSelector } from '@/hooks/reduxHooks'

type Props = NativeStackScreenProps<RootStackParamList, 'Setting'>

const iconSize = dpr(16)

const Item = ({ onPress, icon, name }: { onPress: () => void; icon: any; name: string }) => (
  <Pressable android_ripple={{ color: 'gray' }} onPress={onPress} style={settingStyle.itemBox}>
    <View style={settingStyle.titleItem}>
      <CustomSVG svgIcon={icon} width={iconSize} height={iconSize} />
      <Text style={settingStyle.title}>{name}</Text>
    </View>
    <CustomSVG svgIcon={RightIcon} isRtl={1} />
  </Pressable>
)

const Setting = (props: Props) => {
  const { trans } = useLangTranslation()
  const { user } = useAppSelector((state) => state.signInReducer)

  return (
    <View style={settingStyle.settingRoot}>
      <BackNavigation
        navigationProps={props.navigation}
        routeName={trans('settings')}
        capitalize={false}
      />
      <View style={settingStyle.hrLine} />
      <View style={settingStyle.container}>
        <ScrollView showsVerticalScrollIndicator={false} keyboardShouldPersistTaps="always">
          <View style={settingStyle.section}>
            <Text style={settingStyle.sectionTitle}>{trans('My Account')}</Text>
            <Item
              onPress={() => props.navigation.navigate('SelectLanguage')}
              icon={LanguageIcon}
              name={trans('Language')}
            />
            <Item
              onPress={() => props.navigation.navigate('SelectCurrency')}
              icon={CurrencyIcon}
              name={trans('Currency')}
            />
            {user?.customer_id && (
              <Item
                onPress={() => props.navigation.navigate('NotificationSetting')}
                icon={NotiIcon}
                name={trans('Notification')}
              />
            )}
          </View>

          <View style={settingStyle.section}>
            <Text style={settingStyle.sectionTitle}>{trans('Legal')}</Text>
            <Item
              onPress={() => props.navigation.navigate('Faqs')}
              icon={FaqIcon}
              name={trans('FAQs')}
            />
            <Item
              onPress={() => props.navigation.navigate('PrivacyPolicy')}
              icon={privacy}
              name={trans('Privacy Policy')}
            />
            <Item
              onPress={() => props.navigation.navigate('TermsAndConditions')}
              icon={TncIcon}
              name={trans('Terms & Conditions')}
            />
            <Item
              onPress={() => props.navigation.navigate('PaymentAndDelivery')}
              icon={PndIcon}
              name={trans('Payment & Delivery')}
            />
          </View>
        </ScrollView>
      </View>
    </View>
  )
}

export default Setting
