import React, { useState, useEffect } from 'react'
import { FlatList, View, Switch, Text } from 'react-native'
import { NativeStackScreenProps } from '@react-navigation/native-stack'
import { settingStyle } from './setting.style'
import useLangTranslation from '@/hooks/useLangTranslation'
import BackNavigation from '@/screens/Utilities/CustomHeader/BackNavigation'
import { RootStackParamList } from '@/types/navigations'
import { useAppDispatch, useAppSelector } from '@/hooks/reduxHooks'
import { getPushNotify, setPushNotify } from '@/redux/slices/pushNotification/pushNotification'

interface NotificationSettingProps {
  navigation: NativeStackScreenProps<RootStackParamList, 'NotificationSetting'>
}

const NotificationSetting = (props: NotificationSettingProps) => {
  const { trans } = useLangTranslation()
  const dispatch = useAppDispatch()
  const { is_push_enabled } = useAppSelector((state) => state.pushNotificationReducer)

  useEffect(() => {
    ;(async () => {
      await dispatch(getPushNotify())
    })()
  }, [dispatch])

  const noNotifyChange = async (value: boolean) => {
    await dispatch(setPushNotify(Number(value)))
  }

  return (
    <View style={settingStyle.settingRoot}>
      <BackNavigation
        navigationProps={props.navigation}
        routeName={trans('notification setting')}
        capitalize={false}
      />
      <View style={settingStyle.hrLine} />
      <View style={[settingStyle.container, settingStyle.flexListItem]}>
        <Text style={settingStyle.flexListTitle}>{trans('receive updates on each bid')}</Text>
        <Switch onValueChange={noNotifyChange} value={is_push_enabled} />
      </View>
    </View>
  )
}

export default NotificationSetting
