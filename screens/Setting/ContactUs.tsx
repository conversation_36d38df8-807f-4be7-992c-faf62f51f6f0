import React, { useRef } from 'react'
import { View, Text, Pressable } from 'react-native'
import { NativeStackScreenProps } from '@react-navigation/native-stack'
import BottomSheet from '@gorhom/bottom-sheet'
import * as Linking from 'expo-linking'
import { Trans } from 'react-i18next'
import { settingStyle } from './setting.style'
import BackNavigation from '../Utilities/CustomHeader/BackNavigation'
import useLangTranslation from '@/hooks/useLangTranslation'
import CustomSVG from '@/src/components/CustomSVG/CustomSVG'
import RightIcon from '@/assets/svgs/settings/right_arrow.svg'
import PhoneIcon from '@/assets/svgs/settings/phone.svg'
import EmailIcon from '@/assets/svgs/settings/email.svg'
import AddressIcon from '@/assets/svgs/settings/address.svg'
import EnquiryIcon from '@/assets/svgs/profile/edit profile.svg'
import dpr from '@/screens/Utilities/CustomStyleAttribute/dpr'
import { RootStackParamList } from '@/types/navigations'
import CustomBottomSheet from '@/src/components/CustomBottomSheet/CustomBottomSheet'
import { buttonStyles } from '@/screens/Utilities/CommonStyles/common.styles'

type Props = NativeStackScreenProps<RootStackParamList, 'ContactUs'>

const iconSize = dpr(16)
const buttonStyle = buttonStyles()

const Item = ({ onPress, icon, name }: { onPress: () => void; icon: any; name: string }) => (
  <Pressable android_ripple={{ color: 'gray' }} onPress={onPress} style={settingStyle.itemBox}>
    <View style={settingStyle.titleItem}>
      <CustomSVG svgIcon={icon} width={iconSize} height={iconSize} />
      <Text style={settingStyle.title}>{name}</Text>
    </View>
    <CustomSVG svgIcon={RightIcon} isRtl={1} />
  </Pressable>
)

const ContactUs = (props: Props) => {
  const { trans } = useLangTranslation()
  const phoneRef = useRef<BottomSheet>(null)
  const emailRef = useRef<BottomSheet>(null)

  const handleContact = async (link: string) => {
    await Linking.openURL(link)
  }

  return (
    <View style={settingStyle.settingRoot}>
      <BackNavigation
        navigationProps={props.navigation}
        routeName={trans('Contact Us')}
        capitalize={false}
      />
      <View style={settingStyle.hrLine} />
      <View style={[settingStyle.container, { flex: 1 }]}>
        <View style={settingStyle.section}>
          <Text style={settingStyle.sectionTitle}>{trans('Contact Us')}</Text>
          <Item
            onPress={() => phoneRef?.current?.snapToIndex(0)}
            icon={PhoneIcon}
            name={trans('Phone Number')}
          />
          <Item
            onPress={() => emailRef?.current?.snapToIndex(0)}
            icon={EmailIcon}
            name={trans('Email Address')}
          />
          <Item
            onPress={() => props.navigation.navigate('Map')}
            icon={AddressIcon}
            name={trans('Address')}
          />
          <Item
            onPress={() => props.navigation.navigate('EnquiryForm')}
            icon={EnquiryIcon}
            name={trans('Enquiry Form')}
          />
        </View>
        <CustomBottomSheet bsRef={phoneRef} isScrollable={true} isHeaderComponent={false}>
          <View style={settingStyle.contactUs}>
            <Text style={settingStyle.contactUsText}>
              <Trans i18nKey="contactTMRBy">
                Contact <Text style={settingStyle.brandName}>TMR ARTS LIMITED</Text> By
              </Trans>
            </Text>
            <Pressable
              onPress={() => handleContact('tel:28500777')}
              style={settingStyle.contactUsButton}>
              <CustomSVG svgIcon={PhoneIcon} width={iconSize} height={iconSize} />
              <Text style={settingStyle.contactUsButtonText}>+852 2850 0777</Text>
            </Pressable>
            <Pressable onPress={() => phoneRef?.current?.close()} style={buttonStyle.container}>
              <Text style={buttonStyle.text}>{trans('Close')}</Text>
            </Pressable>
          </View>
        </CustomBottomSheet>
        <CustomBottomSheet bsRef={emailRef} isScrollable={true} isHeaderComponent={false}>
          <View style={settingStyle.contactUs}>
            <Text style={settingStyle.contactUsText}>
              <Trans i18nKey="contactTMRBy">
                Contact <Text style={settingStyle.brandName}>TMR ARTS LIMITED</Text> By
              </Trans>
            </Text>
            <Pressable
              onPress={() => handleContact('mailto:<EMAIL>')}
              style={settingStyle.contactUsButton}>
              <CustomSVG svgIcon={EmailIcon} width={iconSize} height={iconSize} />
              <Text style={settingStyle.contactUsButtonText}><EMAIL></Text>
            </Pressable>
            <Pressable onPress={() => emailRef?.current?.close()} style={buttonStyle.container}>
              <Text style={buttonStyle.text}>{trans('Close')}</Text>
            </Pressable>
          </View>
        </CustomBottomSheet>
      </View>
    </View>
  )
}

export default ContactUs
