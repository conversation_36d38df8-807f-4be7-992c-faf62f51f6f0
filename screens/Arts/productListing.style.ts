import { StyleSheet, ViewStyle } from 'react-native'
import dpr from '../Utilities/CustomStyleAttribute/dpr'

export const productListingStyle = StyleSheet.create({
  categoryContainer: {
    marginBottom: dpr(10),
    paddingHorizontal: dpr(20),
  },
  const: {
    backgroundColor: '#fff',
    flex: 1,
    paddingHorizontal: dpr(20),
  },
  filterComponentsContainer: {
    flex: 0,
  },
  justifyContent: {
    justifyContent: 'space-between',
  },
  mb: {
    marginBottom: dpr(80),
  },
  nftListing: {
    columnGap: 20,
    justifyContent: 'space-between',
  },
  pb0: { paddingBottom: 0 },
  py: {
    paddingTop: dpr(20),
  },
  result: {
    color: '#E8E6E3',
    fontFamily: 'DMSans_400Regular',
    fontSize: dpr(12),
    marginTop: dpr(20),
  },
})

export const nftListing = (gap: number): ViewStyle => {
  return {
    flexDirection: 'row',
    columnGap: gap,
    marginBottom: gap,
    justifyContent: 'flex-start',
  }
}
