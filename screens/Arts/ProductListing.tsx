import React, { useCallback, useEffect, useRef } from 'react'
import { useState } from 'react'
import { useMemo } from 'react'
import { View, FlatList, Modal } from 'react-native'
import { useAnimatedStyle, useSharedValue, withSpring } from 'react-native-reanimated'

import { productListingStyle } from './productListing.style'
import { gapStyle } from '../Utilities/CommonStyles/gap.style'
import { featureDisplayStyle } from '@/screens/Home/FeatureDIsplay/featureDisplay.style'
import EmptyContent from '@/screens/Utilities/EmptyContent/EmptyContent'
import CustomActiveIndicator from '@/src/components/CustomLoader/CustomActiveIndicator'
import useLangTranslation from '@/hooks/useLangTranslation'
import SingleProduct from '@/src/components/SingleProduct/SingleProduct'
import FeatureProductSkeleton from '@/src/skeletons/screens/home/<USER>'
import ProductSearchInput from '@/screens/Filter/FilterHome/ProductSearchInput'
import SearchFilter1 from '@/screens/Filter/SearchFilter1/SearchFilter1'
import NoWishlistIcon from '@/assets/svgs/empty content/noWishlist.svg'
import Filters from '@/screens/Filter/Filters/Filters'
import { useAppDispatch, useAppSelector } from '@/hooks/reduxHooks'
import SelectItemBottomSheet from '@/src/components/SelectItemBottomSheet/SelectItemBottomSheet'
import useAuth from '@/hooks/useAuth'

import { apiService } from '@/redux/slices/util/apiRequest'
import FeatureListProductSkeleton from '@/src/skeletons/screens/home/<USER>'
import { getAllCategory } from '@/redux/slices/categories/allCategory'
import {
  getProductFilters,
  setAttrFilters,
  setFilterSearchText,
  setUIAttributeKey,
} from '@/redux/slices/featureProducts/filters'
import FadeInView from '@/src/components/FadeInView/FadeInView'
import SingleProductListView from '@/src/components/SingleProductListView/SingleProductListView'
import CategoryTabs from '@/src/components/CategoryTabs/CategoryTabs'
import { HomeTabScreenProps } from '@/types/navigations'
import { Filter } from '@/types/productFilter'
import { Product } from '@/types/productDetailState'
import FilterTabs from '@/src/components/FilterTabs/FilterTabs'

const URL = `${process.env.EXPO_PUBLIC_BASE_API_URL}/products`

interface Sort {
  name: string
  key: string
  sortOrder: string | null
}

const initFilter: Filter = {
  search: '',
  path: '', // category
  min: 0,
  max: 0,
  hot: '',
  latest: '',
  nft: '',
  sort: 'sort_order',
  limit: '12',
  order: 'ASC',
  page: '1',
}

const ProductListing = (props: HomeTabScreenProps<'ProductListing'>) => {
  const { trans } = useLangTranslation()
  const { navigation } = props
  const routeFilter = props?.route?.params
  const { lng, currency } = useAuth()
  const dispatch = useAppDispatch()
  const { allCategory } = useAppSelector((state) => state.allCategory)
  const { attrFilters, productFilter } = useAppSelector((state) => state.productFilterReducer)
  const { query } = apiService

  const [products, setProducts] = useState<Product[]>([])
  const [listView, setListView] = useState(false)
  const [loading, setLoading] = useState(false)
  const [loadMore, setLoadMore] = useState(false)
  const [pagination, setPagination] = useState({
    total: 0,
    page: 0,
    limit: 0,
  })
  const [tab, setTab] = useState({
    parent_category_id: 0,
    child_category_id: 0,
  })

  //filter
  const sortByRef = useRef(null)
  const sortByArray = [
    { name: trans('Sort By'), key: 'sort_order', sortOrder: null },
    { name: trans("Filter-What's New"), key: 'latest', sortOrder: null },
    { name: trans('Most View'), key: 'hot', sortOrder: null },
    { name: trans('NFT Artwork'), key: 'nft', sortOrder: null },

    { name: trans('Alphabetically, A-Z'), key: 'pd.name', sortOrder: 'ASC' },
    { name: trans('Alphabetically, Z-A'), key: 'pd.name', sortOrder: 'DESC' },
    { name: trans('Price (High to Low)'), key: 'p.price', sortOrder: 'DESC' },
    { name: trans('Price (Low to High)'), key: 'p.price', sortOrder: 'ASC' },
  ]
  const [searchText, setSearchText] = useState('')
  const [filterModal, setFilterModal] = useState(false)
  const [sortBy, setSortBy] = useState<Sort>(sortByArray[0])
  const [filters, setFilters] = useState(initFilter)
  const abortControllerRef = useRef<AbortController | null>(null)
  const prevFiltersRef = useRef<string>('')
  const requestIdRef = useRef<number>(0)
  const routeFilterProcessed = useRef<boolean>(false)
  const skipNextFilterEffect = useRef<boolean>(false)

  const filterParams = (param: Filter) => {
    return new URLSearchParams(
      Object.entries(param).filter(([_, value]) => value !== ''),
    ).toString()
  }

  useEffect(() => {
    let isMounted = true
    if (isMounted) {
      dispatch(getAllCategory({ lng, currency }))
      dispatch(getProductFilters({ lng, currency }))

      if (routeFilter?.sort) {
        const matchedItem = sortByArray.find((item) => item.key === routeFilter?.sort)
        if (matchedItem) {
          console.warn('Processing route filter for sort:', matchedItem.key)
          routeFilterProcessed.current = true
          skipNextFilterEffect.current = true
          handleSortBy(matchedItem)
          return
        }
      }
      if (routeFilter?.searchText) {
        const newFilters = {
          ...filters,
          search: routeFilter?.searchText,
        }
        routeFilterProcessed.current = true
        setFilters(newFilters)
        setSearchText(routeFilter?.searchText)
        handleFetchData(newFilters)
        return
      }
      handleFetchData(filters)
    }
    return () => {
      isMounted = false
    }
  }, [lng, currency, routeFilter])

  const handleFetchData = useCallback(
    async (param: Filter) => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
      abortControllerRef.current = new AbortController()
      const requestId = ++requestIdRef.current

      const queryParam = filterParams(param)
      try {
        setLoading(true)
        const apiURL = queryParam.length > 0 ? `${URL}?${queryParam}&${attrFilters}` : URL
        console.warn(`apiURL`, apiURL)
        const result = await query(apiURL, 'GET', {
          'X-Oc-Merchant-Language': lng,
          'X-Oc-Currency': currency,
        })

        if (requestId !== requestIdRef.current) {
          return
        }

        const { data: resultData, success } = result || {}
        if (success === 1) {
          const { data, page, pagination: resultPagination } = resultData
          setProducts(data)
          setPagination({
            total: resultPagination.total,
            page: resultPagination.page,
            limit: resultPagination.limit,
          })
          setFilters((prev) => ({ ...prev, page: String(page) }))
        }
      } catch (error) {
        if (error?.name !== 'AbortError') {
          console.error('Fetch error:', error)
        }
      } finally {
        if (requestId === requestIdRef.current) {
          setLoading(false)
        }
      }
    },
    [filters, attrFilters, lng, currency],
  )

  const handleMoreData = useCallback(async () => {
    if (loading || pagination.total / Number(pagination.limit) <= pagination.page) {
      return
    }

    const currentRequestId = requestIdRef.current

    try {
      setLoadMore(true)
      const tmpFilters = Object.assign({}, filters)
      tmpFilters.page = String(Number(tmpFilters.page) + 1)
      const newURL = new URLSearchParams(
        Object.entries(tmpFilters).filter(([_, value]) => value !== ''),
      ).toString()

      const apiURL = newURL.length > 0 ? `${URL}?${newURL}&${attrFilters}` : URL

      console.warn(`more data url: `, apiURL)
      const result = await query(apiURL, 'GET', {
        'X-Oc-Merchant-Language': lng,
        'X-Oc-Currency': currency,
      })

      if (currentRequestId !== requestIdRef.current) {
        return
      }

      const { data: resultData, success } = result || {}
      if (success === 1) {
        const { data, page, pagination: resultPagination } = resultData
        const tmpProducts = Object.assign([], products)
        setProducts(tmpProducts.concat(data))
        setPagination({
          total: resultPagination.total,
          page: resultPagination.page,
          limit: resultPagination.limit,
        })
        setFilters((prev) => ({ ...prev, page: String(page) }))
      }
    } catch (error) {
      console.error(error)
    } finally {
      if (currentRequestId === requestIdRef.current) {
        setLoadMore(false)
      }
    }
  }, [loading, pagination, filters, attrFilters, query, lng, currency, products])

  const memorizeItem = useMemo(
    () =>
      ({ item }: { item: Product }) => <SingleProduct item={item} />,
    [],
  )

  const handleRefresh = async () => {
    const tmpFilters = Object.assign({}, filters)
    tmpFilters.page = '1'
    setFilters((prev) => ({ ...prev, page: '1' }))
    handleFetchData(tmpFilters)
  }

  const handleCategoryChange = (parentId: number, childId: number) => {
    setTab({
      parent_category_id: parentId,
      child_category_id: childId,
    })
    const newFilters = {
      ...filters,
      path: childId ? `${parentId}_${childId}` : `${parentId}`,
      page: '1',
    }
    setFilters(newFilters)
    handleFetchData(newFilters)
  }

  const handleSortBy = (item: Sort) => {
    setSortBy(item)
    //in sorting UI, it is a mixture of filtering and sorting to get the api data
    // pd.nameASC, pd.nameDESC, p.priceAsc, p.priceDESC is sorting

    // Determine the type of filter parameter
    const specialSort = ['latest', 'hot', 'nft', 'sort_order']
    let newFilters = { ...filters }
    const isSpecialSort = specialSort.includes(item.key)
    if (isSpecialSort) {
      newFilters = {
        ...newFilters,
        [item.key]: '1',
      }
      // Reset other filter parameters
      specialSort.forEach((key) => {
        if (key !== item.key) {
          delete newFilters[key as keyof typeof Filters]
        }
      })
      ;['order', 'sort'].forEach((key) => {
        if (newFilters[key as keyof typeof Filters]) {
          delete newFilters[key as keyof typeof Filters]
        }
      })
    } else {
      // handle sorting parameter
      newFilters = {
        ...newFilters,
        sort: item.key,
        order: item.sortOrder ?? 'ASC',
      }
      // Remove special sort parameters if they exist
      ;['latest', 'hot', 'nft'].forEach((key) => {
        delete newFilters[key as keyof typeof Filters]
      })
    }
    // reset page to 1
    newFilters = {
      ...newFilters,
      page: '1',
    }
    setFilters(newFilters)
    handleFetchData(newFilters)
  }

  // for animation
  const offset = useSharedValue(0)
  const customSpringStyles = useAnimatedStyle(() => {
    return {
      transform: [
        {
          translateX: withSpring(offset.value, {
            damping: 20,
            stiffness: 90,
          }),
        },
      ],
    }
  })

  const resetFilter = () => {
    dispatch(setAttrFilters(''))
    dispatch(setUIAttributeKey(productFilter?.filters))
    dispatch(setFilterSearchText(''))

    setFilters(initFilter)
    setSearchText('')
    setFilterModal(false)
    setSortBy(sortByArray[0])
    handleFetchData(initFilter)
  }

  useEffect(() => {
    const filtersWithoutPage = { ...filters }
    delete (filtersWithoutPage as Partial<Filter>).page
    const currentFiltersString = JSON.stringify(filtersWithoutPage)

    if (skipNextFilterEffect.current) {
      console.warn('Skipping filter effect due to route processing')
      skipNextFilterEffect.current = false
      prevFiltersRef.current = currentFiltersString
      return
    }

    if (
      prevFiltersRef.current !== currentFiltersString && 
      (routeFilterProcessed.current || !routeFilter?.sort)
    ) {
      console.warn('Filter change detected, calling handleFetchData')
      handleFetchData(filters)
      prevFiltersRef.current = currentFiltersString
    }
  }, [filters, handleFetchData, routeFilter?.sort])

  useEffect(() => {
    // Don't override route filter on initial load
    if (routeFilterProcessed.current || !routeFilter?.sort) {
      console.warn('attrFilters effect triggered', { attrFilters, routeFilterProcessed: routeFilterProcessed.current })
      if (attrFilters) {
        const newFilters = {
          ...filters,
          page: '1',
        }
        setFilters(newFilters)
        handleFetchData(newFilters)
      }
      // Don't call handleFetchData for empty attrFilters when route filter just processed
    }
  }, [attrFilters, routeFilter?.sort])

  useEffect(() => {
    // Only update filters if route filter was processed or if searchText actually changed
    if (routeFilterProcessed.current && searchText !== '') {
      console.warn('searchText effect triggered', { searchText, routeFilterProcessed: routeFilterProcessed.current })
      const newFilters = {
        ...filters,
        page: '1',
        search: searchText,
      }
      setFilters(newFilters)
      handleFetchData(newFilters)
    }
  }, [searchText, routeFilter?.sort])

  return (
    <FadeInView>
      <ProductSearchInput searchText={searchText} setSearchText={setSearchText} />
      <View style={productListingStyle.categoryContainer}>
        <CategoryTabs activeTab={tab} categories={allCategory} onPress={handleCategoryChange} />
      </View>
      <View style={gapStyle.mb20}>
        <FilterTabs />
      </View>
      <View style={[featureDisplayStyle.const, productListingStyle.filterComponentsContainer]}>
        <SearchFilter1
          setFilterModal={setFilterModal}
          sortByRef={sortByRef}
          sortBy={sortBy}
          sortByArray={sortByArray}
          listView={listView}
          setListView={setListView}
        />
      </View>
      <View style={[productListingStyle.const, productListingStyle.py]}>
        <View>
          {loading ? (
            listView ? (
              <FeatureListProductSkeleton />
            ) : (
              <FeatureProductSkeleton />
            )
          ) : products?.length > 0 ? (
            listView ? (
              <FlatList
                key={`list-${listView ? 'single' : 'double'}`}
                keyboardShouldPersistTaps={'always'}
                data={products}
                keyExtractor={(_, i) => 'key' + i}
                renderItem={({ item }) => <SingleProductListView item={item} />}
                showsVerticalScrollIndicator={false}
                initialNumToRender={12}
                windowSize={10}
                onEndReachedThreshold={1}
                onEndReached={handleMoreData}
                refreshing={false}
                onRefresh={handleRefresh}
              />
            ) : (
              <FlatList
                key={`grid-${listView ? 'single' : 'double'}`}
                data={products}
                keyExtractor={(_, i) => 'key-A' + i}
                renderItem={memorizeItem}
                showsVerticalScrollIndicator={false}
                numColumns={2}
                columnWrapperStyle={productListingStyle.justifyContent}
                refreshing={false}
                onRefresh={handleRefresh}
                onEndReachedThreshold={0.5}
                onEndReached={handleMoreData}
              />
            )
          ) : (
            <FlatList
              data={null}
              renderItem={null}
              refreshing={false}
              onRefresh={handleRefresh}
              showsVerticalScrollIndicator={false}
              ListHeaderComponent={
                <EmptyContent Icon={NoWishlistIcon} text={trans('No Product Found')} />
              }
            />
          )}
          {loadMore && <CustomActiveIndicator />}
        </View>
      </View>
      <Modal
        onRequestClose={() => setFilterModal(false)}
        visible={filterModal}
        animationType="slide">
        <Filters
          filters={filters}
          setFilters={setFilters}
          setFilterModal={setFilterModal}
          resetFilter={resetFilter}
        />
      </Modal>
      <SelectItemBottomSheet
        snapPoint={[480]}
        selectRef={sortByRef}
        data={sortByArray}
        name={'SortBy'}
        onPress={handleSortBy}
      />
    </FadeInView>
  )
}

export default ProductListing
