import { StyleSheet, Dimensions } from 'react-native'
import dpr from '../../Utilities/CustomStyleAttribute/dpr'
import { themeColor } from '@/theme/theme'
const { width } = Dimensions.get('screen')

export const advertismentStyle = StyleSheet.create({
  adBody: {
    backgroundColor: '#dddddd',
    color: '#4e5f70',
    fontFamily: 'EBGaramond_400Regular',
    fontSize: dpr(28),
    lineHeight: dpr(29),
    marginTop: dpr(10),
    textAlign: 'center',
  },
  adTitle: {
    color: themeColor.primaryColoredText,
    fontFamily: 'DMSans_500Medium',
    fontSize: dpr(18),
    lineHeight: dpr(23),
    textAlign: 'center',
    textTransform: 'uppercase',
  },
  adsection: {
    paddingBottom: dpr(70),
  },
  image: {
    flex: 1,
    height: width * 1.09,
    justifyContent: 'flex-end',
    padding: dpr(20),
    width: width,
  },
  pb: {
    paddingBottom: dpr(30),
  },
  viewMore: {
    color: themeColor.primaryColoredText,
    fontFamily: 'DMSans_500Medium',
    fontSize: dpr(12),
    lineHeight: dpr(16),
  },
  viewMoreBtn: {
    alignSelf: 'center',
    backgroundColor: themeColor.primaryButton,
    marginTop: dpr(30),
    paddingHorizontal: 4,
    paddingVertical: 4,
  },
  viewMoreLoading: {
    color: themeColor.thirdText,
    fontFamily: 'DMSans_500Medium',
    fontSize: dpr(12),
    lineHeight: dpr(16),
  },
})
