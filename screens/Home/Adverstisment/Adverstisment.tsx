import { useEffect } from 'react'
import { ImageBackground, Linking, Pressable, Text, View } from 'react-native'
import { advertismentStyle } from './adverstisment.style'
import useLangTranslation from '../../../hooks/useLangTranslation'
import { getHomeAdvertisment } from '@/redux/slices/home/<USER>'
import { useAppDispatch, useAppSelector } from '@/hooks/reduxHooks'
import useAuth from '@/hooks/useAuth'
import AdvertismentSkeleton from '@/src/skeletons/screens/home/<USER>'

const Adverstisment = () => {
  const { trans } = useLangTranslation()
  const dispatch = useAppDispatch()
  const { homeAdvertisment, loading } = useAppSelector((state) => state.homeAdvertismentReducer)
  const { lng, currency } = useAuth()

  useEffect(() => {
    let isMounted = true
    if (isMounted) {
      dispatch(getHomeAdvertisment({ lng, currency }))
    }
    return () => {
      isMounted = false
    }
  }, [lng, currency])

  return (
    <>
      {Object.keys(homeAdvertisment)?.length > 0 ? (
        <View style={advertismentStyle.pb}>
          {loading ? (
            <AdvertismentSkeleton />
          ) : (
            <ImageBackground
              source={{ uri: homeAdvertisment?.image }}
              style={advertismentStyle.image}>
              <View style={advertismentStyle.adsection}>
                <View>
                  <Text style={advertismentStyle.adTitle}>{homeAdvertisment?.title}</Text>
                </View>
                <Text style={advertismentStyle.adBody}>
                  {homeAdvertisment?.description?.trim().replace(/\r\n\r\n|&nbsp;/g, '')}
                </Text>
                <Pressable
                  hitSlop={20}
                  onPress={() => Linking.openURL(homeAdvertisment?.link)}
                  style={advertismentStyle.viewMoreBtn}>
                  {({ pressed }) => (
                    <Text
                      style={
                        pressed ? advertismentStyle.viewMoreLoading : advertismentStyle.viewMore
                      }>
                      {trans('View More')}
                    </Text>
                  )}
                </Pressable>
              </View>
            </ImageBackground>
          )}
        </View>
      ) : null}
    </>
  )
}

export default Adverstisment
