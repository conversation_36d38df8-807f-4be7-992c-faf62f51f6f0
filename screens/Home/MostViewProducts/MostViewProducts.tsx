import React, { useEffect } from 'react'
import { useMemo } from 'react'
import { useNavigation } from '@react-navigation/native'
import { FlatList, Pressable, Text, View } from 'react-native'
import { NavigationProp } from '@react-navigation/native'
import FeatureProductSkeleton from '../../../src/skeletons/screens/home/<USER>'
import { ProductsStyle } from '../FeaturedProducts/featureProducts.style'
import SingleProduct from '../../../src/components/SingleProduct/SingleProduct'
import useLangTranslation from '../../../hooks/useLangTranslation'
import { whatsNewStyle } from '../WhatsNew/whatsNew.style'
import { useAppDispatch, useAppSelector } from '@/hooks/reduxHooks'
import { ProductState } from '@/redux/slices/featureProducts/featureProducts'
import { getHomeMostView } from '@/redux/slices/home/<USER>'
import useAuth from '@/hooks/useAuth'
import { HomeTabParamList } from '@/types/navigations'

const MostViewProducts = () => {
  const { trans } = useLangTranslation()
  const navigation = useNavigation<NavigationProp<HomeTabParamList, 'Home'>>()
  const dispatch = useAppDispatch()
  const { homeMostViewList, loading } = useAppSelector((state) => state.homeMostViewReducer)
  const { lng, currency } = useAuth()

  useEffect(() => {
    let isMounted = true
    if (isMounted) {
      dispatch(getHomeMostView({ lng, currency }))
    }
    return () => {
      isMounted = false
    }
  }, [lng, currency])

  const memorizeItem = useMemo(
    () =>
      ({ item }: { item: ProductState }) => <SingleProduct item={item} navigation={navigation} />,
    [],
  )
  const productsStyle = ProductsStyle()
  return (
    <>
      {homeMostViewList?.length > 0 ? (
        <View style={whatsNewStyle.container}>
          <View style={whatsNewStyle.sectionTitle}>
            <Text style={whatsNewStyle.title}>{trans('Most View Products')}</Text>
            <Pressable
              hitSlop={20}
              onPress={() => navigation.navigate('ProductListing', { sort: 'hot' })}>
              {({ pressed }) => (
                <Text style={pressed ? whatsNewStyle.viewMoreloading : whatsNewStyle.viewMore}>
                  {trans('View More')}
                </Text>
              )}
            </Pressable>
          </View>
          <View style={productsStyle.container}>
            <View style={productsStyle.pb}>
              {loading ? (
                <FeatureProductSkeleton />
              ) : (
                <FlatList
                  data={homeMostViewList}
                  keyExtractor={(_, i) => `fp${i}`}
                  renderItem={memorizeItem}
                  showsVerticalScrollIndicator={false}
                  numColumns={2}
                  columnWrapperStyle={productsStyle.columnWrapper}
                  initialNumToRender={4}
                  windowSize={10}
                  onEndReachedThreshold={1}
                />
              )}
            </View>
          </View>
        </View>
      ) : null}
    </>
  )
}

export default MostViewProducts
