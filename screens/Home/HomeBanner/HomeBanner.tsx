import React, { useEffect } from 'react'
import { Image, View } from 'react-native'
import ImageCarousel from './ImageCarousel'
import { bannerStyle } from './homeBanner.style'
import { getHomeBanner } from '@/redux/slices/home/<USER>'
import SkeletonElement from '@/src/skeletons/SkeletonElement'
import { useAppDispatch, useAppSelector } from '@/hooks/reduxHooks'
import useAuth from '@/hooks/useAuth'

const preview = '@/assets/images/image-loader-icon.png'

const HomeBanner = () => {
  const dispatch = useAppDispatch()
  const { banner, loading } = useAppSelector((state) => state.homeBannerReducer)
  const { lng, currency } = useAuth()

  useEffect(() => {
    let isMounted = true

    if (isMounted) {
      dispatch(getHomeBanner({ lng, currency }))
    }
    return () => {
      isMounted = false
    }
  }, [lng, currency])

  return (
    <View style={bannerStyle.container}>
      {loading && banner?.length === 0 ? (
        <>
          <SkeletonElement wrapperStyle={bannerStyle.skeletonTitle} />
          <SkeletonElement wrapperStyle={bannerStyle.skeletonWrapper} />
        </>
      ) : banner?.length > 0 ? (
        <ImageCarousel data={banner} autoPlay={true} pagination={true} />
      ) : (
        <Image source={preview} style={bannerStyle.defaultImage} />
      )}
    </View>
  )
}

export default HomeBanner
