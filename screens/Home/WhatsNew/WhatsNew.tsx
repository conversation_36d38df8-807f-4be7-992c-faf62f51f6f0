import React, { useEffect } from 'react'
import { useMemo } from 'react'
import { useNavigation } from '@react-navigation/native'
import { FlatList, Pressable, Text, View } from 'react-native'
import { NavigationProp } from '@react-navigation/native'
import FeatureProductSkeleton from '../../../src/skeletons/screens/home/<USER>'
import { ProductsStyle } from '../FeaturedProducts/featureProducts.style'
import SingleProduct from '../../../src/components/SingleProduct/SingleProduct'
import useLangTranslation from '../../../hooks/useLangTranslation'
import { whatsNewStyle } from './whatsNew.style'
import { useAppDispatch, useAppSelector } from '@/hooks/reduxHooks'
import { getHomeWhatsNew } from '@/redux/slices/home/<USER>'
import { ProductState } from '@/redux/slices/featureProducts/featureProducts'
import useAuth from '@/hooks/useAuth'
import { HomeTabParamList } from '@/types/navigations'

const WhatsNew = () => {
  const { trans } = useLangTranslation()
  const navigation = useNavigation<NavigationProp<HomeTabParamList, 'Home'>>()
  const dispatch = useAppDispatch()
  const { homeWhatsNewList, loading } = useAppSelector((state) => state.homeWhatsNewReducer)
  const { lng, currency } = useAuth()

  useEffect(() => {
    let isMounted = true
    if (isMounted) {
      dispatch(getHomeWhatsNew({ lng, currency }))
    }
    return () => {
      isMounted = false
    }
  }, [lng, currency])

  const memorizeItem = useMemo(
    () =>
      ({ item }: { item: ProductState }) => <SingleProduct item={item} navigation={navigation} />,
    [],
  )
  const productsStyle = ProductsStyle()
  return (
    <>
      {homeWhatsNewList?.length > 0 ? (
        <View style={whatsNewStyle.container}>
          <View style={whatsNewStyle.sectionTitle}>
            <Text style={whatsNewStyle.title}>{trans("What's New")}</Text>
            <Pressable
              hitSlop={20}
              onPress={() => navigation.navigate('ProductListing', { sort: 'latest' })}>
              {({ pressed }) => (
                <Text style={pressed ? whatsNewStyle.viewMoreloading : whatsNewStyle.viewMore}>
                  {trans('View More')}
                </Text>
              )}
            </Pressable>
          </View>
          <View style={productsStyle.container}>
            <View style={productsStyle.pb}>
              {loading ? (
                <FeatureProductSkeleton />
              ) : (
                <FlatList
                  data={homeWhatsNewList}
                  keyExtractor={(_, i) => `fp${i}`}
                  renderItem={memorizeItem}
                  showsVerticalScrollIndicator={false}
                  numColumns={2}
                  columnWrapperStyle={productsStyle.columnWrapper}
                  initialNumToRender={4}
                  windowSize={10}
                  onEndReachedThreshold={1}
                />
              )}
            </View>
          </View>
        </View>
      ) : null}
    </>
  )
}

export default WhatsNew
