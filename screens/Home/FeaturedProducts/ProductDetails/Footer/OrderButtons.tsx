import React from 'react'
import { Linking, Platform, Pressable, Text, View } from 'react-native'
import { useNavigation } from '@react-navigation/native'
import { productDetailsStyle } from '../productDetails.style'
import CustomSpinner from '@/screens/Utilities/CustomSpinner/CustomSpinner'
import dpr from '@/screens/Utilities/CustomStyleAttribute/dpr'
import useLangTranslation from '@/hooks/useLangTranslation'

interface OrderButtonsProps {
  stockManage: {
    isForSale: boolean
  }
  cartLoading: boolean
  handleAddToCart: () => void
  tradeURL?: string
}

const OrderButtons = ({
  stockManage,
  cartLoading,
  handleAddToCart,
  tradeURL,
}: OrderButtonsProps) => {
  const { isForSale } = stockManage
  const { trans } = useLangTranslation()
  const navigation = useNavigation()
  return (
    <View style={productDetailsStyle.bottomSections}>
      {cartLoading ? (
        <CustomSpinner
          filePath={require('@/assets/lottie/loadingBlack.json')}
          size={{
            width: dpr(60),
            height: dpr(40),
          }}
        />
      ) : (
        <>
          <View style={productDetailsStyle.bottomSectionRow}>
            <Pressable
              onPress={() => (cartLoading ? {} : handleAddToCart())}
              style={[
                productDetailsStyle.cartBtn,
                isForSale ? productDetailsStyle.addToCart : productDetailsStyle.disabledAddToCart,
              ]}
              disabled={!isForSale}>
              <Text
                style={[
                  productDetailsStyle.cartBtnText,
                  isForSale ? productDetailsStyle.addToCart : productDetailsStyle.disabledAddToCart,
                ]}>
                {isForSale ? trans('Add to Cart') : trans('Sold Out')}
              </Text>
            </Pressable>
            <Pressable
              onPress={() => (cartLoading ? {} : navigation.navigate('EnquiryForm'))}
              style={[productDetailsStyle.cartBtn, productDetailsStyle.contactUs]}>
              <Text style={productDetailsStyle.cartBtnText}>{trans('Contact Us')}</Text>
            </Pressable>
          </View>
          {Platform.OS !== 'ios' && tradeURL && (
            <View style={[productDetailsStyle.bottomSectionRow, productDetailsStyle.openSeaLink]}>
              <Pressable
                onPress={() => Linking.openURL(tradeURL)}
                style={[productDetailsStyle.cartBtn, productDetailsStyle.externalLink]}>
                <Text style={[productDetailsStyle.cartBtnText, productDetailsStyle.externalLink]}>
                  {trans('View On Opensea')}
                </Text>
              </Pressable>
            </View>
          )}
        </>
      )}
    </View>
  )
}

export default OrderButtons
