import React, { useState, useEffect, useMemo } from 'react'
import { View, Pressable, Text, FlatList, ListRenderItemInfo } from 'react-native'
import Toast from 'react-native-toast-message'
import { NativeStackScreenProps } from '@react-navigation/native-stack'
import { productDetailsStyle } from './productDetails.style'
import ItemCarousel from './ItemCarousel/ItemCarousel'
import HubMediaGallery from '@/src/components/HubMediaGallery/HubMediaGallery'

import ItemHeader from './ItemDetails/ItemSection/ItemHeader'
import dpr from '@/screens/Utilities/CustomStyleAttribute/dpr'
import BackNavigation from '@/screens/Utilities/CustomHeader/BackNavigation'
import EmptyContent from '@/screens/Utilities/EmptyContent/EmptyContent'
import { storeItemInCart } from '@/redux/slices/cart/storeItemInCart'
import useAuth from '@/hooks/useAuth'
import { getCartProduct } from '@/redux/slices/cart/getCartProducts'
import useCustomToast from '@/hooks/useCustomToast'

import { getMyAddress } from '@/redux/slices/user/address/getMyAddress'

import useLangTranslation from '@/hooks/useLangTranslation'
import ProductDetailsSkeleton from '@/src/skeletons/screens/home/<USER>'
import NoWishlistIcon from '@/assets/svgs/empty content/noWishlist.svg'
import DownIcon from '@/assets/svgs/dropdown/down.svg'
import UpIcon from '@/assets/svgs/dropdown/up.svg'
import CustomSVG from '@/src/components/CustomSVG/CustomSVG'
import { commonStyles } from '@/screens/Utilities/CommonStyles/common.styles'
import OrderButtons from '@/screens/Home/FeaturedProducts/ProductDetails/Footer/OrderButtons'
import { RootStackParamList } from '@/types/navigations'
import { Product, ProductDetailState } from '@/types/productDetailState'
import { productListingStyle } from '@/screens/Arts/productListing.style'
import SingleProduct from '@/src/components/SingleProduct/SingleProduct'
import { gapStyle } from '@/screens/Utilities/CommonStyles/gap.style'
import { apiService } from '@/redux/slices/util/apiRequest'
import { useAppDispatch, useAppSelector } from '@/hooks/reduxHooks'
import { setAttrFilters, toggleFilterSelection } from '@/redux/slices/featureProducts/filters'
import ProductDescription from './ProductDescription'

type Props = NativeStackScreenProps<RootStackParamList, 'ProductDetails'>

const cartURL = `${process.env.EXPO_PUBLIC_BASE_API_URL}/cart`

const iconSize = dpr(16)

interface galleryDataType {
  image: string
  image_cache: string
  video: string
  name: string
}

const ProductDetails = ({ route, navigation }: Props) => {
  const { trans } = useLangTranslation()
  const {
    params: { productId: productId },
  } = route
  const showToast = useCustomToast()
  const { cartLoading } = useAppSelector((state) => state.storeItemInCartSlice)
  const productDetailsUrl = `${process.env.EXPO_PUBLIC_BASE_API_URL}/products/${productId}`
  const dispatch = useAppDispatch()
  const { lng, currency } = useAuth()
  const { query } = apiService

  const [loading, setLoading] = useState(true)
  const [productDetails, setProductDetails] = useState({} as ProductDetailState)
  const [galleryData, setGalleryData] = useState<galleryDataType>()
  const [carouselData, setCarouselData] = useState([])
  const { uiAttributeKey } = useAppSelector((state) => state.productFilterReducer)
  const [itemCarouselData, setItemCarouselData] = useState({
    images: [
      {
        thumb: '',
        preview: '',
        popup: '',
      },
    ],
    activeImage: 0,
  })

  const [itemHeaderData, setItemHeaderData] = useState({
    name: '',
    productNo: '',
  })

  const [productInfoSections, setProductInfoSections] = useState({
    description: {
      name: 'Description',
      isShowing: true,
    },
    descriptions: {
      name: 'Descriptions',
      isShowing: false,
    },
    details: {
      name: 'Details',
      isShowing: false,
    },
    productInfo: {
      name: 'Product Info',
      isShowing: false,
    },
  })

  const [stockManage, setStockManage] = useState({
    isForSale: false,
  })

  const memorizeItem = useMemo(
    () =>
      ({ item }: ListRenderItemInfo<Product>) => <SingleProduct item={item} />,
    [],
  )

  useEffect(() => {
    let isMounted = true
    ;(async () => {
      if (isMounted) {
        const singleItem = await query(productDetailsUrl, 'GET', {
          'X-Oc-Merchant-Language': lng,
          'X-Oc-Currency': currency,
        })
        const { success, data } = singleItem
        
        if (success === 1) {
          
          setProductDetails(data)
          setLoading(false)
          if (isMounted && data?.images) {
            setItemCarouselData({
              ...itemCarouselData,
              images: data?.images,
            })
            setGalleryData({
              image: data?.images[0]?.popup,
              image_cache: data?.images[0]?.thumb,
              video: data?.video,
              name: data?.name,
            })
            if (data.video) {
              data.images[0].animation_url = data.video;
              data.images[0].image = data.images[0].preview;
            }
            
            setCarouselData(data.images);
            setItemHeaderData({
              name: data?.name,
              productNo: data?.model,
            })
            setStockManage({
              // isForSale: Object.keys(data?.nft_info).length === 0,
              isForSale: data?.sold_out === false,
            })
          }
        } else {
          setLoading(false)
          showToast({
            text1: trans('Something Went Wrong Please Try Again!'),
            type: 'common',
            position: 'bottom',
            props: { variant: 'error' },
          })
        }
      }
    })()
    return () => {
      isMounted = false
    }
  }, [currency, lng, productId])

  const handleAddToCart = async () => {
    const cartData = {
      product_id: productId,
      quantity: 1,
    }
    try {
      const { payload } = await dispatch(storeItemInCart({ url: cartURL, cartData }))
      if (payload.success === 1) {
        showToast({
          text1: trans('Products added to cart successfully.'),
          text2: trans('Visit Cart'),
          type: 'addToCart',
          position: 'bottom',
          props: {
            func: () => {
              navigation.navigate('DrawerStack', {
                screen: 'HomeScreen',
                params: { screen: 'Cart' },
              })
              Toast.hide()
            },
          },
        })
        dispatch(getCartProduct({ lng, currency }))
      }
    } catch (error) {
      showToast({
        text1: trans('Something went wrong. Please Try Again!'),
        type: 'common',
        position: 'bottom',
        props: { variant: 'error' },
      })
    }
  }

  useEffect(() => {
    ;(async () => {
      let isMounted = true
      if (isMounted) {
        await dispatch(getMyAddress({ lng }))
      }
      return () => {
        isMounted = false
      }
    })()
  }, [])

  const handleUpDownIcon = (key: string) => {
    setProductInfoSections({
      ...productInfoSections,
      [key]: {
        ...productInfoSections[key as keyof typeof productInfoSections],
        isShowing: !productInfoSections[key as keyof typeof productInfoSections].isShowing,
      },
    })
  }

  const handleSelectItem = (parentCatId: number, id: number) => {
    dispatch(toggleFilterSelection({ parentCatId, id, forceSelect: true }))
    navigation.navigate('DrawerStack', {
      screen: 'HomeScreen',
      params: {
        screen: 'ProductListing',
      },
    })
  }

  const updateApiFilter = () => {
    let filterString = ''
    uiAttributeKey.map((item) => {
      item.values.map((subItem) => {
        if (subItem.isChecked) {
          filterString += `f${item.filter_group_id}=${subItem.id}&`
        }
      })
    })
    dispatch(setAttrFilters(filterString))
  }

  useEffect(() => {
    updateApiFilter()
  }, [uiAttributeKey])

  return (
    <>
      <BackNavigation navigationProps={navigation} routeName={trans('Detail')} capitalize={false} />
      <FlatList
        data={null}
        renderItem={null}
        ListHeaderComponent={
          <>
            {!loading && itemCarouselData?.images[0]?.preview?.length > 0 && (
              // <ItemCarousel data={itemCarouselData} />
              <HubMediaGallery 
                item={galleryData}
                carouselData={carouselData} 
                canDownload={false}
                canDownloadImage={false}
                canDownloadVideo={false}
                />
            )}
            <View style={[commonStyles.globalContainer, gapStyle.pb50]}>
              {loading ? (
                <ProductDetailsSkeleton />
              ) : (
                <>
                  <ItemHeader data={itemHeaderData} />
                  {!loading && Object.keys(productDetails ?? {})?.length === 0 && (
                    <View>
                      <EmptyContent Icon={NoWishlistIcon} text={trans('No Data Found')} />
                    </View>
                  )}
                  <View style={productDetailsStyle.pricingSection}>
                    <Text style={productDetailsStyle.pricing}>{productDetails?.price}</Text>
                  </View>
                  {productDetails?.filters?.length > 0 && (
                    <>
                      <View style={commonStyles.accordionContainer}>
                        <Pressable
                          style={commonStyles.accordion}
                          onPress={() => handleUpDownIcon('description')}>
                          <Text style={commonStyles.accordionText}>{trans('Description')}</Text>
                          {productInfoSections.description.isShowing ? (
                            <CustomSVG svgIcon={UpIcon} height={iconSize} width={iconSize} />
                          ) : (
                            <CustomSVG
                              svgIcon={DownIcon}
                              height={iconSize}
                              width={iconSize}
                              fill={'#252525'}
                            />
                          )}
                        </Pressable>
                        {!productInfoSections.description.isShowing && (
                          <View style={[commonStyles.hrLine, gapStyle.mt0]} />
                        )}
                      </View>
                      {productInfoSections.description.isShowing && (
                        <View style={productDetailsStyle.metaDataContainer}>
                          {productDetails?.filters?.map((data, index) => (
                            <Pressable
                              style={productDetailsStyle.metaDataBlock}
                              key={index}
                              onPress={() =>
                                handleSelectItem(
                                  Number(data?.filter_group_id),
                                  Number(data?.filters[0]?.filter_id),
                                )
                              }>
                              <Text style={productDetailsStyle.metaDataLabel}>{data?.name}</Text>
                              <Text
                                style={[
                                  productDetailsStyle.metaDataLabel,
                                  productDetailsStyle.metaDataValue,
                                ]}>
                                {data?.filters[0]?.name}
                              </Text>
                            </Pressable>
                          ))}
                        </View>
                      )}
                      <View style={commonStyles.accordionContainer}>
                        <Pressable
                            style={commonStyles.accordion}
                            onPress={() => handleUpDownIcon('descriptions')}>
                            <Text style={commonStyles.accordionText}>{trans('Descriptions')}</Text>
                            {productInfoSections.descriptions.isShowing ? (
                              <CustomSVG svgIcon={UpIcon} height={iconSize} width={iconSize} />
                            ) : (
                              <CustomSVG
                                svgIcon={DownIcon}
                                height={iconSize}
                                width={iconSize}
                                fill={'#252525'}
                              />
                            )}
                        </Pressable>
                    </View>
                    {productInfoSections.descriptions.isShowing && (
                        <View style={productDetailsStyle.metaDataContainer}>
                          <ProductDescription description={productDetails?.description} />
                        </View>
                      )}
                    </>
                  )}
                  {productDetails?.nft_info &&
                    Object.keys(productDetails?.nft_info)?.length > 0 && (
                      <>
                        <View style={commonStyles.accordionContainer}>
                          <Pressable
                            style={commonStyles.accordion}
                            onPress={() => handleUpDownIcon('details')}>
                            <Text style={commonStyles.accordionText}>{trans('Details')}</Text>
                            {productInfoSections.details.isShowing ? (
                              <CustomSVG svgIcon={UpIcon} height={iconSize} width={iconSize} />
                            ) : (
                              <CustomSVG
                                svgIcon={DownIcon}
                                height={iconSize}
                                width={iconSize}
                                fill={'#252525'}
                              />
                            )}
                          </Pressable>
                          {!productInfoSections.details.isShowing && (
                            <View style={[commonStyles.hrLine, gapStyle.mt0]} />
                          )}
                        </View>
                        {productInfoSections.details.isShowing && (
                          <>
                            <View style={productDetailsStyle.detailRow}>
                              <Text style={productDetailsStyle.metaDataLabel}>
                                {trans('Contract Address')}
                              </Text>
                              <Text
                                style={[
                                  productDetailsStyle.metaDataLabel,
                                  productDetailsStyle.metaDataValue,
                                  productDetailsStyle.detailOddRow,
                                ]}>
                                {productDetails?.nft_info?.contract_address.substring(0, 5)}...$
                                {productDetails?.nft_info?.contract_address.slice(-5)}
                              </Text>
                            </View>

                            <View style={productDetailsStyle.detailRow}>
                              <Text style={productDetailsStyle.metaDataLabel}>
                                {trans('Token Standard')}
                              </Text>
                              <Text
                                style={[
                                  productDetailsStyle.metaDataLabel,
                                  productDetailsStyle.metaDataValue,
                                  productDetailsStyle.detailEvenRow,
                                ]}>
                                {productDetails?.nft_info?.token_type}
                              </Text>
                            </View>

                            <View style={productDetailsStyle.detailRow}>
                              <Text style={productDetailsStyle.metaDataLabel}>
                                {trans('Token ID')}
                              </Text>
                              <Text
                                style={[
                                  productDetailsStyle.metaDataLabel,
                                  productDetailsStyle.metaDataValue,
                                  productDetailsStyle.detailOddRow,
                                ]}>
                                {productDetails?.nft_info?.token_id}
                              </Text>
                            </View>

                            <View style={productDetailsStyle.detailRow}>
                              <Text style={productDetailsStyle.metaDataLabel}>
                                {trans('Chain')}
                              </Text>
                              <Text
                                style={[
                                  productDetailsStyle.metaDataLabel,
                                  productDetailsStyle.metaDataValue,
                                  productDetailsStyle.detailEvenRow,
                                ]}>
                                {productDetails?.nft_info?.chain}
                              </Text>
                            </View>
                          </>
                        )}
                      </>
                    )}
                  <View>
                    {productDetails?.width ||
                    productDetails?.height ||
                    productDetails?.length ||
                    productDetails?.weight ? (
                      <>
                        <View style={commonStyles.accordionContainer}>
                          <Pressable
                            style={commonStyles.accordion}
                            onPress={() => handleUpDownIcon('productInfo')}>
                            <Text style={commonStyles.accordionText}>{trans('Product Info')}</Text>
                            {productInfoSections.productInfo.isShowing ? (
                              <CustomSVG svgIcon={UpIcon} height={iconSize} width={iconSize} />
                            ) : (
                              <CustomSVG
                                svgIcon={DownIcon}
                                height={iconSize}
                                width={iconSize}
                                fill={'#252525'}
                              />
                            )}
                          </Pressable>
                          {!productInfoSections.productInfo.isShowing && (
                            <View style={[commonStyles.hrLine, gapStyle.mt0]} />
                          )}
                        </View>
                        {productInfoSections.productInfo.isShowing && (
                          <>
                            {productDetails?.length && (
                              <View style={productDetailsStyle.detailRow}>
                                <Text style={productDetailsStyle.metaDataLabel}>
                                  {productDetails?.text_dimensions_l}
                                </Text>
                                <Text
                                  style={[
                                    productDetailsStyle.metaDataLabel,
                                    productDetailsStyle.metaDataValue,
                                    productDetailsStyle.detailEvenRow,
                                  ]}>
                                  {productDetails?.length}
                                </Text>
                              </View>
                            )}
                            {productDetails?.width && (
                              <View style={productDetailsStyle.detailRow}>
                                <Text style={productDetailsStyle.metaDataLabel}>
                                  {productDetails?.text_dimensions_w}
                                </Text>
                                <Text
                                  style={[
                                    productDetailsStyle.metaDataLabel,
                                    productDetailsStyle.metaDataValue,
                                    productDetailsStyle.detailEvenRow,
                                  ]}>
                                  {productDetails?.width}
                                </Text>
                              </View>
                            )}
                            {productDetails?.height && (
                              <View style={productDetailsStyle.detailRow}>
                                <Text style={productDetailsStyle.metaDataLabel}>
                                  {productDetails?.text_dimensions_h}
                                </Text>
                                <Text
                                  style={[
                                    productDetailsStyle.metaDataLabel,
                                    productDetailsStyle.metaDataValue,
                                    productDetailsStyle.detailEvenRow,
                                  ]}>
                                  {productDetails?.height}
                                </Text>
                              </View>
                            )}
                            {productDetails?.dimensions_o && (
                              <View style={productDetailsStyle.detailRow}>
                                <Text style={productDetailsStyle.metaDataLabel}>
                                  {productDetails?.text_dimensions_o}
                                </Text>
                                <Text
                                  style={[
                                    productDetailsStyle.metaDataLabel,
                                    productDetailsStyle.metaDataValue,
                                    productDetailsStyle.detailEvenRow,
                                  ]}>
                                  {productDetails?.dimensions_o}
                                </Text>
                              </View>
                            )}
                            {productDetails?.weight && (
                              <View style={productDetailsStyle.detailRow}>
                                <Text style={productDetailsStyle.metaDataLabel}>
                                  {productDetails?.text_weight}
                                </Text>
                                <Text
                                  style={[
                                    productDetailsStyle.metaDataLabel,
                                    productDetailsStyle.metaDataValue,
                                    productDetailsStyle.detailEvenRow,
                                  ]}>
                                  {productDetails?.weight}
                                </Text>
                              </View>
                            )}
                          </>
                        )}
                      </>
                    ) : null}
                  </View>
                  <View>
                    {productDetails?.products?.length > 0 && (
                      <View style={productDetailsStyle.relatedProductWrapper}>
                        <View>
                          <Text style={productDetailsStyle.relatedProductTitle}>
                            {trans('Related Products')}
                          </Text>
                        </View>
                        <FlatList
                          key={`grid-double`}
                          data={productDetails?.products}
                          keyExtractor={(_, i) => 'key-A' + i}
                          renderItem={memorizeItem}
                          showsVerticalScrollIndicator={false}
                          numColumns={2}
                          columnWrapperStyle={productListingStyle.justifyContent}
                          refreshing={false}
                        />
                      </View>
                    )}
                  </View>
                </>
              )}
            </View>
          </>
        }
      />
      <View style={productDetailsStyle.bottomSectionsContainer}>
        <OrderButtons
          stockManage={stockManage}
          cartLoading={cartLoading}
          handleAddToCart={handleAddToCart}
          tradeURL={productDetails?.opensea_link}
        />
      </View>
    </>
  )
}

export default ProductDetails
