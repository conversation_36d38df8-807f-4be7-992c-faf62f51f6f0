import React, { useState } from 'react'
import { Pressable, Image, View } from 'react-native'
import { itemCarouselStyle } from './itemCarousel.style'
import CustomPhotoGallery from '@/src/components/CustomImageGallery/CustomPhotoGallery'
import { HubsProductImages, ProductImages } from '@/types/productDetailState'
import { DownloadContent } from '@/src/components/NFTDetail/NFTDetail'

interface SingleImageProps {
  item: ProductImages | HubsProductImages
  index: number
  setCurrentSlideIndex: (index: number) => void
  allImages: ProductImages[] | HubsProductImages[]
  itemName?: string
  canDownload?: boolean
  downloadContent?: DownloadContent[]
  canDownloadImage?: boolean
  canDownloadVideo?: boolean
}

const SingleImage = ({
  item,
  index,
  setCurrentSlideIndex,
  allImages,
  canDownload,
  downloadContent,
  canDownloadImage,
  canDownloadVideo,
}: SingleImageProps) => {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <>
      <Pressable style={itemCarouselStyle.singleImgCont} onPress={() => setIsOpen(true)}>
        {/* <View style={singleProductStyle.offerBadge}>
          <ProductBadge data={{ isMinted }} />
        </View> */}
        {item ? (
          <Image
            source={{
              uri: 'preview' in item ? item.preview : 'image' in item ? item.image : undefined,
            }}
            style={itemCarouselStyle.imageSize}
          />
        ) : (
          <Image
            source={require('@/assets/images/image-loader-icon.png')}
            style={itemCarouselStyle.imageSize}
          />
        )}
      </Pressable>
      <CustomPhotoGallery
        isOpen={isOpen}
        setIsOpen={setIsOpen}
        images={allImages}
        index={index}
        setCurrentSlideIndex={setCurrentSlideIndex}
        canDownload={canDownload}
        downloadContent={downloadContent}
        canDownloadImage={canDownloadImage}
        canDownloadVideo={canDownloadVideo}
      />
    </>
  )
}

export default SingleImage
