import React, { useState, useEffect } from 'react'
import { Platform, View, FlatList, Dimensions } from 'react-native'
import dpr from '../../../../Utilities/CustomStyleAttribute/dpr'
import { itemCarouselStyle } from './itemCarousel.style'
import SingleImage from './SingleImage'
import { HubsProductImages, ProductImages } from '@/types/productDetailState'
import { DownloadContent } from '@/src/components/NFTDetail/NFTDetail'

interface ItemCarouselProps {
  data: {
    images: ProductImages[] | HubsProductImages[]
    activeImage: number
  }
  isHub?: boolean
  itemName?: string
  canDownload?: boolean
  downloadContent?: DownloadContent[]
  canDownloadImage?: boolean
  canDownloadVideo?: boolean
}

const { width } = Dimensions.get('screen')

let scrollValue = 0
let scrolled = 0
// let flatList
const ItemCarousel = ({
  data,
  canDownload,
  downloadContent,
  canDownloadImage,
  canDownloadVideo,
}: ItemCarouselProps) => {
  const flatList = React.useRef<FlatList>(null)
  const { images, activeImage } = data || {}
  const [currentSlideIndex, setCurrentSlideIndex] = useState(activeImage)

  useEffect(() => {
    scrollValue = dpr('wf') * currentSlideIndex
    flatList?.current?.scrollToOffset({
      animated: true,
      offset: scrollValue,
    })
  }, [currentSlideIndex])

  useEffect(() => {
    setCurrentSlideIndex(activeImage)
    scrollValue = dpr('wf') * activeImage
    scrolled = activeImage
    flatList?.current?.scrollToOffset({
      animated: true,
      offset: scrollValue,
    })
  }, [activeImage])

  const moveBack = () => {
    if (scrolled === 0) {
      return
    } else {
      scrolled--
      setCurrentSlideIndex(scrolled)
      Platform.OS === 'android' && (scrollValue = scrollValue - dpr('wf'))
    }
    flatList?.current?.scrollToOffset({
      animated: true,
      offset: scrollValue,
    })
  }
  const moveNext = () => {
    if (scrolled === images?.length - 1) {
      return
    } else {
      scrolled++
      setCurrentSlideIndex(scrolled)
      Platform.OS === 'android' &&
        scrolled < images?.length &&
        (scrollValue = scrollValue + dpr('wf'))
      flatList?.current?.scrollToOffset({
        animated: true,
        offset: scrollValue,
      })
    }
  }

  const updateCurrentSlideIndex = (e) => {
    const contentOffsetX = e.nativeEvent.contentOffset.x
    scrollValue = contentOffsetX
    const currentIndex = Math.round(scrollValue / width)
    scrolled = currentIndex
    setCurrentSlideIndex(currentIndex)
  }

  return (
    <View>
      <FlatList
        ref={flatList}
        data={images}
        key={`grid-${images?.length}`}
        keyExtractor={(_, index) => 'key' + index}
        renderItem={({ item, index }) => {
          return (
            <SingleImage
              item={item}
              index={index}
              allImages={images}
              setCurrentSlideIndex={setCurrentSlideIndex}
              canDownload={canDownload}
              downloadContent={downloadContent}
              canDownloadImage={canDownloadImage}
              canDownloadVideo={canDownloadVideo}
            />
          )
        }}
        horizontal
        pagingEnabled
        scrollEnabled={true}
        snapToAlignment="center"
        scrollEventThrottle={16}
        decelerationRate={'fast'}
        showsHorizontalScrollIndicator={false}
        onMomentumScrollEnd={updateCurrentSlideIndex}
        overScrollMode="never"
      />
      {Object.keys(images).length > 1 && (
        <View style={itemCarouselStyle.indidatorCont}>
          {images.map((_, index) => (
            <View
              key={`key${index}`}
              style={[
                itemCarouselStyle.indicator,
                currentSlideIndex === index && itemCarouselStyle.activeIndicator,
              ]}
            />
          ))}
        </View>
      )}
    </View>
  )
}

export default ItemCarousel
