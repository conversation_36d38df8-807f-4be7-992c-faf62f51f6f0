import { StyleSheet, Dimensions } from 'react-native'
import dpr from '../../Utilities/CustomStyleAttribute/dpr'
const { width } = Dimensions.get('window')

const flexWidth = width / 2 - 30

const ProductsStyle = () =>
  StyleSheet.create({
    columnWrapper: {
      justifyContent: 'space-between',
    },
    container: {},
    discount: {
      color: '#CBCBCB',
      fontFamily: 'DMSans_500Medium',
      fontSize: dpr(12),
      marginLeft: dpr(2),
      marginTop: 'auto',
      textDecorationLine: 'line-through',
    },
    imageContainer: {
      alignItems: 'center',
      backgroundColor: '#FFFFFF',
      borderColor: '#DFDFDF',
      borderRadius: 6,
      borderWidth: 1,
      flexDirection: 'row',
      height: flexWidth,
      justifyContent: 'center',
      marginBottom: dpr(10),
      width: flexWidth,
    },
    img: {
      height: dpr(80),
      width: dpr(80),
    },
    imgCont: {
      borderRadius: 4,
      marginRight: dpr(20),
    },
    item: {
      marginBottom: dpr(20),
    },
    loadMore: {
      alignItems: 'center',
      bottom: dpr(10),
      justifyContent: 'center',
      left: 0,
      position: 'absolute',
      right: 0,
    },
    pb: {
      paddingBottom: dpr(30),
    },
    price: {
      color: '#2c2c2c',
      fontFamily: 'DMSans_700Bold',
      fontSize: dpr(14),
      lineHeight: dpr(17),
      textAlign: 'left',
    },
    priceTag: {
      color: '#666666',
      fontFamily: 'DMSans_400Regular',
      fontSize: dpr(12),
      lineHeight: dpr(13),
      marginTop: dpr(10),
      textTransform: 'uppercase',
    },
    productNo: {
      color: '#666666',
      fontFamily: 'DMSans_400Regular',
      fontSize: dpr(10),
      lineHeight: dpr(13),
      textTransform: 'uppercase',
      width: flexWidth,
    },
    start_mr: { marginRight: 3 },
    text: {
      color: '#2c2c2c',
      fontFamily: 'DMSans_500Medium',
      fontSize: dpr(14),
      lineHeight: dpr(16),
      textAlign: 'left',
      textTransform: 'uppercase',
      width: flexWidth,
    },
  })

export { ProductsStyle }
