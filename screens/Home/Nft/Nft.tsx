import React, { useEffect } from 'react'
import { useMemo } from 'react'
import { useNavigation } from '@react-navigation/native'
import { FlatList, Pressable, Text, View } from 'react-native'
import { NavigationProp } from '@react-navigation/native'
import { ProductsStyle } from '../FeaturedProducts/featureProducts.style'
import { nftStyle } from './nft.style'
import FeatureProductSkeleton from '@/src/skeletons/screens/home/<USER>'
import useLangTranslation from '@/hooks/useLangTranslation'
import NftProduct from '@/src/components/NftProduct/NftProduct'
import { useAppDispatch, useAppSelector } from '@/hooks/reduxHooks'
import { getHomeNft } from '@/redux/slices/home/<USER>'
import { ProductState } from '@/redux/slices/featureProducts/featureProducts'
import useAuth from '@/hooks/useAuth'
import { HomeTabParamList } from '@/types/navigations'

const Nft = () => {
  const { trans } = useLangTranslation()
  const navigation = useNavigation<NavigationProp<HomeTabParamList, 'Home'>>()
  const dispatch = useAppDispatch()
  const { homeNftList, loading } = useAppSelector((state) => state.homeNftReducer)
  const { lng, currency } = useAuth()

  useEffect(() => {
    let isMounted = true
    if (isMounted) {
      dispatch(getHomeNft({ lng, currency }))
    }
    return () => {
      isMounted = false
    }
  }, [lng, currency])

  const memorizeItem = useMemo(
    () =>
      ({ item }: { item: ProductState }) => <NftProduct item={item} />,
    [],
  )
  const productsStyle = ProductsStyle()
  return (
    <>
      {homeNftList?.length > 0 ? (
        <View style={nftStyle.container}>
          <View style={nftStyle.sectionTitle}>
            <Text style={nftStyle.title}>{trans('NFT')}</Text>
            <Pressable
              hitSlop={20}
              onPress={() => navigation.navigate('ProductListing', { sort: 'nft' })}>
              {({ pressed }) => (
                <Text style={pressed ? nftStyle.viewMoreloading : nftStyle.viewMore}>
                  {trans('View More')}
                </Text>
              )}
            </Pressable>
          </View>
          <View style={productsStyle.container}>
            <View style={productsStyle.pb}>
              {loading ? (
                <FeatureProductSkeleton />
              ) : (
                <FlatList
                  data={homeNftList}
                  keyExtractor={(_, i) => `fp${i}`}
                  renderItem={memorizeItem}
                  showsVerticalScrollIndicator={false}
                  numColumns={2}
                  columnWrapperStyle={productsStyle.columnWrapper}
                  initialNumToRender={4}
                  windowSize={10}
                  onEndReachedThreshold={1}
                />
              )}
            </View>
          </View>
        </View>
      ) : null}
    </>
  )
}

export default Nft
