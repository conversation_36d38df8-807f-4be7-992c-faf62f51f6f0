import { useEffect, useMemo } from 'react'
import { FlatList, Pressable, Text, View } from 'react-native'
import { useNavigation } from '@react-navigation/native'
import { NavigationProp } from '@react-navigation/native'
import { discoverStyle } from './discover.style'
import { RootStackParamList } from '@/types/navigations'
import useLangTranslation from '@/hooks/useLangTranslation'
import { useAppDispatch, useAppSelector } from '@/hooks/reduxHooks'
import { getHomeLatestStory } from '@/redux/slices/home/<USER>'
import useAuth from '@/hooks/useAuth'
import DiscoverSkeleton from '@/src/skeletons/screens/home/<USER>'
import DiscoverPoster from '@/screens/Discover/DiscoverPoster'

const Discover = () => {
  const { trans } = useLangTranslation()
  const dispatch = useAppDispatch()
  const { homeLatestStoryList, loading } = useAppSelector((state) => state.homeLatestStoryReducer)
  const { lng, currency } = useAuth()
  const navigation = useNavigation<NavigationProp<RootStackParamList>>()

  useEffect(() => {
    let isMounted = true
    if (isMounted) {
      dispatch(getHomeLatestStory({ lng, currency }))
    }
    return () => {
      isMounted = false
    }
  }, [lng, currency])

  return (
    <>
      {homeLatestStoryList?.length > 0 ? (
        <View style={discoverStyle.container}>
          <View style={discoverStyle.sectionTitle}>
            <Text style={discoverStyle.title}>{trans('Discover')}</Text>
            <Pressable hitSlop={20} onPress={() => navigation.navigate('Discover')}>
              {({ pressed }) => (
                <Text style={pressed ? discoverStyle.viewMoreLoading : discoverStyle.viewMore}>
                  {trans('View More')}
                </Text>
              )}
            </Pressable>
          </View>
          <View>
            {loading ? (
              <DiscoverSkeleton />
            ) : (
              <FlatList
                data={homeLatestStoryList}
                keyExtractor={(_, i) => `fp${i}`}
                renderItem={({ item }) => <DiscoverPoster item={item} navigation={navigation} />}
                showsVerticalScrollIndicator={false}
                numColumns={1}
                initialNumToRender={2}
              />
            )}
          </View>
        </View>
      ) : null}
    </>
  )
}

export default Discover
