import React, { useEffect, useState } from 'react'
import { View, Text, ScrollView, Pressable } from 'react-native'
import HTMLView from 'react-native-htmlview'
import { orderSummaryStyle } from '../OrderSummary/orderSummary.style'
import { orderConfirmedStyle } from './orderConfirmed.style'
import { orderDetailsStyle } from '../../Profile/OrderHistory/OrderDetails/orderDetails.style'
import OrderIndicator from '../OrderIndicator'
import { cartStyle } from '../shoppingCart.style'
import SelectedProduct from '../OrderSummary/SelectedProduct'
import useAuth from '@/hooks/useAuth'

import useLangTranslation from '@/hooks/useLangTranslation'
import useGuestUser from '@/hooks/useGuestUser'
import CustomSVG from '@/src/components/CustomSVG/CustomSVG'
import OrderDetailsSkeleton from '@/src/skeletons/screens/profile/order/OrderDetails/OrderDetailsSkeleton'
import { gapStyle } from '@/screens/Utilities/CommonStyles/gap.style'
import dpr from '@/screens/Utilities/CustomStyleAttribute/dpr'
import { commonStyles, dynamicStatusText } from '@/screens/Utilities/CommonStyles/common.styles'
import UpIcon from '@/assets/svgs/dropdown/up.svg'
import DownIcon from '@/assets/svgs/dropdown/down.svg'
import SuccessTickIcon from '@/assets/svgs/successTick.svg'
import { useAppDispatch, useAppSelector } from '@/hooks/reduxHooks'
import { emptyCart, getCartProduct } from '@/redux/slices/cart/getCartProducts'
import { clearOrderDetails, getOrderDetails } from '@/redux/slices/user/orderDetails/orderDetails'
import { themeColor } from '@/theme/theme'
import { ORDER_STATUS_LABEL } from '@/screens/Profile/OrderHistory/orderStatus'
import { resetCoupon } from '@/redux/slices/cart/coupon/applyCoupon'
import { clearOrderPreview } from '@/redux/slices/cart/order/postOrders'

const iconSize = dpr(16)
const cartURL = `${process.env.EXPO_PUBLIC_BASE_API_URL}/cart`

const OrderConfirmed = (props) => {
  const isGuest = useGuestUser()
  const dispatch = useAppDispatch()
  const { trans } = useLangTranslation()
  const { lng, currency } = useAuth()
  const { orderId } = props?.route?.params || {}
  const { navigation } = props

  const { orderDetails, loading } = useAppSelector((state) => state.getOrderDetailsSlice)
  const { orderDetails: guestOrderDetails } = useAppSelector((state) => state.postOrdersReducer)
  const guestOrder = guestOrderDetails?.[0]?.order_info || {}

  const [show, setShow] = useState(true)

  const cleanCart = async () => {
    const { payload } = await dispatch(emptyCart({ cartURL: `${cartURL}/empty` }))
    if (payload.success === 1) {
      dispatch(getCartProduct({ lng, currency }))
    }
  }

  const cleanupandNavigate = (routeName: string, params: any) => {
    cleanCart()
    dispatch(resetCoupon()) //clear coupon
    dispatch(clearOrderDetails()) //clear confirmed order details
    dispatch(clearOrderPreview()) //clear guest order details
    navigation.reset({
      index: 0,
      routes: [{ name: 'MyCart' }],
    })
    navigation.navigate(routeName, params)
  }

  useEffect(() => {
    if (!isGuest && orderId) {
      const URL = `${process.env.EXPO_PUBLIC_BASE_API_URL}/customerorders/${orderId}`
      dispatch(getOrderDetails({ URL, lng }))
    }
  }, [isGuest])

  return (
    <>
      <View style={commonStyles.container}>
        <View style={commonStyles.customHeaderContainer}>
          <Text style={commonStyles.headerName}>{trans('Cart').toUpperCase()}</Text>
        </View>
      </View>
      <View style={[commonStyles.hrLineFull, gapStyle.mt0]} />
      <View style={orderDetailsStyle.bodyContainer}>
        <View style={commonStyles.paddingSection}>
          <OrderIndicator currentStep={4} />
        </View>
        {loading ? (
          <OrderDetailsSkeleton />
        ) : (
          <ScrollView
            showsVerticalScrollIndicator={false}
            style={[commonStyles.paddingSection, gapStyle.mb20]}>
            <View style={orderConfirmedStyle.topPosition}>
              <View style={orderConfirmedStyle.iconAlign}>
                <Text style={orderConfirmedStyle.topIcon}>
                  <CustomSVG svgIcon={SuccessTickIcon} />
                </Text>
              </View>
              <Text style={orderConfirmedStyle.topText1}>{trans('Thank you')}</Text>
              <Text style={orderConfirmedStyle.topText2}>
                {trans('Your order has been received.')}
              </Text>
            </View>
            <View style={[orderDetailsStyle.deliveryCont, orderDetailsStyle.invoiceCont]}>
              <View style={orderDetailsStyle.deliverySubCont}>
                <Text style={orderDetailsStyle.deliveryText1}>{trans('Order Number')}</Text>
                <Text style={orderDetailsStyle.fieldContent}>
                  #{isGuest ? guestOrder?.order_no : orderDetails?.order_no}
                </Text>
              </View>
              <View style={[orderDetailsStyle.deliverySubCont, orderDetailsStyle.borderStyle]}>
                <Text style={orderDetailsStyle.deliveryText1}>{trans('Grand Total')}</Text>
                <Text style={orderDetailsStyle.fieldContent}>
                  {isGuest
                    ? `${guestOrder?.currency_code} ${guestOrder?.total}`
                    : orderDetails?.totals?.[orderDetails?.totals?.length - 1]?.text}
                </Text>
              </View>
            </View>
            <View style={orderDetailsStyle.deliveryCont}>
              <View style={orderDetailsStyle.deliverySubCont}>
                <Text style={orderDetailsStyle.deliveryText1}>{trans('Shipping Method')}</Text>
                <Text style={orderDetailsStyle.deliveryText2}>
                  {isGuest ? guestOrder?.shipping_method : orderDetails?.shipping_method}
                </Text>
              </View>
              <View style={[orderDetailsStyle.deliverySubCont, orderDetailsStyle.borderStyle]}>
                <Text style={orderDetailsStyle.deliveryText1}>{trans('Payment Method')}</Text>
                <Text style={orderDetailsStyle.deliveryText2}>
                  {isGuest ? guestOrder?.payment_method : orderDetails?.payment_method}
                </Text>
              </View>
            </View>
            <View style={[orderDetailsStyle.deliveryCont, gapStyle.mt15]}>
              <View style={orderDetailsStyle.deliverySubCont}>
                <Text style={orderDetailsStyle.deliveryText1}>{trans('Order Date')}</Text>
                <Text style={orderDetailsStyle.deliveryText2}>
                  {isGuest ? guestOrder?.date_added : orderDetails?.date_added}
                </Text>
              </View>
              <View style={[orderDetailsStyle.deliverySubCont, orderDetailsStyle.borderStyle]}>
                <Text style={orderDetailsStyle.deliveryText1}>{trans('Payment Status')}</Text>
                <Text
                  style={[
                    orderDetailsStyle.deliveryText2,
                    { color: dynamicStatusText(ORDER_STATUS_LABEL[orderDetails?.order_status_id]) },
                  ]}>
                  {trans(
                    ORDER_STATUS_LABEL[
                      isGuest
                        ? Number(guestOrder?.order_status_id || 0)
                        : orderDetails?.order_status_id
                    ],
                  )}
                </Text>
              </View>
            </View>

            {orderDetails?.payment_info?.remark && (
              <View style={[orderDetailsStyle.shippingAddressCont, gapStyle.mt15]}>
                <View style={{ borderBottomWidth: 1, borderColor: themeColor.lightBorder }}>
                  <Text style={orderDetailsStyle.shippingTitle}>
                    {trans(orderDetails?.payment_info?.title)}
                  </Text>
                </View>
                <View style={orderDetailsStyle.shippingTextCont}>
                  <HTMLView
                    stylesheet={htmlStyles}
                    value={orderDetails?.payment_info?.information?.trim()}
                  />
                  <HTMLView
                    stylesheet={htmlStyles}
                    value={orderDetails?.payment_info?.remark?.trim()}
                  />
                </View>
              </View>
            )}

            {!isGuest && (
              <View>
                <View style={orderDetailsStyle.orderProductsCont}>
                  <Pressable style={cartStyle.accordion} onPress={() => setShow(!show)}>
                    <Text style={orderSummaryStyle.allProducts}>{trans('Ordered Products')}</Text>
                    {show ? (
                      <CustomSVG
                        svgIcon={UpIcon}
                        height={iconSize}
                        width={iconSize}
                        fill={themeColor.secondText}
                      />
                    ) : (
                      <CustomSVG
                        svgIcon={DownIcon}
                        height={iconSize}
                        width={iconSize}
                        fill={themeColor.secondText}
                      />
                    )}
                  </Pressable>
                  {show && (
                    <View style={orderSummaryStyle.allProdctsContainer}>
                      <ScrollView nestedScrollEnabled={true}>
                        {orderDetails?.products?.map((item, index) => (
                          <SelectedProduct
                            key={index}
                            item={item}
                            index={index}
                            length={orderDetails?.products?.length - 1}
                          />
                        ))}
                      </ScrollView>
                    </View>
                  )}
                </View>
              </View>
            )}
          </ScrollView>
        )}
        <View style={orderDetailsStyle.bottomSectionRow}>
          <Pressable
            onPress={() => cleanupandNavigate('Home', {})}
            style={[orderConfirmedStyle.button, orderConfirmedStyle.homeBtn]}>
            <Text style={orderConfirmedStyle.homeBtnText}>{trans('Home')}</Text>
          </Pressable>
          {!isGuest && (
            <Pressable
              onPress={() =>
                cleanupandNavigate('OrderDetails', {
                  itemId: orderDetails?.order_id,
                })
              }
              style={[orderConfirmedStyle.button, orderConfirmedStyle.orderDetailBtn]}>
              <Text style={orderConfirmedStyle.orderDetailBtnText}>{trans('Detail View')}</Text>
            </Pressable>
          )}
        </View>
      </View>
    </>
  )
}

const htmlStyles = {
  p: {
    color: themeColor.primaryText,
    fontFamily: 'DMSans_400Regular',
    fontSize: dpr(14),
  },
}

export default OrderConfirmed
