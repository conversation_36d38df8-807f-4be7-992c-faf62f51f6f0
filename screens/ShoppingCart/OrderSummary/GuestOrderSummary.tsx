import React, { useEffect } from 'react'
import { useState } from 'react'
import { View, Text, KeyboardAvoidingView, ScrollView, Pressable, Platform } from 'react-native'

import { orderSummaryStyle } from './orderSummary.style'
import SelectedProduct from './SelectedProduct'
import { cartStyle } from '../shoppingCart.style'
import InOnScreenLoader from './InOnScreenLoader'
import OrderIndicator from '../OrderIndicator'
import useAuth from '@/hooks/useAuth'
import useCustomToast from '@/hooks/useCustomToast'
import useLangTranslation from '@/hooks/useLangTranslation'
import CustomSVG from '@/src/components/CustomSVG/CustomSVG'
import { getCartProduct } from '@/redux/slices/cart/getCartProducts'
import { postOrders } from '@/redux/slices/cart/order/postOrders'
import { gapStyle } from '@/screens/Utilities/CommonStyles/gap.style'
import dpr from '@/screens/Utilities/CustomStyleAttribute/dpr'
import BackNavigation from '@/screens/Utilities/CustomHeader/BackNavigation'
import UpIcon from '@/assets/svgs/dropdown/up.svg'
import DownIcon from '@/assets/svgs/dropdown/down.svg'
import { commonStyles } from '@/screens/Utilities/CommonStyles/common.styles'
import { getShippingCost } from '@/redux/slices/featureProducts/deliveryOption/getShipping'
import { useAppDispatch, useAppSelector } from '@/hooks/reduxHooks'
import { getPaymentMethods, PaymentMethod } from '@/redux/slices/cart/getPaymentMethods'
import { themeColor } from '@/theme/theme'
import CustomRadioButton from '@/src/components/CustomRadioButton/CustomRadioButton'
import { clearUserCart, setGuestCart } from '@/redux/slices/cart/storeItemInCart'
import { apiService } from '@/redux/slices/util/apiRequest'
import CustomButton from '@/src/components/CustomButton/CustomButton'
import SelectPaymentMethods from '@/src/components/SelectPaymentMethods/SelectPaymentMethods'

const URL = `${process.env.EXPO_PUBLIC_BASE_API_URL}/shippingmethods`
const guestOrderURL = `${process.env.EXPO_PUBLIC_BASE_API_URL}/guest-confirm`

const iconSize = dpr(16)

const GuestOrderSummary = (props) => {
  const { trans } = useLangTranslation()
  const { navigation } = props
  const dispatch = useAppDispatch()
  const { lng, currency } = useAuth()
  const showToast = useCustomToast()
  const { mutation } = apiService

  const {
    cartData,
    cartPrice,
    cartDataLength,
    loading: cartLoading,
  } = useAppSelector((state) => state.cartProductSlice)
  const { orderLoading } = useAppSelector((state) => state.postOrdersReducer)
  const { guestCart } = useAppSelector((state) => state.storeItemInCartSlice)
  const { shippingCost, loading: shippingCostLoading } = useAppSelector(
    (state) => state.getShippingReducer,
  )
  const { paymentMethods, loading: paymentMethodsLoading } = useAppSelector(
    (state) => state.paymentMethodsReducer,
  )
  const [shippingMethod, setShippingMethod] = useState(
    shippingCost?.shipping_methods?.[0]?.code || guestCart?.shipping_method,
  )
  const [paymentMethod, setPaymentMethod] = useState('')

  // accordion
  const [accordion, setAccordion] = useState({
    product: true,
    shippingMethod: true,
    paymentMethod: true,
  })

  const handleSelectShipping = async (code: string) => {
    setShippingMethod(code)
    dispatch(setGuestCart({ ...guestCart, shipping_method: code }))
    const result = await mutation(URL, 'POST', { shipping_method: code })
    if (result?.success === 1) {
      dispatch(getCartProduct({ lng, currency }))
    }
  }

  const handleProceedToPayment = async () => {
    if (!shippingMethod || !paymentMethod) {
      showToast({
        text1: trans('Please select one shipping / payment method.'),
        type: 'common',
        position: 'bottom',
        props: { variant: 'error' },
      })
      return
    }

    const orderData = {
      shipping_method: guestCart?.shipping_method,
      payment_method: paymentMethod as string,
      coupon: '',
      wallet_address: guestCart?.wallet_address ? guestCart?.wallet_address : '',
      comment: '',
      terms: guestCart?.terms ? '1' : '0',
      shipping_address: guestCart?.shipping_address,
    }

    try {
      const res = await dispatch(postOrders({ orderPostUrl: guestOrderURL, orderData }))
      const { success, data, error } = res?.payload || {}

      if (success === 1) {
        // offline payment
        if (data?.[0]?.is_online === false && data?.[0]?.order_id) {
          navigation.navigate('OrderDone', {
            orderId: data?.[0]?.order_id,
          })
        } else {
          if (data?.[0]?.payment_link) {
            navigation.navigate('PaymentWebview', {
              url: data?.[0]?.payment_link,
            })
          }
        }
      } else {
        showToast({
          text1: error ? trans(error) : trans('Order process failed. Please try again.'),
          type: 'common',
          position: 'bottom',
          props: { variant: 'error' },
        })
        navigation.navigate('MyCart')
      }
    } catch (error) {
      showToast({
        text1: trans('Order process failed. Please try again.'),
        type: 'common',
        position: 'bottom',
        props: { variant: 'error' },
      })
    }
  }

  useEffect(() => {
    ;(async () => {
      const result = await dispatch(getShippingCost({ url: URL, lng }))
      const { success, data } = result?.payload || {}
      if (success === 1) {
        handleSelectShipping(data?.shipping_methods?.[0]?.code)
      }
    })()
    dispatch(getPaymentMethods({ lng }))
  }, [dispatch, lng])

  return (
    <>
      {(cartLoading || shippingCostLoading || paymentMethodsLoading) && <InOnScreenLoader />}
      <BackNavigation
        navigationProps={props.navigation}
        routeName={trans('Cart')}
        capitalize={false}
      />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'padding'}
        style={commonStyles.globalScreenContainer}>
        <View style={commonStyles.paddingSection}>
          <OrderIndicator currentStep={3} />
        </View>
        <ScrollView showsVerticalScrollIndicator={false}>
          <View style={commonStyles.globalContainer}>
            <View style={cartStyle.accordionContainer}>
              <Pressable
                style={cartStyle.accordion}
                onPress={() => setAccordion({ ...accordion, product: !accordion.product })}>
                <Text style={orderSummaryStyle.allProducts}>
                  {trans('All Products ({{x}})', {
                    x: cartDataLength,
                  })}
                </Text>
                {accordion.product ? (
                  <CustomSVG
                    svgIcon={UpIcon}
                    height={iconSize}
                    width={iconSize}
                    fill={themeColor.secondText}
                  />
                ) : (
                  <CustomSVG
                    svgIcon={DownIcon}
                    height={iconSize}
                    width={iconSize}
                    fill={themeColor.secondText}
                  />
                )}
              </Pressable>
              {accordion.product && accordion.product && (
                <View style={orderSummaryStyle.allProdctsContainer}>
                  <ScrollView nestedScrollEnabled={true}>
                    {cartData?.map((item, index) => (
                      <SelectedProduct
                        key={index}
                        item={item}
                        index={index}
                        length={cartDataLength - 1}
                      />
                    ))}
                  </ScrollView>
                </View>
              )}
            </View>

            <View style={gapStyle.pb200}>
              <View style={orderSummaryStyle.priceContainer}>
                <Text style={cartStyle.fieldTitle}>{cartPrice?.[0]?.title}</Text>
                <Text style={cartStyle.fieldTitleBold}>{cartPrice?.[0]?.text}</Text>
              </View>

              <View style={cartStyle.accordionContainer}>
                <Pressable
                  style={cartStyle.accordion}
                  onPress={() =>
                    setAccordion({ ...accordion, shippingMethod: !accordion.shippingMethod })
                  }>
                  <Text style={orderSummaryStyle.allProducts}>
                    {trans('Select Shipping Method')}
                  </Text>
                  {accordion.shippingMethod ? (
                    <CustomSVG
                      svgIcon={UpIcon}
                      height={iconSize}
                      width={iconSize}
                      fill={themeColor.secondText}
                    />
                  ) : (
                    <CustomSVG
                      svgIcon={DownIcon}
                      height={iconSize}
                      width={iconSize}
                      fill={themeColor.secondText}
                    />
                  )}
                </Pressable>
                {accordion.shippingMethod && (
                  <View style={orderSummaryStyle.allProdctsContainer}>
                    {Object.keys(shippingCost).length > 0 && shippingCost.shipping ? (
                      shippingCost?.shipping_methods?.map((shipping) => (
                        <View
                          key={shipping?.code}
                          style={[
                            orderSummaryStyle.checkBoxContainer,
                            orderSummaryStyle.shippingContainer,
                          ]}>
                          <CustomRadioButton
                            id={1}
                            label={shipping?.title}
                            size={16}
                            selected={shippingMethod === shipping?.code}
                            color={
                              shippingMethod !== '' ? themeColor.primaryText : themeColor.secondText
                            }
                            containerStyle={[orderSummaryStyle.radioButton]}
                            labelStyle={
                              shippingMethod !== ''
                                ? orderSummaryStyle.radioText
                                : orderSummaryStyle.radioTextSelected
                            }
                            onPress={() => handleSelectShipping(shipping?.code)}
                          />
                          <Text style={cartStyle.fieldTitle}>{shipping?.text}</Text>
                        </View>
                      ))
                    ) : (
                      <View style={orderSummaryStyle.checkBoxContainer}>
                        <Text>{trans('Shipping is not requires for this order.')}</Text>
                      </View>
                    )}
                  </View>
                )}
              </View>

              <View style={gapStyle.mb20}>
                {cartPrice
                  .filter((price, index) => index > 0)
                  .map((priceSum, idx) => (
                    <View key={idx} style={orderSummaryStyle.totalPriceContainer}>
                      <Text style={cartStyle.fieldTitle}>{priceSum?.title}</Text>
                      <Text style={cartStyle.fieldTitleBold}>{priceSum?.text}</Text>
                    </View>
                  ))}
              </View>

              <SelectPaymentMethods
                paymentMethod={paymentMethod}
                setPaymentMethod={setPaymentMethod}
              />
            </View>
          </View>
        </ScrollView>
        <View style={cartStyle.checkoutCont}>
          <CustomButton
            onPress={handleProceedToPayment}
            disabled={orderLoading}
            loading={orderLoading}
            text={trans('Proceed to Payment')}
            buttonColor={themeColor.primaryButton}
            style={{ flex: 1, marginTop: 0 }}
          />
        </View>
      </KeyboardAvoidingView>
    </>
  )
}

export default GuestOrderSummary
