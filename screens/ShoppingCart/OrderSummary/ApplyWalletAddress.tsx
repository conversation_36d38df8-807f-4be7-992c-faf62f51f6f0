import React, { Dispatch, SetStateAction, useRef } from 'react'
import { Text, TextInput, View } from 'react-native'
import { accordionInputStyle, cartStyle } from '../shoppingCart.style'
import useLangTranslation from '@/hooks/useLangTranslation'
import { gapStyle } from '@/screens/Utilities/CommonStyles/gap.style'
interface ApplyCouponProps {
  walletAddress: string
  setWalletAddress: Dispatch<SetStateAction<string>>
}

const ApplyWalletAddress = (props: ApplyCouponProps) => {
  const { walletAddress, setWalletAddress } = props
  const { trans } = useLangTranslation()
  const ref = useRef<TextInput | null>(null)

  return (
    <View style={gapStyle.mt20}>
      <Text style={cartStyle.fieldTitle}>{trans('Wallet Address')}</Text>
      <View style={[cartStyle.accordionInputRow, gapStyle.mt0]}>
        <TextInput
          ref={ref}
          onFocus={() => {
            ref?.current?.focus()
          }}
          style={[accordionInputStyle(!!walletAddress), cartStyle.walletInput]}
          placeholder={trans('Wallet Address')}
          onChangeText={(text) => setWalletAddress(text)}
          value={walletAddress}
        />
      </View>
      <Text style={cartStyle.walletRemarks}>
        {trans(
          "Please provide your wallet address. Once the order is confirmed, we will send you the product's NFT.",
        )}
      </Text>
    </View>
  )
}

export default ApplyWalletAddress
