import { StyleSheet } from 'react-native'
import dpr from '../../Utilities/CustomStyleAttribute/dpr'
import { themeColor } from '@/theme/theme'
import { Styles } from '@/types/style'

export const billingInfoStyle = StyleSheet.create<Styles>({
  addAddressBtn: {
    borderColor: '#252525',
    borderStyle: 'dashed',
  },
  checkoutBtn: {
    bottom: dpr(10),
    left: dpr(20),
    position: 'absolute',
  },
  checkoutBtnBg: (shippingAddress) => ({
    backgroundColor: shippingAddress ? '#FCCA19' : '#F1F1F1',
  }),
  grandText: {
    color: '#8B8B8B',
    fontFamily: 'DMSans_700Bold',
    fontSize: dpr(18),
    textAlign: 'center',
  },
  grandTotal: {
    borderColor: '#E6E6E6',
    borderRadius: 6,
    borderWidth: 1,
    bottom: dpr(100),
    marginHorizontal: dpr(20),
    paddingVertical: dpr(20),
    position: 'absolute',
    width: dpr('wf') - dpr(20) * 2,
  },
  mt: {
    marginTop: 0,
  },
  otherAddressCont: {
    borderColor: '#E6E6E6',
    borderWidth: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    padding: dpr(10),
  },
  priceText: {
    color: '#252525',
    fontFamily: 'DMSans_700Bold',
    fontSize: dpr(33),
    textAlign: 'center',
  },
  selectAddressContainer: {
    marginTop: dpr(20),
  },
  selectAddressTitle: {
    color: themeColor.primaryText,
    fontFamily: 'DMSans_400Regular',
    fontSize: dpr(14),
    marginBottom: dpr(10),
  },
  text: {
    color: '#252525',
    fontFamily: 'DMSans_400Regular',
    fontSize: dpr(12),
    marginTop: dpr(16),
  },
})
