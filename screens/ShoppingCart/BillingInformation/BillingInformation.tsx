import React, { useEffect, useRef, useState } from 'react'
import { View, Text, Pressable, KeyboardAvoidingView, Platform } from 'react-native'
import { useSelector } from 'react-redux'
import { useIsFocused } from '@react-navigation/native'

import { FlatList } from 'react-native-gesture-handler'
import { orderSummaryStyle } from '../OrderSummary/orderSummary.style'
import { billingInfoStyle } from './billingInformation.style'
import OrderAddresses from './OrderAddresses'
import AddNewAddressBtn from './AddNewAddressBtn'
import InOnScreenLoader from '../OrderSummary/InOnScreenLoader'
import { bg, cartStyle } from '../shoppingCart.style'
import ApplyWalletAddress from '../OrderSummary/ApplyWalletAddress'
import OrderIndicator from '../OrderIndicator'
import TncCheckbox from '../TncCheckbox'
import useAuth from '@/hooks/useAuth'
import AddressSkeleton from '@/src/skeletons/screens/profile/AddressSkeleton'
import { resetCountry } from '@/redux/slices/user/address/getCountries'
import { resetState } from '@/redux/slices/user/address/getStates'
import { resetCity } from '@/redux/slices/user/address/getCity'
import useLangTranslation from '@/hooks/useLangTranslation'
import { commonStyles } from '@/screens/Utilities/CommonStyles/common.styles'
import BackNavigation from '@/screens/Utilities/CustomHeader/BackNavigation'
import { useAppDispatch, useAppSelector } from '@/hooks/reduxHooks'
import EmptyContent from '@/screens/Utilities/EmptyContent/EmptyContent'
import NoContentIcon from '@/assets/svgs/empty content/noAddress.svg'
import { regWalletAddress } from '@/components/Authentication/Registration/formValidation'
import useCustomToast from '@/hooks/useCustomToast'
import { setUserCart } from '@/redux/slices/cart/storeItemInCart'
import { getShipping } from '@/redux/slices/featureProducts/deliveryOption/getShipping'
import { gapStyle } from '@/screens/Utilities/CommonStyles/gap.style'
import { apiService } from '@/redux/slices/util/apiRequest'
import dpr from '@/screens/Utilities/CustomStyleAttribute/dpr'

const URL = `${process.env.EXPO_PUBLIC_BASE_API_URL}/shippingaddress`

const BillingInformation = (props) => {
  const dispatch = useAppDispatch()
  const { trans } = useLangTranslation()
  const { navigation } = props
  const { lng } = useAuth()
  const { cartData, cartPrice, cartLoading } = useSelector((state) => state.cartProductSlice)
  const { shippingAddresses, loading } = useAppSelector((state) => state.getShippingReducer)
  const { user } = useAppSelector((state) => state.signInReducer)
  const { mutation } = apiService

  const [walletAddress, setWalletAddress] = useState(user?.wallet_address || '')
  const showToast = useCustomToast()
  const isFocused = useIsFocused()

  const [selectedAddressId, setSelectedAddressId] = useState<null | number>(null)
  const [agree, setAgree] = useState(false)
  const flatListRef = useRef<FlatList>(null)

  useEffect(() => {
    let isMounted = true

    const fetchData = async () => {
      if (isMounted) {
        await dispatch(getShipping({ url: URL, lng }))
      }

      if (isMounted && cartData.length === 0) {
        navigation.navigate('MyCart')
      }
    }

    fetchData()

    return () => {
      isMounted = false
    }
  }, [cartData.length, dispatch, lng, navigation])

  useEffect(() => {
    if (isFocused) {
      dispatch(resetCountry())
      dispatch(resetState())
      dispatch(resetCity())
    }
  }, [isFocused])

  const handleSelectAddress = async (adressId: number) => {
    setSelectedAddressId(adressId)
  }

  const validateWalletAddress = (walletAddress: string) => {
    return regWalletAddress.test(walletAddress)
  }

  const handleAddressSubmit = async () => {
    if (selectedAddressId === null) {
      showToast({
        text1: trans('Create or select an address.'),
        type: 'common',
        position: 'bottom',
        props: { variant: 'error' },
      })
      return
    }

    const isWalletAddressValid = walletAddress ? validateWalletAddress(walletAddress) : true

    if (!isWalletAddressValid) {
      showToast({
        text1: trans('Enter a valid wallet address format.'),
        type: 'common',
        position: 'bottom',
        props: { variant: 'error' },
      })
      return
    }

    if (!agree) {
      showToast({
        text1: trans('You must agree to the terms and conditions.'),
        type: 'common',
        position: 'bottom',
        props: { variant: 'error' },
      })
      if (flatListRef.current) {
        flatListRef?.current?.scrollToOffset({
          animated: true,
          offset: dpr('hf'),
        })
      }
      return
    }

    //save shipping address
    const formData = {
      address_id: selectedAddressId,
    }
    const result = await mutation(`${URL}/existing`, 'POST', formData)

    if (result?.success === 1) {
      // Dispatch action to set user cart
      dispatch(
        setUserCart({
          shipping_address_id: selectedAddressId,
          wallet_address: walletAddress || null,
          terms: agree,
        }),
      )

      // Navigate to the next page
      props.navigation.navigate('OrderSummary')
    } else {
      showToast({
        text1: trans('Failed to save shipping address.'),
        type: 'common',
        position: 'bottom',
        props: { variant: 'error' },
      })
    }
  }

  const navigateToPrivacyPolicy = () => {
    props.navigation.navigate('PrivacyPolicy')
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={{ flex: 1 }}>
      {(cartLoading || loading) && <InOnScreenLoader />}
      <BackNavigation
        navigationProps={navigation}
        routeName={trans('Shipping Address')}
        capitalize={false}
      />
      <View style={commonStyles.globalContainer}>
        <OrderIndicator currentStep={2} />
        <AddNewAddressBtn />
        {loading ? (
          <AddressSkeleton />
        ) : shippingAddresses?.length > 0 ? (
          <View style={billingInfoStyle.selectAddressContainer}>
            <Text style={billingInfoStyle.selectAddressTitle}>{trans('Select Your Address')}</Text>
            <FlatList
              ref={flatListRef}
              data={shippingAddresses}
              keyboardDismissMode="on-drag"
              renderItem={({ item, index }) => (
                <OrderAddresses
                  item={item}
                  index={index}
                  navigation={props.navigation}
                  shippingAddress={selectedAddressId}
                  handleSelectAddress={handleSelectAddress}
                />
              )}
              keyExtractor={(item) => item.id}
              showsVerticalScrollIndicator={false}
              initialNumToRender={10}
              windowSize={10}
              contentContainerStyle={gapStyle.pb350}
              ListFooterComponent={
                <>
                  <ApplyWalletAddress
                    walletAddress={walletAddress}
                    setWalletAddress={setWalletAddress}
                  />
                  <TncCheckbox
                    agree={agree}
                    handleChange={(value) => setAgree(value)}
                    onPress={navigateToPrivacyPolicy}
                  />
                </>
              }
            />
          </View>
        ) : (
          <View style={billingInfoStyle.selectAddressContainer}>
            <FlatList
              ref={flatListRef}
              data={null}
              keyboardDismissMode="on-drag"
              renderItem={null}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={gapStyle.pb350}
              ListHeaderComponent={
                <EmptyContent Icon={NoContentIcon} text={trans('No Address Has Been Found')} />
              }
              ListFooterComponent={
                <>
                  <ApplyWalletAddress
                    walletAddress={walletAddress}
                    setWalletAddress={setWalletAddress}
                  />
                  <TncCheckbox
                    agree={agree}
                    handleChange={(value) => setAgree(value)}
                    onPress={navigateToPrivacyPolicy}
                  />
                </>
              }
            />
          </View>
        )}
        {cartData?.length > 0 && (
          <View style={cartStyle.checkoutCont}>
            <View>
              <Text style={cartStyle.totalPriceText}>{trans('Total')}</Text>
              <Text style={[cartStyle.totalPriceText, cartStyle.totalPriceTextSize]}>
                {cartPrice?.[cartPrice.length - 1]?.text}
              </Text>
            </View>
            <Pressable
              style={[cartStyle.proceedBtn, bg(cartPrice?.[1]?.text)]}
              onPress={handleAddressSubmit}>
              <Text style={cartStyle.proceedText}>{trans('Checkout')}</Text>
            </Pressable>
          </View>
        )}
      </View>
    </KeyboardAvoidingView>
  )
}

export default BillingInformation
