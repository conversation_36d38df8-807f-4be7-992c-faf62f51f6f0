import React, { useEffect, useRef, useState } from 'react'
import { View, Text, Pressable, KeyboardAvoidingView, Platform } from 'react-native'

import { FlatList } from 'react-native-gesture-handler'
import { billingInfoStyle } from './billingInformation.style'
import AddNewAddressBtn from './AddNewAddressBtn'
import InOnScreenLoader from '../OrderSummary/InOnScreenLoader'
import { bg, cartStyle } from '../shoppingCart.style'
import ApplyWalletAddress from '../OrderSummary/ApplyWalletAddress'
import OrderIndicator from '../OrderIndicator'
import TncCheckbox from '../TncCheckbox'
import useAuth from '@/hooks/useAuth'
import { resetCountry } from '@/redux/slices/user/address/getCountries'
import { resetState } from '@/redux/slices/user/address/getStates'
import { resetCity } from '@/redux/slices/user/address/getCity'
import useLangTranslation from '@/hooks/useLangTranslation'
import { commonStyles } from '@/screens/Utilities/CommonStyles/common.styles'
import BackNavigation from '@/screens/Utilities/CustomHeader/BackNavigation'
import { useAppDispatch, useAppSelector } from '@/hooks/reduxHooks'
import EmptyContent from '@/screens/Utilities/EmptyContent/EmptyContent'
import NoContentIcon from '@/assets/svgs/empty content/noAddress.svg'
import { regWalletAddress } from '@/components/Authentication/Registration/formValidation'
import useCustomToast from '@/hooks/useCustomToast'
import { gapStyle } from '@/screens/Utilities/CommonStyles/gap.style'
import SingleAddress from '@/screens/Profile/Address/SingleAddress'
import { apiService } from '@/redux/slices/util/apiRequest'
import { setGuestCart } from '@/redux/slices/cart/storeItemInCart'
import dpr from '@/screens/Utilities/CustomStyleAttribute/dpr'

const URL = `${process.env.EXPO_PUBLIC_BASE_API_URL}/shippingaddress/guest`

const GuestBillingInformation = (props) => {
  const dispatch = useAppDispatch()
  const { trans } = useLangTranslation()
  const { navigation } = props
  const { accessToken, lng } = useAuth()
  const { mutation } = apiService
  const {
    cartData,
    cartPrice,
    loading: cartLoading,
  } = useAppSelector((state) => state.cartProductSlice)
  const { guestCart } = useAppSelector((state) => state.storeItemInCartSlice)

  const [walletAddress, setWalletAddress] = useState('')
  const showToast = useCustomToast()

  const [agree, setAgree] = useState(false)
  const flatListRef = useRef<FlatList>(null)

  useEffect(() => {
    dispatch(resetCountry())
    dispatch(resetState())
    dispatch(resetCity())
  }, [])

  const validateWalletAddress = (walletAddress: string) => {
    return regWalletAddress.test(walletAddress)
  }

  const handleAddressSubmit = async () => {
    if (Object.keys(guestCart).length === 0) {
      showToast({
        text1: trans('Create or select an address.'),
        type: 'common',
        position: 'bottom',
        props: { variant: 'error' },
      })
      return
    }

    const isWalletAddressValid = walletAddress ? validateWalletAddress(walletAddress) : true

    if (!isWalletAddressValid) {
      showToast({
        text1: trans('Enter a valid wallet address format.'),
        type: 'common',
        position: 'bottom',
        props: { variant: 'error' },
      })
      return
    }

    if (!agree) {
      showToast({
        text1: trans('You must agree to the terms and conditions.'),
        type: 'common',
        position: 'bottom',
        props: { variant: 'error' },
      })
      if (flatListRef.current) {
        flatListRef?.current?.scrollToOffset({
          animated: true,
          offset: dpr('hf'),
        })
      }
      return
    } else {
      await dispatch(setGuestCart({ ...guestCart, terms: agree }))
    }

    const formData = {
      country_id: guestCart?.shipping_address?.country_id,
      zone_id: guestCart?.shipping_address?.zone_id,
      city_id: guestCart?.shipping_address?.city_id,
      county_id: guestCart?.shipping_address?.county_id,
    }

    const result = await mutation(URL, 'POST', formData)
    if (result?.success === 1) {
      // Navigate to the next page
      props.navigation.navigate('GuestOrderSummary')
    }
  }

  const navigateToPrivacyPolicy = () => {
    props.navigation.navigate('PrivacyPolicy')
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={{ flex: 1 }}>
      {cartLoading && <InOnScreenLoader />}
      <BackNavigation
        navigationProps={navigation}
        routeName={trans('Shipping Address')}
        capitalize={false}
      />
      <View style={commonStyles.globalContainer}>
        <OrderIndicator currentStep={2} />
        <AddNewAddressBtn />
        {Object.keys(guestCart).length > 0 ? (
          <View style={billingInfoStyle.selectAddressContainer}>
            <FlatList
              ref={flatListRef}
              data={null}
              renderItem={null}
              keyboardDismissMode="on-drag"
              ListHeaderComponent={
                <SingleAddress
                  item={guestCart?.shipping_address}
                  index={0}
                  navigation={navigation}
                  isDefault={true}
                />
              }
              keyExtractor={(item) => item.id}
              showsVerticalScrollIndicator={false}
              initialNumToRender={10}
              windowSize={10}
              contentContainerStyle={gapStyle.pb350}
              ListFooterComponent={
                <>
                  <ApplyWalletAddress
                    walletAddress={walletAddress}
                    setWalletAddress={setWalletAddress}
                  />
                  <TncCheckbox
                    agree={agree}
                    handleChange={(value) => setAgree(value)}
                    onPress={navigateToPrivacyPolicy}
                  />
                </>
              }
            />
          </View>
        ) : (
          <View style={billingInfoStyle.selectAddressContainer}>
            <FlatList
              ref={flatListRef}
              data={null}
              renderItem={null}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={gapStyle.pb350}
              ListHeaderComponent={
                <EmptyContent Icon={NoContentIcon} text={trans('No Address Has Been Found')} />
              }
              ListFooterComponent={
                <>
                  <ApplyWalletAddress
                    walletAddress={walletAddress}
                    setWalletAddress={setWalletAddress}
                  />
                  <TncCheckbox
                    agree={agree}
                    handleChange={(value) => setAgree(value)}
                    onPress={navigateToPrivacyPolicy}
                  />
                </>
              }
            />
          </View>
        )}
        {cartData?.length > 0 && (
          <View style={cartStyle.checkoutCont}>
            <View>
              <Text style={cartStyle.totalPriceText}>{trans('Total')}</Text>
              <Text style={[cartStyle.totalPriceText, cartStyle.totalPriceTextSize]}>
                {cartPrice?.[cartPrice.length - 1]?.text}
              </Text>
            </View>
            <Pressable
              style={[cartStyle.proceedBtn, bg(cartPrice?.[1]?.text)]}
              onPress={handleAddressSubmit}>
              <Text style={cartStyle.proceedText}>{trans('Checkout')}</Text>
            </Pressable>
          </View>
        )}
      </View>
    </KeyboardAvoidingView>
  )
}

export default GuestBillingInformation
