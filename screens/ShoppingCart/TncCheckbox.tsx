import React from 'react'
import { Pressable, StyleSheet, Text, View } from 'react-native'
import Checkbox from 'expo-checkbox'
import dpr from '../Utilities/CustomStyleAttribute/dpr'
import { cartStyle } from './shoppingCart.style'
import { Styles } from '@/types/style'
import useLangTranslation from '@/hooks/useLangTranslation'
import { themeColor } from '@/theme/theme'

type Props = {
  agree: boolean
  handleChange: (value: boolean) => void
  onPress: () => void
}

const TncCheckbox = (props: Props) => {
  const { agree, handleChange, onPress } = props
  const { trans } = useLangTranslation()

  const handlePress = () => {
    handleChange(!agree)
  }

  return (
    <Pressable onPress={handlePress}>
      <View style={styles.inputTextContainer}>
        <Checkbox value={agree} onValueChange={handleChange} style={styles.checkBox} />
        <View style={styles.remarkContainer}>
          <Text style={styles.remarkText}>{trans('I have read and agree to the ')}</Text>
          <Pressable onPress={onPress}>
            <Text style={styles.remarkLink}>{trans('Privacy Policy')}</Text>
          </Pressable>
        </View>
      </View>
    </Pressable>
  )
}

const styles = StyleSheet.create<Styles>({
  checkBox: {
    borderColor: themeColor.secondText,
    borderWidth: 1,
    height: dpr(16),
    marginRight: dpr(10),
    width: dpr(16),
  },
  inputTextContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    marginTop: dpr(15),
  },
  remarkContainer: {
    alignItems: 'flex-end',
    flexDirection: 'row',
  },
  remarkLink: {
    color: themeColor.primaryButton,
    fontFamily: 'DMSans_700Bold',
    fontSize: dpr(14),
    fontWeight: '700',
  },
  remarkText: {
    color: themeColor.secondText,
    fontFamily: 'DMSans_400Regular',
    fontSize: dpr(14),
  },
})

export default TncCheckbox
