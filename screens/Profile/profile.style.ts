import { StyleSheet } from 'react-native'
import dpr from '../Utilities/CustomStyleAttribute/dpr'
import { themeColor } from '@/theme/theme'

export const profileStyles = StyleSheet.create({
  amount: {
    color: themeColor.secondyButton,
    fontFamily: 'DMSans_700Bold',
    fontSize: dpr(20),
    textAlign: 'right',
  },
  editProfile: {
    alignItems: 'center',
    flexDirection: 'row',
    marginTop: dpr(12),
  },
  editText: {
    color: themeColor.primaryColoredText,
    fontFamily: 'Roboto_500Medium',
    fontSize: dpr(16),
    fontWeight: '500',
    marginLeft: dpr(10),
  },
  greetingText: {
    color: themeColor.primaryColoredText,
    fontFamily: 'DMSans_400Regular',
    fontSize: dpr(16),
  },
  greetings: {
    flexDirection: 'row',
  },
  greetingsName: {
    fontFamily: 'DMSans_700Bold',
    fontSize: dpr(16),
    fontWeight: '700',
  },
  hrLine: {
    borderBottomWidth: 1,
    borderColor: themeColor.hrLine,
  },
  image: {
    backgroundColor: themeColor.primaryBackground,
    borderRadius: 50,
    height: dpr(80),
    marginRight: dpr(15),
    width: dpr(80),
  },
  info: {
    color: '#898989',
    fontFamily: 'DMSans_500Medium',
    fontSize: dpr(18),
    paddingVertical: dpr(18),
  },
  infoBorder: {
    borderBottomWidth: 1,
    borderColor: themeColor.hrLine,
  },
  itemBox: {
    alignItems: 'center',
    flexDirection: 'row',
    gap: dpr(33),
    justifyContent: 'space-between',
  },
  name: {
    color: themeColor.primaryText,
    fontFamily: 'DMSans_400Regular',
    fontSize: dpr(16),
    textAlign: 'left',
  },
  profileContainer: {
    marginBottom: dpr(60),
  },
  profileDesc: {
    marginLeft: dpr(20),
  },
  profileIconPlaceholer: {
    backgroundColor: themeColor.primaryBackground,
    borderRadius: 50,
    height: dpr(94),
    width: dpr(94),
  },
  profileImageCont: {
    alignItems: 'center',
    backgroundColor: themeColor.secondaryBackground,
    borderRadius: dpr(20),
    flexDirection: 'row',
    padding: dpr(15),
  },
  profileInfo: {
    alignItems: 'flex-start',
    backgroundColor: themeColor.primaryBackground,
    borderColor: themeColor.primaryText,
    borderRadius: dpr(20),
    borderWidth: 1,
    flexDirection: 'column',
    gap: dpr(20),
    marginTop: dpr(15),
    paddingHorizontal: dpr(23),
    paddingVertical: dpr(20),
  },
  profileInfoCard: {
    alignItems: 'center',
    backgroundColor: themeColor.secondaryBackground,
    borderRadius: dpr(20),
    // flex: 1,
    flexBasis: '45%',
    flexDirection: 'column',
    gap: dpr(15),
    paddingVertical: dpr(20),
  },
  profileInfoCardBody: {
    color: themeColor.primaryColoredText,
    fontFamily: 'DMSans_400Regular',
    fontSize: dpr(16),
  },
  profileInfoContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: dpr(20),
    justifyContent: 'space-between',
    marginVertical: dpr(10),
  },
  quantity: {
    color: themeColor.primaryText,
    fontFamily: 'DMSans_700Bold',
    fontSize: dpr(18),
  },
  quantityContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: dpr(12),
  },
  quantityText: {
    color: '#898989',
    fontFamily: 'Roboto_500Medium',
    fontSize: dpr(18),
  },
  sectionTitle: {
    marginVertical: dpr(30),
  },
  title: {
    fontFamily: 'DMSans_700Bold',
    fontSize: dpr(18),
    fontWeight: '700',
    lineHeight: dpr(23),
  },
  titleHrLine: {
    borderBottomColor: themeColor.primaryButton,
    borderBottomWidth: 3,
    marginTop: dpr(5),
    width: dpr(70),
  },
  wallet: {
    color: '#DFDFDF',
    fontFamily: 'DMSans_700Bold',
    fontSize: dpr(20),
  },
  walletContainer: {
    alignItems: 'center',
    backgroundColor: themeColor.secondaryBackground,
    borderRadius: 6,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: dpr(30),
    padding: dpr(18),
  },
  walletText: {
    color: '#DFDFDF',
    fontFamily: 'DMSans_500Medium',
    fontSize: dpr(14),
    marginBottom: dpr(8),
  },
  wishlistCont: {
    borderColor: '#DFDFDF',
    borderLeftWidth: 1,
    borderRightWidth: 1,
    paddingHorizontal: dpr(23),
  },
  wrapper: {
    backgroundColor: '#fff',
    flex: 1,
  },
})
