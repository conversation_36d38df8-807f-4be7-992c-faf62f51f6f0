import { Dimensions, StyleSheet } from 'react-native'
import dpr from '../../Utilities/CustomStyleAttribute/dpr'
import { themeColor } from '@/theme/theme'
import { Styles } from '@/types/style'
const { width } = Dimensions.get('screen')

export const editProfileStyle = StyleSheet.create<Styles>({
  borderColor: (error) => ({
    borderColor: error ? '#E43147' : '#DFDFDF',
  }),
  changeInfo: {
    alignItems: 'center',
    backgroundColor: themeColor.primaryBackground,
    elevation: 24,
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: dpr(12),
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 12,
    },
    shadowOpacity: 0.6,
    width: width,
    zIndex: 999,
  },
  deleteAccount: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  deleteAccountText: {
    color: themeColor.primaryWarning,
    fontSize: dpr(14),
    fontWeight: '700',
    marginLeft: dpr(7),
    textDecorationLine: 'underline',
    // marginTop: dpr(20),
  },
  errorCont: {
    backgroundColor: '#F9E8E8',
    flexDirection: 'row',
    paddingHorizontal: dpr(20),
    paddingVertical: dpr(14),
  },
  errorText: {
    color: '#C8191C',
    fontFamily: 'DMSans_500Medium',
    fontSize: dpr(14),
    marginLeft: dpr(8, 'w'),
  },
  keyboardAvoidingViewFlex: {
    flex: 1,
  },
  label: {
    color: themeColor.primaryText,
    fontFamily: 'DMSans_500Medium',
    fontSize: dpr(15),
    marginBottom: dpr(9),
    marginTop: dpr(18),
  },
  radioCont: {
    flexDirection: 'row',
    gap: dpr(25),
  },
  radioText: {
    color: themeColor.secondText,
    fontFamily: 'Roboto_500Medium',
    fontSize: dpr(14),
    marginLeft: 10,
  },
})
