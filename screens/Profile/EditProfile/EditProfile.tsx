import React, { useState } from 'react'
import { Alert, KeyboardAvoidingView, Pressable, ScrollView, Text, View } from 'react-native'
import * as Clipboard from 'expo-clipboard'
import useAuth from '../../../hooks/useAuth'
import { login } from '../../../redux/slices/auth/signIn'
import { postUpdateUserProfile } from '../../../redux/slices/user/updateProfile/postUpdateProfile'
import BackNavigation from '../../Utilities/CustomHeader/BackNavigation'
import { editFormValidation } from './extraFunction'
import { editProfileStyle } from './editProfile.style'
import useCustomToast from '../../../hooks/useCustomToast'
import useLangTranslation from '../../../hooks/useLangTranslation'
import { commonStyles, formStyle } from '@/screens/Utilities/CommonStyles/common.styles'
import { useAppDispatch, useAppSelector } from '@/hooks/reduxHooks'
import { gapStyle } from '@/screens/Utilities/CommonStyles/gap.style'
import { getUserProfile } from '@/redux/slices/user/userProfile/userProfile'
import CustomTextInput from '@/src/components/CustomInput/CustomTextInput/CustomTextInput'
import CustomButton from '@/src/components/CustomButton/CustomButton'
import dpr from '@/screens/Utilities/CustomStyleAttribute/dpr'
import { themeColor } from '@/theme/theme'
import CustomSVG from '@/src/components/CustomSVG/CustomSVG'
import DeleteIcon from '@/assets/svgs/hub/icon_delete.svg'

const EditProfile = (props) => {
  const { trans } = useLangTranslation()
  const { lng } = useAuth()
  const dispatch = useAppDispatch()
  const { user } = useAppSelector((state) => state.signInReducer)
  const [updateData, setUpdateData] = useState(user)
  const [error, setError] = useState({})
  const { loading } = useAppSelector((state) => state.postUpdateUserProfile)
  const showToast = useCustomToast()
  const { jPushRegId } = useAppSelector((state) => state.updateUserProfile)

  const handleUpdateData = (name, text) => {
    setUpdateData({
      ...updateData,
      [name]: text,
    })
  }

  const handleProfileUpdate = async () => {
    const err = editFormValidation(updateData, trans)
    if (Object.keys(err).length > 0) {
      setError(err)
    } else {
      setError({})
      const { payload } = await dispatch(postUpdateUserProfile({ formData: updateData }))
      const { success, error: updateProfileError } = payload || {}
      if (success === 1) {
        showToast({
          text1: trans('Account updated successfully!'),
          type: 'common',
          position: 'bottom',
          props: { variant: 'success' },
        })
        const { payload: newProfilePayload } = await dispatch(getUserProfile({ lng }))
        dispatch(
          login({
            user: newProfilePayload?.data,
          }),
        )
        setUpdateData(newProfilePayload?.data)
        props.navigation.navigate('My Account')
      } else {
        if (updateProfileError.length > 0) {
          let errorMessage = ''
          updateProfileError.map((message: string) => {
            errorMessage += trans(message) + '\n'
          })
          showToast({
            text1: errorMessage,
            type: 'common',
            position: 'bottom',
            props: { variant: 'error' },
          })
        }
      }
    }
  }

  const copyJPushRegId = async () => {
    if (jPushRegId) {
      try {
        await Clipboard.setStringAsync(jPushRegId)
        Alert.alert('Copied ID to clipboard')
      } catch (error) {
        console.error('Failed to copy to clipboard:', error)
      }
    }
  }

  return (
    <KeyboardAvoidingView style={editProfileStyle.keyboardAvoidingViewFlex} behavior={'padding'}>
      <BackNavigation
        navigationProps={props.navigation}
        routeName={trans('edit profile')}
        capitalize={false}
      />
      <View style={[commonStyles.hrLineFull, , gapStyle.mt0]} />
      <ScrollView style={commonStyles.globalBodyContainer}>
        <CustomTextInput
          label={
            <Text style={formStyle.labelText}>
              <Text style={commonStyles.aterisk}>*</Text> {trans('First Name')}
            </Text>
          }
          isError={!!error?.firstname}
          error={error?.firstname}
          value={updateData.firstname}
          onChangeText={(text) => handleUpdateData('firstname', text)}
          isFirst={true}
        />

        <CustomTextInput
          label={
            <Text style={formStyle.labelText}>
              <Text style={commonStyles.aterisk}>*</Text> {trans('Last Name')}
            </Text>
          }
          isError={!!error?.lastname}
          error={error?.lastname}
          value={updateData.lastname}
          onChangeText={(text) => handleUpdateData('lastname', text)}
        />

        <CustomTextInput
          label={
            <Text style={formStyle.labelText}>
              <Text style={commonStyles.aterisk}>*</Text> {trans('Email')}
            </Text>
          }
          isError={!!error?.email}
          error={error?.email}
          value={updateData.email}
          onChangeText={(text) => handleUpdateData('email', text)}
        />

        <CustomTextInput
          label={<Text style={formStyle.labelText}>{trans('Telephone')}</Text>}
          isError={!!error?.telephone}
          error={error?.telephone}
          value={updateData.telephone}
          onChangeText={(text) => handleUpdateData('telephone', text)}
        />

        <CustomTextInput
          label={<Text style={formStyle.labelText}>{trans('Wallet Address')}</Text>}
          isError={!!error?.wallet_address}
          error={error?.wallet_address}
          value={updateData.wallet_address}
          onChangeText={(text) => handleUpdateData('wallet_address', text)}
        />

        <CustomButton
          loading={loading}
          disabled={loading}
          onPress={handleProfileUpdate}
          text={trans('Save')}
        />

        <Pressable onPress={copyJPushRegId} style={{ marginTop: dpr(40) }}>
          <View style={editProfileStyle.deleteAccount}>
            <CustomSVG fill={themeColor.primaryWarning} svgIcon={DeleteIcon} />
            <Text style={editProfileStyle.deleteAccountText}>{trans('Delete My Account')}</Text>
          </View>
        </Pressable>
      </ScrollView>
    </KeyboardAvoidingView>
  )
}

export default EditProfile
