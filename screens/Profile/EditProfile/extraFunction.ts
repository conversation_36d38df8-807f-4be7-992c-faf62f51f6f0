import {
  regEmail,
  regPhone,
  regWalletAddress,
} from '@/components/Authentication/Registration/formValidation'

interface FormError {
  firstname?: string
  lastname?: string
  email?: string
  telephone?: string
  wallet_address?: string
}

export const dateCount = []
for (let i = 1; i <= 31; i++) {
  dateCount.push({ name: i.toString() })
}
export const monthCount = [
  { name: '' },
  { name: 'January' },
  { name: 'February' },
  { name: 'March' },
  { name: 'April' },
  { name: 'May' },
  { name: 'June' },
  { name: 'July' },
  { name: 'August' },
  { name: 'September' },
  { name: 'October' },
  { name: 'November' },
  { name: 'December' },
]
export const convertMonth = (value) => {
  return monthCount[parseInt(value)].name
}

export const yearCount = []
for (let i = new Date().getFullYear(); i > 1900; i--) {
  yearCount.push({ name: i.toString() })
}

export const editFormValidation = (value, trans) => {
  const error = {}
  if (!value.firstname) {
    error.firstname = trans('First name is required')
  } else if (value.firstname > 32) {
    error.firstname = trans('First name is too large')
  } else if (value.firstname.length < 1) {
    error.firstname = trans('First name should be at least 1 characters')
  }

  if (!value.lastname) {
    error.lastname = trans('Last name is required')
  } else if (value.lastname > 32) {
    error.lastname = trans('Last name is too large')
  } else if (value.lastname.length < 1) {
    error.lastname = trans('Last name should be at least 1 characters')
  }

  if (!value.email) {
    error.email = trans('Email is required')
  } else if (regEmail.test(value.email) === false) {
    error.email = trans('Enter a valid email address')
  }

  if (!value.telephone) {
    error.telephone = trans('Telephone is required')
  } else if (
    regPhone.test(value.telephone.toString()) === false ||
    value.telephone.length < 3 ||
    value.telephone.length > 32
  ) {
    error.telephone = trans('Enter a valid phone format')
  }

  if (
    value.wallet_address &&
    value.wallet_address.length > 0 &&
    regWalletAddress.test(value.wallet_address) === false
  ) {
    error.wallet_address = trans('Enter a valid wallet address format')
  }

  return error
}

export const confirmGender = (value, setGender) => {
  if (value === 'Male') {
    setGender({
      male: value,
      female: '',
    })
  } else {
    setGender({
      male: '',
      female: value,
    })
  }
}

export const appendFormData = (updateData) => {
  const month =
    typeof updateData.month === 'number'
      ? updateData.month
      : monthCount.findIndex((item) => item?.name == updateData.month)
  const formData = new FormData()
  const birthday =
    updateData.year && updateData.month && updateData.day
      ? `${updateData.year}/${month}/${updateData.day}`
      : ''
  formData.append('name', `${updateData.firstName} ${updateData.lastName}`)
  formData.append('email', updateData.email)
  formData.append('gender', updateData.gender)
  formData.append('phone', updateData.phone)
  formData.append('birthday', birthday)
  formData.append('address', updateData.address)
  formData.append('image', updateData.attachment)
  return formData
}
