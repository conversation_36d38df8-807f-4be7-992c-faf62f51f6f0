import React from 'react'
import { Pressable, Text, View } from 'react-native'
import * as SecureStore from 'expo-secure-store'
import { profileStyles } from './profile.style'
import ProfilePicture from './ProfilePicture'
import dpr from '../Utilities/CustomStyleAttribute/dpr'
import { commonStyles } from '../Utilities/CommonStyles/common.styles'
import ProfileSkeleton from '@/src/skeletons/screens/profile/ProfileSkeleton'
import CustomSVG from '@/src/components/CustomSVG/CustomSVG'
import { useAppDispatch, useAppSelector } from '@/hooks/reduxHooks'
import useLangTranslation from '@/hooks/useLangTranslation'
import EmailIcon from '@/assets/svgs/profile/email.svg'
import PhoneIcon from '@/assets/svgs/profile/phone.svg'
import LogoutIcon from '@/assets/svgs/logout.svg'
import PasswordIcon from '@/assets/svgs/profile/password.svg'
import AddressIcon from '@/assets/svgs/profile/address.svg'
import OrderHistoryIcon from '@/assets/svgs/profile/order_history.svg'
import ProfileIcon from '@/assets/svgs/tabNaviagtion/profile.svg'
import MyAuctionIcon from '@/assets/svgs/profile/my_auction.svg'
import EditProfileIcon from '@/assets/svgs/profile/edit_profile.svg'
import resetReduxStore from '@/helper/resetReduxStore'
import { apiService } from '@/redux/slices/util/apiRequest'

const LOGOUT_URL = `${process.env.EXPO_PUBLIC_BASE_API_URL}/logout`

interface ProfileDashboardProps {
  setLogoutLoading: (value: boolean) => void
  navigation: any
}

const ProfileDashboard = (props: ProfileDashboardProps) => {
  const { setLogoutLoading } = props
  const dispatch = useAppDispatch()
  const { user } = useAppSelector((state) => state.signInReducer)
  const { jPushRegId, loading } = useAppSelector((state) => state.updateUserProfile)
  const { trans } = useLangTranslation()
  const { mutation } = apiService

  const handleLogout = async () => {
    setLogoutLoading(true)
    await mutation(LOGOUT_URL, 'POST', {
      reg_id: jPushRegId,
    })
    setLogoutLoading(false)
    resetReduxStore(dispatch)
    await SecureStore.deleteItemAsync('isLoggedIn')
  }

  return (
    <View style={[commonStyles.globalContainer, profileStyles.profileContainer]}>
      <View style={profileStyles.sectionTitle}>
        <Text style={profileStyles.title}>{trans('My Account')}</Text>
        <View style={profileStyles.titleHrLine} />
      </View>
      {loading ? (
        <ProfileSkeleton />
      ) : (
        <>
          <View style={profileStyles.profileImageCont}>
            <ProfilePicture user={user} />
            <View style={profileStyles.profileDesc}>
              <View style={profileStyles.greetings}>
                <Text style={profileStyles.greetingText}>
                  {trans('Hello')}
                  {', '}
                </Text>
                <Text
                  style={[
                    profileStyles.greetingText,
                    profileStyles.greetingsName,
                  ]}>{`${user?.firstname} ${user?.lastname}`}</Text>
              </View>
              <Pressable onPress={handleLogout}>
                <View style={profileStyles.editProfile}>
                  <CustomSVG svgIcon={LogoutIcon} width={dpr(16)} height={dpr(24)} />
                  <Text style={profileStyles.editText}>{trans('Logout')}</Text>
                </View>
              </Pressable>
            </View>
          </View>
          <View style={profileStyles.profileInfo}>
            <View style={profileStyles.itemBox}>
              <CustomSVG svgIcon={ProfileIcon} width={dpr(16)} height={dpr(16)} />
              <Text style={profileStyles.name}>
                {user?.firstname} {user?.lastname}
              </Text>
            </View>
            <View style={profileStyles.itemBox}>
              <CustomSVG svgIcon={EmailIcon} width={dpr(16)} height={dpr(16)} />
              <Text style={profileStyles.name}>{user?.email}</Text>
            </View>
            <View style={profileStyles.itemBox}>
              <CustomSVG svgIcon={PhoneIcon} width={dpr(16)} height={dpr(16)} />
              <Text style={profileStyles.name}>{user?.telephone}</Text>
            </View>
          </View>
        </>
      )}
      <View style={profileStyles.profileInfoContainer}>
        <ProfileInfoCard
          iconComponent={<CustomSVG svgIcon={EditProfileIcon} />}
          bodyContent={trans('Edit Account')}
          onPress={() => props.navigation.navigate('EditProfile')}
        />
        <ProfileInfoCard
          iconComponent={<CustomSVG svgIcon={PasswordIcon} />}
          bodyContent={trans('Change Password')}
          onPress={() => props.navigation.navigate('ChangePassword')}
        />
        <ProfileInfoCard
          iconComponent={<CustomSVG svgIcon={AddressIcon} />}
          bodyContent={trans('Address Book')}
          onPress={() => props.navigation.navigate('AddressBook')}
        />
        <ProfileInfoCard
          iconComponent={<CustomSVG svgIcon={OrderHistoryIcon} />}
          bodyContent={trans('Order History')}
          onPress={() => props.navigation.navigate('OrderHistory')}
        />
        <ProfileInfoCard
          iconComponent={<CustomSVG svgIcon={MyAuctionIcon} />}
          bodyContent={trans('My Auction')}
          onPress={() => props.navigation.navigate('MyAuctionListing')}
        />
      </View>
    </View>
  )
}
const ProfileInfoCard = ({
  iconComponent,
  bodyContent,
  onPress,
}: {
  iconComponent: React.ReactNode
  bodyContent: string
  onPress: () => void
}) => {
  return (
    <Pressable onPress={onPress} style={profileStyles.profileInfoCard}>
      {iconComponent}
      <Text style={profileStyles.profileInfoCardBody}>{bodyContent}</Text>
    </Pressable>
  )
}

export default ProfileDashboard
