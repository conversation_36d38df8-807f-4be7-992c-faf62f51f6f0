import React from 'react'
import { Image, Pressable, Text, View } from 'react-native'
import { useNavigation } from '@react-navigation/native'
import { NativeStackNavigationProp } from '@react-navigation/native-stack'

import listItemStyle from './listItem.style'
import ItemInfoRow from '@/src/components/Auction/ItemInfoRow'
import { MyLotListItem } from '@/types/auction'
import { AUCTION_LOT_STATUS_LABEL } from '@/screens/Auction/auctionStatus'
import { dynamicStatusText } from '@/screens/Utilities/CommonStyles/common.styles'
import dpr from '@/screens/Utilities/CustomStyleAttribute/dpr'
import useLangTranslation from '@/hooks/useLangTranslation'
import { RootStackParamList } from '@/types/navigations'
import CustomSVG from '@/src/components/CustomSVG/CustomSVG'
import RightIcon from '@/assets/svgs/settings/right_arrow.svg'
import productStyle from '@/screens/Utilities/CommonStyles/product.style'

const MyLotItem = ({ item }: { item: MyLotListItem }) => {
  const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>()
  const { trans } = useLangTranslation()
  return (
    <Pressable
      style={[listItemStyle.container, { borderBottomWidth: 0 }]}
      onPress={() => {
        navigation.navigate('MyLotDetail', {
          auctionId: item?.auction_id,
        })
      }}>
      <View style={[listItemStyle.upperContainer, listItemStyle.innnerPadding]}>
        <View style={listItemStyle.upperContainer}>
          <Image source={{ uri: item?.thumb }} style={listItemStyle.img} />
          <View>
            <Text style={productStyle.text} numberOfLines={1}>
              {item?.name}
            </Text>
            <Text style={productStyle.productNo}>
              {trans('product no')}
              {': '}
              {item?.model}
            </Text>
          </View>
        </View>
        <CustomSVG svgIcon={RightIcon} />
      </View>
      <ItemInfoRow
        configs={[
          {
            label: trans('Last Bid'),
            value: item?.last_bid_price,
          },
          {
            label: trans('Bid end time'),
            value: item?.bid_end_time,
            hasBorder: true,
          },
        ]}
      />
      <ItemInfoRow
        configs={[
          {
            label: trans('Status'),
            value: item?.status_str,
            customStyle: {
              color: dynamicStatusText(AUCTION_LOT_STATUS_LABEL[Number(item?.status)]),
            },
          },
        ]}
        isDoubleColumn={false}
        wrapperStyle={{ padding: dpr(10) }}
      />
    </Pressable>
  )
}

export default MyLotItem
