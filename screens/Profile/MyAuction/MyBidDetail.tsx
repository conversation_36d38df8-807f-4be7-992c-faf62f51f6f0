import React, { useState, useEffect } from 'react'
import { View, Text, FlatList } from 'react-native'
import { NativeStackScreenProps } from '@react-navigation/native-stack'
import listItemStyle from './listItem.style'
import auctionDetailStyle from '@/screens/Auction/auctionDetail.style'
import { orderDetailsStyle } from '@/screens/Profile/OrderHistory/OrderDetails/orderDetails.style'
import ItemCarousel from '@/screens/Home/FeaturedProducts/ProductDetails/ItemCarousel/ItemCarousel'
import ItemHeader from '@/screens/Home/FeaturedProducts/ProductDetails/ItemDetails/ItemSection/ItemHeader'
import { productDetailsStyle } from '@/screens/Home/FeaturedProducts/ProductDetails/productDetails.style'
import BackNavigation from '@/screens/Utilities/CustomHeader/BackNavigation'
import EmptyContent from '@/screens/Utilities/EmptyContent/EmptyContent'
import useAuth from '@/hooks/useAuth'
import useCustomToast from '@/hooks/useCustomToast'
import useLangTranslation from '@/hooks/useLangTranslation'
import NoWishlistIcon from '@/assets/svgs/empty content/noWishlist.svg'
import { commonStyles, dynamicStatusText } from '@/screens/Utilities/CommonStyles/common.styles'
import { RootStackParamList } from '@/types/navigations'
import { gapStyle } from '@/screens/Utilities/CommonStyles/gap.style'
import { useAppDispatch, useAppSelector } from '@/hooks/reduxHooks'
import { MyBidDetailProps } from '@/types/auction'
import AuctionDetailSkeleton from '@/src/skeletons/screens/auction/AuctionDetailSkeleton'
import { getMyAuctiondetail } from '@/redux/slices/auction/getMyAuctionDetail'
import CustomButton from '@/src/components/CustomButton/CustomButton'
import { orderConfirmedStyle } from '@/screens/ShoppingCart/OrderConfirmed/orderConfirmed.style'
import { themeColor } from '@/theme/theme'
import ItemInfoRow from '@/src/components/Auction/ItemInfoRow'
import { AUCTION_BIT_STATUS_LABEL } from '@/screens/Auction/auctionStatus'

type Props = NativeStackScreenProps<RootStackParamList, 'MyBidDetail'>

const MyBidDetail = ({ route, navigation }: Props) => {
  const { trans } = useLangTranslation()
  const { bidId } = route.params
  const showToast = useCustomToast()
  const dispatch = useAppDispatch()
  const { lng, currency } = useAuth()

  const { myAuctionDetail, loading } = useAppSelector((state) => state.getMyAuctionDetailReducer)
  const [itemCarouselData, setItemCarouselData] = useState({
    images: [
      {
        thumb: '',
        preview: '',
        popup: '',
      },
    ],
    activeImage: 0,
  })

  const [itemHeaderData, setItemHeaderData] = useState({
    name: '',
    productNo: '',
  })

  useEffect(() => {
    ;(async () => {
      const result = await dispatch(getMyAuctiondetail({ bidId, type: 'my-bid', lng, currency }))
      const { data, success } = result.payload || {}
      if (success === 1) {
        setItemCarouselData({
          ...itemCarouselData,
          images: [
            {
              thumb: data?.bid_info?.thumb,
              preview: data?.bid_info?.thumb,
              popup: data?.bid_info?.thumb,
            },
          ],
        })

        setItemHeaderData({
          name: data?.bid_info?.name,
          productNo: data?.bid_info?.model,
        })
      } else {
        showToast({
          text1: trans('Something Went Wrong Please Try Again!'),
          type: 'common',
          position: 'bottom',
          props: { variant: 'error' },
        })
      }
    })()
  }, [lng, bidId])

  return (
    <>
      <BackNavigation navigationProps={navigation} routeName={trans('My Bid')} capitalize={false} />
      <FlatList
        data={null}
        renderItem={null}
        ListHeaderComponent={
          <>
            {!loading && itemCarouselData?.images[0]?.preview?.length > 0 && (
              <ItemCarousel data={itemCarouselData} />
            )}
            <View style={[commonStyles.globalContainer, gapStyle.pb50]}>
              {loading ? (
                <AuctionDetailSkeleton />
              ) : (
                <>
                  <ItemHeader data={itemHeaderData} />
                  {!loading && Object.keys(myAuctionDetail)?.length === 0 ? (
                    <View>
                      <EmptyContent Icon={NoWishlistIcon} text={trans('No Data Found')} />
                    </View>
                  ) : (
                    <>
                      <Text
                        style={[
                          productDetailsStyle.pricing,
                          auctionDetailStyle.pricing,
                          gapStyle.mt25,
                        ]}>
                        {(myAuctionDetail as MyBidDetailProps)?.bid_info?.bidding_price}
                      </Text>

                      <View style={[listItemStyle.container, gapStyle.mt30]}>
                        <ItemInfoRow
                          configs={[
                            {
                              label: trans('Bid Time'),
                              value: (myAuctionDetail as MyBidDetailProps)?.bid_info?.date_added,
                            },
                            {
                              label: trans('Status'),
                              value: (myAuctionDetail as MyBidDetailProps)?.bid_info?.status_str,
                              customStyle: {
                                color: dynamicStatusText(
                                  AUCTION_BIT_STATUS_LABEL[
                                    (myAuctionDetail as MyBidDetailProps)?.bid_info?.status
                                  ],
                                ),
                              },
                              hasBorder: true,
                            },
                          ]}
                          wrapperStyle={{ borderBottomWidth: 0 }}
                        />
                      </View>
                    </>
                  )}
                </>
              )}
            </View>
          </>
        }
      />
      <View style={[orderDetailsStyle.bottomSectionRow, { marginTop: 'auto' }]}>
        <CustomButton
          onPress={() => navigation.goBack()}
          style={[orderConfirmedStyle.button, orderConfirmedStyle.homeBtn, gapStyle.mt0]}
          text={trans('Back')}
          textColor={themeColor.primaryText}
          loading={false}
          customIcon={require('@/assets/lottie/loadingBlack.json')}
        />
        {(myAuctionDetail as MyBidDetailProps)?.bid_info?.status === 2 && (
          <CustomButton
            onPress={() => navigation.navigate('AuctionPayNow', { bidId })}
            style={[orderConfirmedStyle.button, orderConfirmedStyle.orderDetailBtn, gapStyle.mt0]}
            text={trans('Pay')}
            loading={loading}
          />
        )}
      </View>
    </>
  )
}

export default MyBidDetail
