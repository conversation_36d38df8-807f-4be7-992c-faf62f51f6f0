import React, { useCallback, useEffect, useRef } from 'react'
import { useState } from 'react'
import { View, FlatList } from 'react-native'

import BottomSheet from '@gorhom/bottom-sheet'
import { NativeStackScreenProps } from '@react-navigation/native-stack'
import { RootParamList } from '@react-navigation/native'

import MyBidItem from './MyBidItem'
import MyLotItem from './MyLotItem'
import { productListingStyle } from '@/screens/Utilities/CommonStyles/productListing.style'
import { featureDisplayStyle } from '@/screens/Home/FeatureDIsplay/featureDisplay.style'
import EmptyContent from '@/screens/Utilities/EmptyContent/EmptyContent'
import CustomActiveIndicator from '@/src/components/CustomLoader/CustomActiveIndicator'
import useLangTranslation from '@/hooks/useLangTranslation'
import ProductSearchInput from '@/screens/Filter/FilterHome/ProductSearchInput'
import NoWishlistIcon from '@/assets/svgs/empty content/noWishlist.svg'
import SelectItemBottomSheet from '@/src/components/SelectItemBottomSheet/SelectItemBottomSheet'
import useAuth from '@/hooks/useAuth'

import { apiService } from '@/redux/slices/util/apiRequest'
import FadeInView from '@/src/components/FadeInView/FadeInView'
import CategoryTabs from '@/src/components/CategoryTabs/CategoryTabs'
import SelectInput from '@/src/components/CustomInput/SelectInput/SelectInput'
import CustomSVG from '@/src/components/CustomSVG/CustomSVG'
import DownArrowFill from '@/assets/svgs/downArrowFill.svg'
import { MyAuctionFilter, MyBidListItem, MyLotListItem } from '@/types/auction'
import dpr from '@/screens/Utilities/CustomStyleAttribute/dpr'
import { commonStyles } from '@/screens/Utilities/CommonStyles/common.styles'
import { gapStyle } from '@/screens/Utilities/CommonStyles/gap.style'
import { searchFilterStyles } from '@/screens/Filter/SearchFilter1/searchFilter1.style'
import BackNavigation from '@/screens/Utilities/CustomHeader/BackNavigation'
import auctionListingStyle from '@/screens/Auction/auctionListing.style'
import AuctionListItemSkeleton from '@/src/skeletons/screens/auction/AuctionListItemSkeleton'

const URL = `${process.env.EXPO_PUBLIC_BASE_API_URL}/account/auctions`

type Props = NativeStackScreenProps<RootParamList, 'MyAuctionListing'>

interface Sort {
  name: string
  key: string
  sortOrder: string | null
}

const initFilter: MyAuctionFilter = {
  filter_status: '',
  limit: '12',
  order: 'ASC',
  page: '1',
  cat: 'my-bid',
}

const MyAuctionListing = (props: Props) => {
  const { trans } = useLangTranslation()
  const { lng, currency } = useAuth()
  const allCategory = [
    {
      category_id: 1,
      parent_id: 0,
      name: trans('My Bid'),
      categories: [],
    },
    {
      category_id: 2,
      parent_id: 0,
      name: trans('My Lot'),
      categories: [],
    },
  ]
  const { query } = apiService

  const [list, setList] = useState<MyBidListItem[] | MyLotListItem[]>([])
  const [loading, setLoading] = useState(false)
  const [loadMore, setLoadMore] = useState(false)
  const [pagination, setPagination] = useState({
    total: 0,
    page: 0,
    limit: 0,
  })
  const [tab, setTab] = useState({
    parent_category_id: 1,
    child_category_id: 0,
  })

  //filter
  const sortByRef = useRef<BottomSheet>(null)
  const MyBidSort = [
    { name: trans('All'), key: '', sortOrder: null },
    { name: trans('Pending'), key: '1', sortOrder: null },
    { name: trans('Accept'), key: '2', sortOrder: null },
    { name: trans('Reject'), key: '3', sortOrder: null },
    { name: trans('Completed'), key: '4', sortOrder: null },
  ]
  const MyLotSort = [
    { name: trans('All'), key: '', sortOrder: null },
    { name: trans('In Auction'), key: '1', sortOrder: null },
    { name: trans('Sold'), key: '2', sortOrder: null },
    { name: trans('Ended'), key: '3', sortOrder: null },
  ]

  const [searchText, setSearchText] = useState('')
  const [sortBy, setSortBy] = useState<Sort>(
    tab.parent_category_id === 1 ? MyLotSort[0] : MyBidSort[0],
  )
  const [filters, setFilters] = useState(initFilter)

  const filterParams = (param: MyAuctionFilter) => {
    return new URLSearchParams(
      Object.entries(param).filter(([_, value]) => value !== '' && _ !== 'cat'),
    ).toString()
  }

  useEffect(() => {
    let isMounted = true
    if (isMounted) {
      handleFetchData(filters)
    }
    return () => {
      isMounted = false
    }
  }, [lng, currency])

  const handleFetchData = useCallback(
    async (param: MyAuctionFilter) => {
      const tmpFilters = filterParams(param)
      try {
        setLoading(true)
        const apiURL = tmpFilters.length > 0 ? `${URL}/${param?.cat}?${tmpFilters}` : URL
        const result = await query(apiURL, 'GET', {
          'X-Oc-Merchant-Language': lng,
          'X-Oc-Currency': currency,
        })
        const { data: resultData, success } = result || {}
        if (success === 1) {
          const { lists: resultList, pagination: resultPagination } = resultData
          setList(resultList)
          setPagination({
            total: resultPagination.total,
            page: resultPagination.page,
            limit: resultPagination.limit,
          })
          setFilters((prev) => ({ ...prev, page: String(resultPagination.page) }))
        }
      } catch (error) {
      } finally {
        setLoading(false)
      }
    },
    [filters],
  )

  const handleMoreData = async () => {
    let isMounted = true
    if (isMounted && !loading && pagination.total / Number(pagination.limit) > pagination.page) {
      try {
        setLoadMore(true)
        const tmpFilters = Object.assign({}, filters)
        tmpFilters.page = String(Number(tmpFilters.page) + 1)
        const newURL = new URLSearchParams(
          Object.entries(tmpFilters).filter(([_, value]) => value !== ''),
        ).toString()

        const type = tab.parent_category_id === 1 ? 'my-bid' : 'my-lot'
        const apiURL = newURL.length > 0 ? `${URL}/${type}?${newURL}` : URL

        const result = await query(apiURL, 'GET', {
          'X-Oc-Merchant-Language': lng,
          'X-Oc-Currency': currency,
        })
        const { data: resultData, success } = result || {}
        if (success === 1) {
          const { lists: resultList, pagination: resultPagination } = resultData
          const tmpList = Object.assign([], list)
          setList(tmpList.concat(resultList))
          setPagination({
            total: resultPagination.total,
            page: resultPagination.page,
            limit: resultPagination.limit,
          })
          setFilters((prev) => ({ ...prev, page: String(resultPagination?.page) }))
        }
      } catch (error) {
        console.error(error)
      } finally {
        setLoadMore(false)
      }
    }
    return () => {
      isMounted = false
    }
  }

  const handleRefresh = async () => {
    const tmpFilters = Object.assign({}, filters)
    tmpFilters.page = '1'
    setFilters((prev) => ({ ...prev, page: '1' }))
    handleFetchData(tmpFilters)
  }

  const handleCategoryChange = (parentId: number, childId: number) => {
    setTab({
      parent_category_id: parentId,
      child_category_id: childId,
    })
    const newFilters = {
      ...filters,
      cat: parentId === 1 ? 'my-bid' : 'my-lot',
      page: '1',
      filter_status: '',
    }
    setSortBy(parentId === 1 ? MyBidSort[0] : MyLotSort[0])
    setFilters(newFilters)
    handleFetchData(newFilters)
  }

  const handleSortBy = (item: Sort) => {
    setSortBy(item)
    // Determine the type of filter parameter
    let newFilters = { ...filters }
    newFilters = {
      ...newFilters,
      filter_status: item.key,
      page: '1',
    }

    setFilters(newFilters)
    handleFetchData(newFilters)
  }

  useEffect(() => {
    const newFilters = {
      ...filters,
      page: '1',
      search: searchText,
    }
    setFilters(newFilters)
    handleFetchData(newFilters)
  }, [searchText])

  return (
    <FadeInView>
      <BackNavigation
        navigationProps={props.navigation}
        routeName={trans('My Auction')}
        backActions={() => {
          props.navigation.navigate('DrawerStack', {
            screen: 'HomeScreen',
            params: { screen: 'My Account' },
          })
        }}
      />
      <View style={[commonStyles.hrLineFull, gapStyle.mt0]} />
      <ProductSearchInput searchText={searchText} setSearchText={setSearchText} />
      <View style={productListingStyle.categoryContainer}>
        <CategoryTabs activeTab={tab} categories={allCategory} onPress={handleCategoryChange} />
      </View>
      <View
        style={[
          featureDisplayStyle.const,
          productListingStyle.filterComponentsContainer,
          auctionListingStyle.filterContainer,
        ]}>
        <View style={searchFilterStyles.sortContainer}>
          <SelectInput
            style={productListingStyle.dropdown1BtnStyle}
            placeholder={trans('{{x}}', { x: sortBy.name })}
            onPress={() => sortByRef.current?.snapToIndex(0)}
            title={sortBy?.name}
            icon={<CustomSVG svgIcon={DownArrowFill} fill={'#2C2C2C'} />}
          />
        </View>
      </View>
      <View style={[productListingStyle.const, auctionListingStyle.listContainer]}>
        <View>
          {loading ? (
            <AuctionListItemSkeleton />
          ) : list?.length > 0 ? (
            tab.parent_category_id === 1 ? (
              <FlatList
                key={'lot-list'}
                data={list as MyBidListItem[]}
                keyExtractor={(_, i) => 'key' + i}
                renderItem={({ item }) => <MyBidItem item={item} />}
                showsVerticalScrollIndicator={false}
                initialNumToRender={12}
                windowSize={10}
                onEndReachedThreshold={1}
                onEndReached={handleMoreData}
                refreshing={false}
                onRefresh={handleRefresh}
                contentContainerStyle={{ rowGap: dpr(20) }}
              />
            ) : (
              <FlatList
                key={'auction-list'}
                data={list as MyLotListItem[]}
                keyExtractor={(_, i) => 'key' + i}
                renderItem={({ item }) => <MyLotItem item={item} />}
                showsVerticalScrollIndicator={false}
                initialNumToRender={12}
                windowSize={10}
                onEndReachedThreshold={1}
                onEndReached={handleMoreData}
                refreshing={false}
                onRefresh={handleRefresh}
                contentContainerStyle={{ rowGap: dpr(20) }}
              />
            )
          ) : (
            <FlatList
              data={null}
              renderItem={null}
              refreshing={false}
              onRefresh={handleRefresh}
              showsVerticalScrollIndicator={false}
              ListHeaderComponent={() => (
                <EmptyContent Icon={NoWishlistIcon} text={trans('No Auction Found')} />
              )}
            />
          )}
          {loadMore && <CustomActiveIndicator />}
        </View>
      </View>
      <SelectItemBottomSheet
        snapPoint={[480]}
        selectRef={sortByRef}
        data={tab.parent_category_id === 1 ? MyBidSort : MyLotSort}
        name={'SortBy'}
        onPress={handleSortBy}
      />
    </FadeInView>
  )
}

export default MyAuctionListing
