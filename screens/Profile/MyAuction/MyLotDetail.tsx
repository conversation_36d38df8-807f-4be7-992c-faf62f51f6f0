import React, { useState, useEffect } from 'react'
import { View, Text, FlatList, Pressable } from 'react-native'
import { NativeStackScreenProps } from '@react-navigation/native-stack'
import listItemStyle from './listItem.style'
import auctionDetailStyle from '@/screens/Auction/auctionDetail.style'
import { orderDetailsStyle } from '@/screens/Profile/OrderHistory/OrderDetails/orderDetails.style'
import ItemCarousel from '@/screens/Home/FeaturedProducts/ProductDetails/ItemCarousel/ItemCarousel'
import ItemHeader from '@/screens/Home/FeaturedProducts/ProductDetails/ItemDetails/ItemSection/ItemHeader'
import BackNavigation from '@/screens/Utilities/CustomHeader/BackNavigation'
import EmptyContent from '@/screens/Utilities/EmptyContent/EmptyContent'
import useAuth from '@/hooks/useAuth'
import useCustomToast from '@/hooks/useCustomToast'
import useLangTranslation from '@/hooks/useLangTranslation'
import NoWishlistIcon from '@/assets/svgs/empty content/noWishlist.svg'
import { commonStyles, dynamicStatusText } from '@/screens/Utilities/CommonStyles/common.styles'
import { RootStackParamList } from '@/types/navigations'
import { gapStyle } from '@/screens/Utilities/CommonStyles/gap.style'
import { useAppDispatch, useAppSelector } from '@/hooks/reduxHooks'
import { MyLotBidList, MyLotDetailProps } from '@/types/auction'
import AuctionDetailSkeleton from '@/src/skeletons/screens/auction/AuctionDetailSkeleton'
import { getMyAuctiondetail } from '@/redux/slices/auction/getMyAuctionDetail'
import CustomButton from '@/src/components/CustomButton/CustomButton'
import { orderConfirmedStyle } from '@/screens/ShoppingCart/OrderConfirmed/orderConfirmed.style'
import { themeColor } from '@/theme/theme'
import ItemInfoRow from '@/src/components/Auction/ItemInfoRow'
import { AUCTION_LOT_STATUS_LABEL } from '@/screens/Auction/auctionStatus'
import CustomRadioButton from '@/src/components/CustomRadioButton/CustomRadioButton'
import { orderSummaryStyle } from '@/screens/ShoppingCart/OrderSummary/orderSummary.style'
import { apiService } from '@/redux/slices/util/apiRequest'

type Props = NativeStackScreenProps<RootStackParamList, 'MyLotDetail'>

const URL = `${process.env.EXPO_PUBLIC_BASE_API_URL}/account/auctions/my-lot/audit-bid`

const UPDATE_BID_STATUS = {
  RECOVERED: 1,
  ACCEPTED: 2,
  REJECTED: 3,
  PAID: 4,
}

const MyBidDetail = ({ route, navigation }: Props) => {
  const { trans } = useLangTranslation()
  const { auctionId } = route.params
  const showToast = useCustomToast()
  const dispatch = useAppDispatch()
  const { lng, currency } = useAuth()
  const { mutation } = apiService

  const { loading } = useAppSelector((state) => state.getMyAuctionDetailReducer)
  const [myAuctionDetail, setMyAuctionDetail] = useState<MyLotDetailProps | MyLotBidList>(
    {} as MyLotDetailProps | MyLotBidList,
  )
  const [itemCarouselData, setItemCarouselData] = useState({
    images: [
      {
        thumb: '',
        preview: '',
        popup: '',
      },
    ],
    activeImage: 0,
  })

  const [itemHeaderData, setItemHeaderData] = useState({
    name: '',
    productNo: '',
  })

  const [acceptedBid, setAcceptedBid] = useState<MyLotBidList | null>(null)
  const [hasAcceptedBid, setHasAcceptedBid] = useState(false)

  const handleTransfer = async () => {
    //to-do
  }

  const updateBidStatus = async (status: number) => {
    if (acceptedBid === null) {
      return showToast({
        text1: trans('Please select a bid to accept'),
        type: 'common',
        position: 'bottom',
        props: { variant: 'error' },
      })
    }

    try {
      const result = await mutation(
        URL,
        'POST',
        {
          auction_id: acceptedBid?.auction_id,
          bid_id: acceptedBid?.bid_id,
          status: status,
        },
        lng,
        currency,
      )
      const { success, error } = result

      if (success === 1) {
        showToast({
          text1: trans('The bid status has been updated successfully.'),
          type: 'common',
          position: 'bottom',
          props: { variant: 'success' },
        })
        fetchAuctionDetail()
      } else {
        if (error.length > 0) {
          let errorMessage = ''
          error.map((message: string) => {
            errorMessage += trans(message) + '\n'
          })
          showToast({
            text1: errorMessage,
            type: 'common',
            position: 'bottom',
            props: { variant: 'error' },
          })
        }
      }
    } catch (error) {
      showToast({
        text1: 'Something Went Wrong Please Try Again!',
        type: 'common',
        position: 'bottom',
        props: { variant: 'error' },
      })
    }
  }

  useEffect(() => {
    fetchAuctionDetail()
  }, [lng, auctionId])

  const fetchAuctionDetail = async () => {
    const result = await dispatch(
      getMyAuctiondetail({ bidId: auctionId, type: 'my-lot', lng, currency }),
    )
    const { data, success } = result.payload || {}
    if (success === 1) {
      setMyAuctionDetail(data)
      setItemCarouselData({
        ...itemCarouselData,
        images: [
          {
            thumb: data?.info?.thumb,
            preview: data?.info?.thumb,
            popup: data?.info?.thumb,
          },
        ],
      })

      setItemHeaderData({
        name: data?.info?.name,
        productNo: data?.info?.model,
      })

      setHasAcceptedBid(
        data?.bid_list?.some(
          (item: MyLotBidList) => Number(item?.status) === UPDATE_BID_STATUS.ACCEPTED,
        ) || false,
      )
    } else {
      showToast({
        text1: trans('Something Went Wrong Please Try Again!'),
        type: 'common',
        position: 'bottom',
        props: { variant: 'error' },
      })
    }
  }

  return (
    <>
      <BackNavigation
        navigationProps={navigation}
        routeName={trans('My Lot')}
        capitalize={false}
        backActions={() => {
          navigation.navigate('MyAuctionListing')
        }}
      />
      <FlatList
        data={null}
        renderItem={null}
        ListHeaderComponent={
          <>
            {!loading && itemCarouselData?.images[0]?.preview?.length > 0 && (
              <ItemCarousel data={itemCarouselData} />
            )}
            <View style={[commonStyles.globalContainer, gapStyle.pb50]}>
              {loading ? (
                <AuctionDetailSkeleton />
              ) : (
                <>
                  <ItemHeader data={itemHeaderData} />
                  {!loading && Object.keys(myAuctionDetail)?.length === 0 ? (
                    <View>
                      <EmptyContent Icon={NoWishlistIcon} text={trans('No Data Found')} />
                    </View>
                  ) : (
                    <>
                      <View style={[listItemStyle.container, gapStyle.mt30]}>
                        <ItemInfoRow
                          configs={[
                            {
                              label: trans('Last Bid'),
                              value: (myAuctionDetail as MyLotDetailProps)?.info?.last_bid_price,
                            },
                            {
                              label: trans('Status'),
                              value: (myAuctionDetail as MyLotDetailProps)?.info?.status_str,
                              customStyle: {
                                color: dynamicStatusText(
                                  AUCTION_LOT_STATUS_LABEL[
                                    Number((myAuctionDetail as MyLotDetailProps)?.info?.status)
                                  ],
                                ),
                              },
                              hasBorder: true,
                            },
                          ]}
                          wrapperStyle={{ borderBottomWidth: 0 }}
                        />
                      </View>
                      <View style={[listItemStyle.container, gapStyle.mt20]}>
                        <View style={auctionDetailStyle.labelBorderContainer}>
                          <Text style={auctionDetailStyle.text}>{trans('Bid Details')}</Text>
                        </View>
                        <View style={auctionDetailStyle.bidDetailRowContainer}>
                          <View style={auctionDetailStyle.bidDetailRow}>
                            <Text
                              style={
                                auctionDetailStyle.bidDetailLabel
                              }>{`${trans('Starting price')}:`}</Text>
                            <Text style={[auctionDetailStyle.text, { fontWeight: '500' }]}>
                              {(myAuctionDetail as MyLotDetailProps)?.info?.starting_price_str}
                            </Text>
                          </View>
                          <View style={auctionDetailStyle.bidDetailRow}>
                            <Text
                              style={
                                auctionDetailStyle.bidDetailLabel
                              }>{`${trans('Reserve Price')}:`}</Text>
                            <Text style={[auctionDetailStyle.text, { fontWeight: '500' }]}>
                              {(myAuctionDetail as MyLotDetailProps)?.info?.reserve_price_str}
                            </Text>
                          </View>
                          <View style={auctionDetailStyle.bidDetailRow}>
                            <Text
                              style={
                                auctionDetailStyle.bidDetailLabel
                              }>{`${trans('Bid Increment')}:`}</Text>
                            <Text style={[auctionDetailStyle.text, { fontWeight: '500' }]}>
                              {(myAuctionDetail as MyLotDetailProps)?.info?.bid_increment}
                            </Text>
                          </View>
                          <View style={auctionDetailStyle.bidDetailRow}>
                            <Text
                              style={
                                auctionDetailStyle.bidDetailLabel
                              }>{`${trans('Bid end time')}:`}</Text>
                            <Text style={[auctionDetailStyle.text, { fontWeight: '500' }]}>
                              {(myAuctionDetail as MyLotDetailProps)?.info?.bid_end_time}
                            </Text>
                          </View>
                        </View>
                      </View>
                      {(myAuctionDetail as MyLotDetailProps)?.bid_list?.length > 0 && (
                        <>
                          <View style={gapStyle.mt20}>
                            <Text style={auctionDetailStyle.boldHeader}>
                              {trans('Auction History')}
                            </Text>
                            <View style={auctionDetailStyle.boldHeaderLine} />
                          </View>
                          <Text style={[auctionDetailStyle.text, gapStyle.mt10]}>
                            {trans('Please select the bid you want to accept')}
                          </Text>
                          {(myAuctionDetail as MyLotDetailProps)?.bid_list?.map((item) => (
                            <Pressable
                              style={[
                                listItemStyle.container,
                                gapStyle.mt20,
                                (UPDATE_BID_STATUS.RECOVERED === Number(item?.status) &&
                                  hasAcceptedBid) ||
                                UPDATE_BID_STATUS.REJECTED === Number(item?.status)
                                  ? { opacity: 0.5 }
                                  : {},
                              ]}
                              key={item?.bid_id}
                              onPress={() => {
                                setAcceptedBid(item)
                              }}>
                              <View style={auctionDetailStyle.labelBorderContainer}>
                                <CustomRadioButton
                                  id={item?.bid_id}
                                  label={''}
                                  size={16}
                                  selected={acceptedBid?.bid_id === item?.bid_id}
                                  color={
                                    acceptedBid?.bid_id === item?.bid_id
                                      ? themeColor.primaryButton
                                      : themeColor.secondText
                                  }
                                  containerStyle={orderSummaryStyle.radioButton}
                                  labelStyle={
                                    acceptedBid?.bid_id === item?.bid_id
                                      ? orderSummaryStyle.radioTextSelected
                                      : orderSummaryStyle.radioText
                                  }
                                  onPress={() => {
                                    setAcceptedBid(item)
                                  }}
                                />
                              </View>
                              <View style={auctionDetailStyle.bidDetailRowContainer}>
                                <View style={auctionDetailStyle.bidDetailRow}>
                                  <Text style={auctionDetailStyle.text}>{`${trans('Time')}`}</Text>
                                  <Text style={auctionDetailStyle.bidDetailLabel}>
                                    {item?.date_added}
                                  </Text>
                                </View>
                                <View style={auctionDetailStyle.bidDetailRow}>
                                  <Text
                                    style={auctionDetailStyle.text}>{`${trans('Bidders')}`}</Text>
                                  <Text style={auctionDetailStyle.bidDetailLabel}>
                                    {item?.bid_fullname}
                                  </Text>
                                </View>
                                <View style={auctionDetailStyle.bidDetailRow}>
                                  <Text
                                    style={
                                      auctionDetailStyle.text
                                    }>{`${trans('Biding Price')}`}</Text>
                                  <Text style={auctionDetailStyle.bidDetailLabel}>
                                    {item?.bidding_price_str}
                                  </Text>
                                </View>
                                <View style={auctionDetailStyle.bidDetailRow}>
                                  <Text
                                    style={auctionDetailStyle.text}>{`${trans('Status')}`}</Text>
                                  <Text style={auctionDetailStyle.bidDetailLabel}>
                                    {item?.status_str}
                                  </Text>
                                </View>
                              </View>
                            </Pressable>
                          ))}
                        </>
                      )}
                    </>
                  )}
                </>
              )}
            </View>
          </>
        }
      />
      {acceptedBid && (
        <>
          {(UPDATE_BID_STATUS.ACCEPTED || UPDATE_BID_STATUS.PAID) !==
            Number(acceptedBid?.status) && (
            <View style={[orderDetailsStyle.bottomSectionRow, { marginTop: 'auto' }]}>
              <CustomButton
                disabled={
                  hasAcceptedBid || UPDATE_BID_STATUS.REJECTED === Number(acceptedBid?.status)
                }
                onPress={() => updateBidStatus(UPDATE_BID_STATUS.REJECTED)}
                style={[
                  orderConfirmedStyle.button,
                  orderConfirmedStyle.cancelBtn,
                  gapStyle.mt0,
                  hasAcceptedBid || UPDATE_BID_STATUS.REJECTED === Number(acceptedBid?.status)
                    ? commonStyles.disabledButton
                    : {},
                ]}
                text={trans('Reject')}
                loading={false}
              />
              {(myAuctionDetail as MyLotDetailProps)?.bid_list?.length > 0 && (
                <CustomButton
                  disabled={
                    hasAcceptedBid || UPDATE_BID_STATUS.REJECTED === Number(acceptedBid?.status)
                  }
                  onPress={() => updateBidStatus(UPDATE_BID_STATUS.ACCEPTED)}
                  style={[
                    orderConfirmedStyle.button,
                    orderConfirmedStyle.orderDetailBtn,
                    gapStyle.mt0,
                    hasAcceptedBid || UPDATE_BID_STATUS.REJECTED === Number(acceptedBid?.status)
                      ? commonStyles.disabledButton
                      : {},
                  ]}
                  text={trans('Accept')}
                  loading={loading}
                />
              )}
            </View>
          )}
          {(UPDATE_BID_STATUS.ACCEPTED || UPDATE_BID_STATUS.PAID) ===
            Number(acceptedBid?.status) && (
            <View style={[orderDetailsStyle.bottomSectionRow, { marginTop: 'auto' }]}>
              <CustomButton
                disabled={UPDATE_BID_STATUS.PAID !== Number(acceptedBid?.status)}
                onPress={handleTransfer}
                style={[
                  orderConfirmedStyle.button,
                  orderConfirmedStyle.orderDetailBtn,
                  gapStyle.mt0,
                ]}
                text={trans('Transfter')}
                loading={loading}
              />
            </View>
          )}
        </>
      )}
    </>
  )
}

export default MyBidDetail
