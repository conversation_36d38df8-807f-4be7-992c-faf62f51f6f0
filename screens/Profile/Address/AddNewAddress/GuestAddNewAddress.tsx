import React, { useEffect, useRef, useState } from 'react'
import {
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  Pressable,
  ScrollView,
  Text,
  View,
} from 'react-native'
import PhoneInput from 'react-native-phone-number-input'

import { addNewAddressStyle, dropdown1BtnStyle } from './addNewAddress.style'
import { isError, initErrorText, initialState } from './initializeValue'
import { profileStyles } from '@/screens/Profile/profile.style'
import { editProfileStyle } from '@/screens/Profile/EditProfile/editProfile.style'
import BackNavigation from '@/screens/Utilities/CustomHeader/BackNavigation'
import CustomSpinner from '@/screens/Utilities/CustomSpinner/CustomSpinner'
import dpr from '@/screens/Utilities/CustomStyleAttribute/dpr'
import useAuth from '@/hooks/useAuth'
import { getMyAddress } from '@/redux/slices/user/address/getMyAddress'
import useCustomToast from '@/hooks/useCustomToast'
import SelectItemBottomSheet from '@/src/components/SelectItemBottomSheet/SelectItemBottomSheet'
import SelectInput from '@/src/components/CustomInput/SelectInput/SelectInput'
import DownArrowFill from '@/assets/svgs/downArrowFill.svg'
import { getHKArea } from '@/redux/slices/user/address/getCountries'
import { City, getZones, Zone } from '@/redux/slices/user/address/getStates'
import CustomSmallLoader from '@/src/components/CustomLoader/CustomSmallLoader'
import CustomTextInput from '@/src/components/CustomInput/CustomTextInput/CustomTextInput'

import useLangTranslation from '@/hooks/useLangTranslation'
import CustomSVG from '@/src/components/CustomSVG/CustomSVG'
import DeleteIcon from '@/assets/svgs/trash.svg'
import ErrorIcon from '@/assets/svgs/errorIcon.svg'
import { commonStyles, formStyle, mb5 } from '@/screens/Utilities/CommonStyles/common.styles'
import { gapStyle } from '@/screens/Utilities/CommonStyles/gap.style'
import { registrationStyle } from '@/components/Authentication/Registration/registration.style'
import { themeColor } from '@/theme/theme'
import { useAppDispatch, useAppSelector } from '@/hooks/reduxHooks'
import { regEmail, regPhone } from '@/components/Authentication/Registration/formValidation'
import { setGuestCart } from '@/redux/slices/cart/storeItemInCart'
import { apiService } from '@/redux/slices/util/apiRequest'
import CustomButton from '@/src/components/CustomButton/CustomButton'

const GuestAddNewAddress = (props) => {
  const { trans } = useLangTranslation()
  const { isUpdate, item, index = {} } = props.route.params || {}
  const showToast = useCustomToast()
  const dispatch = useAppDispatch()
  const { lng } = useAuth()
  const { mutation } = apiService

  // Redux state
  const { loading } = useAppSelector((state) => state.postNewAddress)
  const { hkArea, loading: countryLoading } = useAppSelector((state) => state.getCountries)
  const { zones, loading: zoneLoading } = useAppSelector((state) => state.getStates)

  // Component state
  const zoneSelectRef = useRef(null)
  const [zoneInfo, setZoneInfo] = useState({} as Zone)

  const citySelectRef = useRef(null)
  const [cityInfo, setCityInfo] = useState({} as City)

  const [deleteLoading, setDeleteLoading] = useState(false)
  const [address, setAddress] = useState({ ...initialState, email: '' })
  const [error, setError] = useState(isError)
  const [errorText, setErrorText] = useState(initErrorText)

  const [zoneSheetIndex, setZoneSheetIndex] = useState(-1)
  const [citySheetIndex, setCitySheetIndex] = useState(-1)

  // const resetPhoneRef = useRef({})
  const firstNameRef = useRef(false)
  const lastNameRef = useRef(false)
  const emailRef = useRef(false)
  const phoneRef = useRef(false)
  const address1Ref = useRef(false)
  const cityRef = useRef(false)
  const zoneRef = useRef(false)
  const countryRef = useRef(false)

  const { firstname, lastname, email, telephone, address_1, zone_id, city_id } = address

  useEffect(() => {
    if (isUpdate) {
      setAddress(item)
      dispatch(getZones({ lng, zone_id: Number(item?.zone_id) }))
    }
    dispatch(getHKArea({ lng }))
  }, [])

  useEffect(() => {
    setError({
      ...error,
      city_id: cityRef.current,
    })

    if (zoneInfo?.zone_id) {
      dispatch(getZones({ lng, zone_id: Number(zoneInfo?.zone_id) }))
    }
  }, [zoneInfo?.zone_id])

  const handleValidationError = () => {
    firstNameRef.current = firstname === '' ? true : false
    lastNameRef.current = lastname === '' ? true : false
    emailRef.current = email === '' ? true : false
    phoneRef.current =
      telephone === '' || regPhone.test(address?.telephone) === false ? true : false
    address1Ref.current =
      address_1 === '' || address.address_1.length < 3 || address.address_1.length > 128
        ? true
        : false
    zoneRef.current = zone_id === '' ? true : false
    cityRef.current = city_id === '' ? true : false
    setError({
      ...error,
      firstname: firstNameRef.current,
      lastname: lastNameRef.current,
      email: emailRef.current,
      telephone: phoneRef.current,
      address_1: address1Ref.current,
      city_id: cityRef.current,
      zone_id: zoneRef.current,
      country_id: countryRef.current,
    })

    if (
      firstNameRef.current ||
      lastNameRef.current ||
      emailRef.current ||
      phoneRef.current ||
      address1Ref.current ||
      cityRef.current ||
      zoneRef.current
    ) {
      return true
    }
  }
  const handleAddressInfo = (name, text) => {
    if (name === 'email' && regEmail.test(text) === false) {
      setError({
        ...error,
        [name]: true,
      })
      setErrorText({
        ...errorText,
        [name]: trans('Enter a valid email format'),
      })
    } else if (name === 'telephone' && regPhone.test(text) === false) {
      setError({
        ...error,
        [name]: true,
      })
      setErrorText({
        ...errorText,
        [name]: trans('Enter a valid phone format'),
      })
    } else if (name === 'address_1' && (text.length < 3 || text.length > 128)) {
      setError({
        ...error,
        [name]: true,
      })
      setErrorText({
        ...errorText,
        [name]: trans('The street address must be between 3 and 128 characters.'),
      })
    } else {
      setError({
        ...error,
        [name]: false,
      })
      setErrorText({
        ...errorText,
        [name]: '',
      })
    }
    if (name === 'zone_id') {
      setAddress({
        ...address,
        [name]: text,
        city: '',
        city_id: '',
      })
      setCityInfo({} as City)
    } else {
      setAddress({
        ...address,
        [name]: text,
      })
    }
  }

  const handleAddNewAddress = async () => {
    const err = handleValidationError()

    if (!err) {
      try {
        dispatch(
          setGuestCart({
            shipping_address: {
              ...initialState,
              firstname: address.firstname,
              lastname: address.lastname,
              email: address.email,
              telephone: address.telephone,
              address_1: address.address_1,
              zone: zoneInfo.name || address.zone,
              zone_id: Number(address.zone_id),
              city: cityInfo.name || address.city,
              city_id: Number(address.city_id),
              postcode: address.postcode,
              country: 'Hong Kong',
            },
          }),
        )

        showToast({
          text1: isUpdate
            ? trans('Address has been edited successfully.')
            : trans('Address has been added successfully.'),
          type: 'common',
          position: 'bottom',
          props: { variant: 'success' },
        })
        setAddress({ ...initialState, email: '' })
        setError(isError)
        setZoneInfo({} as Zone)
        setCityInfo({} as City)
        props.navigation.goBack()
      } catch (error) {
        showToast({
          text1: trans('Something Went Wrong Please Try Again!'),
          type: 'common',
          position: 'bottom',
          props: { variant: 'error' },
        })
      }
    }
  }

  const handleDeleteAddress = async (id) => {
    const DELETE_URL = `${process.env.EXPO_PUBLIC_BASE_API_URL}/account/address/${id}`
    setDeleteLoading(true)
    const data = await mutation(DELETE_URL, 'DELETE', {})
    if (data.success === 1) {
      props.navigation.goBack()
      dispatch(getMyAddress({ lng }))
      showToast({
        text1: trans('Address has been deleted'),
        type: 'common',
        position: 'bottom',
        props: { variant: 'success' },
      })
    }
  }

  const [addPhoneNumber, setAddPhoneNumber] = useState([1])
  const handleDeletePhoneNumber = (index) => {
    const result = addPhoneNumber.filter((item) => item !== index)
    setAddPhoneNumber(result)
  }

  const handleZoneSelect = (state: Zone) => {
    handleAddressInfo('zone_id', state.zone_id)
    setZoneInfo(state)
  }

  const handleCitySelect = (city: City) => {
    handleAddressInfo('city_id', city.city_id)
    setCityInfo(city)
  }

  return (
    <>
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
        <BackNavigation
          navigationProps={props.navigation}
          routeName={isUpdate ? trans('Address {{x}}', { x: index + 1 }) : trans('Add New Address')}
          capitalize={true}
        />
        <View style={[commonStyles.hrLineFull, gapStyle.mt0]} />
        <View>
          {error.firstname ||
          error.telephone ||
          error.address_1 ||
          error.city_id ||
          error.country_id ? (
            <View style={editProfileStyle.errorCont}>
              <CustomSVG svgIcon={ErrorIcon} />
              <Text style={editProfileStyle.errorText}>
                {errorText.telephone ||
                  errorText?.address_1 ||
                  trans('Please fill in all the required fields')}
              </Text>
            </View>
          ) : null}
        </View>
        <ScrollView showsVerticalScrollIndicator={false} keyboardShouldPersistTaps="always">
          <View style={commonStyles.globalContainer}>
            <View style={profileStyles.sectionTitle}>
              <Text style={profileStyles.title}>{trans('Add Address')}</Text>
              <View style={profileStyles.titleHrLine} />
            </View>

            <CustomTextInput
              label={
                <Text style={formStyle.labelText}>
                  <Text style={commonStyles.aterisk}>*</Text> {trans('First Name')}
                </Text>
              }
              isError={!!error.firstname}
              error={errorText?.firstname}
              value={address?.firstname}
              placeholder="Tai Man"
              onChangeText={(text) => handleAddressInfo('firstname', text)}
              returnKeyType="next"
              isFirst={true}
            />

            <CustomTextInput
              label={
                <Text style={formStyle.labelText}>
                  <Text style={commonStyles.aterisk}>*</Text> {trans('Last Name')}
                </Text>
              }
              isError={!!error.lastname}
              error={errorText?.lastname}
              value={address?.lastname}
              placeholder="Chan"
              onChangeText={(text) => handleAddressInfo('lastname', text)}
              returnKeyType="next"
            />

            <CustomTextInput
              label={
                <Text style={formStyle.labelText}>
                  <Text style={commonStyles.aterisk}>*</Text> {trans('Email')}
                </Text>
              }
              isError={!!error.email}
              error={errorText?.email}
              autoComplete="email"
              autoCapitalize="none"
              value={address?.email}
              placeholder="<EMAIL>"
              onChangeText={(text) => handleAddressInfo('email', text)}
              returnKeyType="next"
            />

            <CustomTextInput
              label={
                <Text style={formStyle.labelText}>
                  <Text style={commonStyles.aterisk}>*</Text> {trans('Telephone')}
                </Text>
              }
              isError={!!error.telephone}
              error={errorText?.telephone}
              value={address?.telephone}
              onChangeText={(text) => handleAddressInfo('telephone', text)}
              returnKeyType="next"
            />

            <View style={registrationStyle.inputTextContainer}>
              <Text style={[formStyle.labelText, gapStyle.mt15]}>
                <Text style={commonStyles.aterisk}>*</Text> {trans('Location')}
              </Text>
            </View>
            <SelectInput
              style={dropdown1BtnStyle(error.zone_id)}
              placeholder={trans('Please Select')}
              onPress={() => {
                Keyboard.dismiss()
                zoneSelectRef.current?.snapToIndex(0)
              }}
              title={zoneInfo?.name || address?.zone}
              icon={
                countryLoading ? (
                  <CustomSmallLoader />
                ) : (
                  <CustomSVG svgIcon={DownArrowFill} fill={themeColor.primaryText} />
                )
              }
            />
            {error?.zone_id && (
              <Text style={[formStyle.noteText, mb5(error?.country_id)]}>{error?.zone_id}</Text>
            )}

            <SelectInput
              style={[dropdown1BtnStyle(error?.city_id), gapStyle.mt15]}
              placeholder={trans('Please Select')}
              onPress={() => {
                Keyboard.dismiss()
                citySelectRef.current?.snapToIndex(0)
              }}
              title={cityInfo?.name || address?.city}
              disabled={zoneInfo === null ? true : false}
              icon={
                zoneLoading ? (
                  <CustomSmallLoader />
                ) : (
                  <CustomSVG svgIcon={DownArrowFill} fill={'#2C2C2C'} />
                )
              }
            />
            {error?.city_id && (
              <Text style={[formStyle.noteText, mb5(error?.city_id)]}>{error?.city_id}</Text>
            )}

            <CustomTextInput
              label={
                <Text style={formStyle.labelText}>
                  <Text style={commonStyles.aterisk}>*</Text> {trans('Address')}
                </Text>
              }
              isError={!!error.address_1}
              error={errorText?.address_1}
              value={address?.address_1}
              onChangeText={(text) => handleAddressInfo('address_1', text)}
              returnKeyType="next"
            />

            <CustomTextInput
              label={<Text style={formStyle.labelText}>{trans('Post Code')}</Text>}
              isError={!!error?.postcode}
              error={errorText?.postcode}
              value={address?.postcode}
              onChangeText={(text) => handleAddressInfo('postcode', text)}
            />

            {isUpdate && !item?.default && (
              <View>
                <Pressable
                  onPress={() => {
                    return deleteLoading ? {} : handleDeleteAddress(address?.address_id)
                  }}
                  style={addNewAddressStyle.deleteAddress}
                  disabled={item?.default ? true : false}>
                  {deleteLoading ? (
                    <CustomSpinner
                      filePath={require('@/assets/lottie/loader.json')}
                      size={{
                        width: dpr(45),
                        height: dpr(45),
                      }}
                    />
                  ) : (
                    <View style={addNewAddressStyle.deleteAddressBtn}>
                      <View style={addNewAddressStyle.deleteAddressBtnCont}>
                        <CustomSVG
                          svgIcon={DeleteIcon}
                          height={dpr(20)}
                          width={dpr(20)}
                          fill={'#FFFFFF'}
                        />
                        <Text style={addNewAddressStyle.deleteAddressBtnText}>
                          {trans('Delete this Address')}
                        </Text>
                      </View>
                    </View>
                  )}
                </Pressable>
              </View>
            )}
          </View>
        </ScrollView>
        <View style={commonStyles.stickyButtonContainer}>
          <CustomButton
            loading={loading}
            onPress={handleAddNewAddress}
            text={trans('Save Address')}
          />
        </View>
      </KeyboardAvoidingView>
      {hkArea?.length > 0 && (
        <SelectItemBottomSheet
          selectRef={zoneSelectRef}
          data={hkArea}
          name={'zone'}
          onPress={handleZoneSelect}
          loading={countryLoading}
          isHeaderComponent={true}
          onChange={(index) => {
            setZoneSheetIndex(index)
          }}
        />
      )}
      {zones.length > 0 && (
        <SelectItemBottomSheet
          selectRef={citySelectRef}
          data={zones}
          name={'city'}
          onPress={handleCitySelect}
          isHeaderComponent={true}
          onChange={(index) => {
            setCitySheetIndex(index)
          }}
        />
      )}
    </>
  )
}

export default GuestAddNewAddress
