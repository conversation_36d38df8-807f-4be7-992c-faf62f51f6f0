export const DEFAULT_COUNTRY_ID = 96

export const emailReg =
  /^$|^[A-Za-z0-9]+((\.[_A-Za-z0-9-]+)|(\_[.A-Za-z0-9-]+)|(\-[.A-Za-z0-9_]+))*@[A-Za-z0-9-]+(\.[A-Za-z0-9-]+)*(\.[A-Za-z]{2,})$/

export const phoneReg = /^[+]?[0-9]{1,45}$/

export const zipReg = /^[0-9]*$/

export const initialState = {
  firstname: '',
  lastname: '',
  telephone: '',
  address_1: '',
  country_id: DEFAULT_COUNTRY_ID,
  zone: '',
  zone_id: '',
  city_id: '',
  city: '',
  postcode: '',
  default: 1,
}

export const isError = {
  firstname: false,
  lastname: false,
  telephone: false,
  address_1: false,
  country_id: false,
  zone_id: false,
  city_id: false,
  city: false,
  postcode: false,
  default: false,
  email: false,
}

export const initErrorText = {
  telephone: '',
  address_1: '',
}
