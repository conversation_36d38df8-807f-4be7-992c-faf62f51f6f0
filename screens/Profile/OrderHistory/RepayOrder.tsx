import React, { useEffect, useState } from 'react'
import { View, Text } from 'react-native'
import { NativeStackScreenProps } from '@react-navigation/native-stack'

import { profileStyles } from '../profile.style'
import useAuth from '@/hooks/useAuth'

import useLangTranslation from '@/hooks/useLangTranslation'
import BackNavigation from '@/screens/Utilities/CustomHeader/BackNavigation'
import dpr from '@/screens/Utilities/CustomStyleAttribute/dpr'
import { orderSummaryStyle } from '@/screens/ShoppingCart/OrderSummary/orderSummary.style'
import { commonStyles } from '@/screens/Utilities/CommonStyles/common.styles'
import { cartStyle } from '@/screens/ShoppingCart/shoppingCart.style'
import { orderConfirmedStyle } from '@/screens/ShoppingCart/OrderConfirmed/orderConfirmed.style'
import { gapStyle } from '@/screens/Utilities/CommonStyles/gap.style'
import useCustomToast from '@/hooks/useCustomToast'
import CustomButton from '@/src/components/CustomButton/CustomButton'
import { useAppDispatch, useAppSelector } from '@/hooks/reduxHooks'
import { getOrderRepaySession, orderRepay } from '@/redux/slices/cart/order/repayOrder'
import { RootStackParamList } from '@/types/navigations'
import RepayOrderSkeleton from '@/src/skeletons/screens/profile/order/RepayOrderSkeleton'
import SelectPaymentMethods from '@/src/components/SelectPaymentMethods/SelectPaymentMethods'

type Props = NativeStackScreenProps<RootStackParamList, 'RepayOrder'>

const iconSize = dpr(16)

const RepayOrder = (props: Props) => {
  const { navigation } = props
  const { trans } = useLangTranslation()
  const { orderNo } = props.route.params
  const dispatch = useAppDispatch()
  const { lng } = useAuth()
  const { repaySession, loading } = useAppSelector((state) => state.repayOrderReducer || {})
  const showToast = useCustomToast()

  const [paymentMethod, setPaymentMethod] = useState('')
  const [orderActionLoading, setOrderActionLoading] = useState(false)
  let isMounted = true

  const proceedToPayment = async () => {
    if (!paymentMethod) {
      showToast({
        text1: trans('Please select one shipping / payment method.'),
        type: 'common',
        position: 'bottom',
        props: { variant: 'error' },
      })
      return
    }

    try {
      setOrderActionLoading(true)
      const res = await dispatch(
        orderRepay({ requestData: { payment_method: paymentMethod }, lng }),
      )
      const { success, data, error } = res?.payload || {}

      if (success === 1) {
        //to-do: not yet updated
        // offline payment
        if (data?.[0]?.is_online === false && data?.[0]?.order_id) {
          showToast({
            text1: trans('Updated payment method.'),
            type: 'common',
            position: 'bottom',
            props: { variant: 'success' },
          })
          navigation.navigate('OrderDetails', {
            itemId: data?.[0]?.order_id,
          })
        } else {
          if (data?.[0]?.payment_link) {
            navigation.navigate('PaymentWebview', {
              url: data?.[0]?.payment_link,
            })
          }
        }
      } else {
        showToast({
          text1: error ? trans(error) : trans('Order process failed. Please try again.'),
          type: 'common',
          position: 'bottom',
          props: { variant: 'error' },
        })
      }
    } catch (error) {
      console.debug(error)
      showToast({
        text1: trans('Order process failed. Please try again.'),
        type: 'common',
        position: 'bottom',
        props: { variant: 'error' },
      })
    } finally {
      setOrderActionLoading(false)
    }
  }

  useEffect(() => {
    ;(async () => {
      if (isMounted) {
        await dispatch(getOrderRepaySession({ orderNo, lng }))
      }
      return () => {
        isMounted = false
      }
    })()
  }, [])

  return (
    <>
      <BackNavigation
        navigationProps={props.navigation}
        routeName={trans('order history')}
        capitalize={false}
      />
      <View style={profileStyles.hrLine} />
      <View style={commonStyles.flex}>
        {loading ? (
          <RepayOrderSkeleton />
        ) : (
          <>
            <View style={commonStyles.globalBodyContainer}>
              <View style={orderSummaryStyle.totalPriceContainer}>
                <Text style={cartStyle.fieldTitle}>{trans('Grand Total')}</Text>
                <Text style={cartStyle.fieldTitleBold}>{repaySession?.total}</Text>
              </View>
              <View style={gapStyle.mt20}>
                <SelectPaymentMethods
                  paymentMethod={paymentMethod}
                  setPaymentMethod={setPaymentMethod}
                />
              </View>
            </View>

            <View style={[commonStyles.bottomSectionsContainer, commonStyles.blackBg]}>
              <CustomButton
                onPress={() => props.navigation.goBack()}
                loading={orderActionLoading}
                text={trans('Cancel')}
                style={[orderConfirmedStyle.button, orderConfirmedStyle.cancelBtn, gapStyle.mt0]}
              />
              <CustomButton
                onPress={proceedToPayment}
                loading={orderActionLoading}
                text={trans('Pay')}
                style={[
                  orderConfirmedStyle.button,
                  orderConfirmedStyle.orderDetailBtn,
                  gapStyle.mt0,
                ]}
              />
            </View>
          </>
        )}
      </View>
    </>
  )
}

export default RepayOrder
