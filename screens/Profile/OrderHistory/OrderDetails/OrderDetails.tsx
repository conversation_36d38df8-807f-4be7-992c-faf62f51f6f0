import React, { useEffect, useState } from 'react'
import { View, Text, ScrollView, Pressable } from 'react-native'
import HTMLView from 'react-native-htmlview'
import { useFocusEffect } from '@react-navigation/native'
import { NativeStackScreenProps } from '@react-navigation/native-stack'
import { orderDetailsStyle } from './orderDetails.style'
import { profileStyles } from '../../profile.style'
import { ORDER_STATUS, ORDER_STATUS_LABEL } from '../orderStatus'
import useAuth from '@/hooks/useAuth'
import { getOrderDetails } from '@/redux/slices/user/orderDetails/orderDetails'
import OrderDetailsSkeleton from '@/src/skeletons/screens/profile/order/OrderDetails/OrderDetailsSkeleton'

import useLangTranslation from '@/hooks/useLangTranslation'
import CustomSVG from '@/src/components/CustomSVG/CustomSVG'
import BackNavigation from '@/screens/Utilities/CustomHeader/BackNavigation'
import dpr from '@/screens/Utilities/CustomStyleAttribute/dpr'
import { orderSummaryStyle } from '@/screens/ShoppingCart/OrderSummary/orderSummary.style'
import SelectedProduct from '@/screens/ShoppingCart/OrderSummary/SelectedProduct'
import {
  commonStyles,
  dynamicStatusText,
  htmlStyles,
} from '@/screens/Utilities/CommonStyles/common.styles'
import { themeColor } from '@/theme/theme'
import UpIcon from '@/assets/svgs/dropdown/up.svg'
import DownIcon from '@/assets/svgs/dropdown/down.svg'
import { cartStyle } from '@/screens/ShoppingCart/shoppingCart.style'
import { orderConfirmedStyle } from '@/screens/ShoppingCart/OrderConfirmed/orderConfirmed.style'
import { gapStyle } from '@/screens/Utilities/CommonStyles/gap.style'
import { apiService } from '@/redux/slices/util/apiRequest'
import useCustomToast from '@/hooks/useCustomToast'
import CustomButton from '@/src/components/CustomButton/CustomButton'
import { useAppDispatch, useAppSelector } from '@/hooks/reduxHooks'
import { RootStackParamList } from '@/types/navigations'
import { setAuctionOrderId } from '@/redux/slices/auction/createAuction'

const URL = `${process.env.EXPO_PUBLIC_BASE_API_URL}/customerorders`
const iconSize = dpr(16)

type Props = NativeStackScreenProps<RootStackParamList, 'OrderDetails'>

const OrderDetails = (props: Props) => {
  const { navigation, route } = props
  const { itemId } = route.params
  const { trans } = useLangTranslation()
  const dispatch = useAppDispatch()
  const { lng } = useAuth()
  const { orderDetails, loading } = useAppSelector((state) => state.getOrderDetailsSlice)
  const { mutation } = apiService
  const showToast = useCustomToast()

  const [show, setShow] = useState(true)
  const [orderActionLoading, setOrderActionLoading] = useState(false)

  useFocusEffect(
    React.useCallback(() => {
      handleFetchData()
    }, []),
  )

  const handleFetchData = async () => {
    await dispatch(getOrderDetails({ URL: `${URL}/${itemId}`, lng }))
  }

  const handleCancelOrder = async () => {
    setOrderActionLoading(true)
    try {
      const response = await mutation(`${URL}/cancel`, 'POST', {
        'X-Oc-Merchant-Language': lng,
        order_id: itemId,
      })
      const { success, error } = response
      if (success === 1) {
        showToast({
          text1: trans('The order has been successfully canceled.'),
          type: 'common',
          position: 'bottom',
          props: { variant: 'success' },
        })
        dispatch(getOrderDetails({ URL: `${URL}/${itemId}`, lng }))
      } else {
        if (error.length > 0) {
          let errorMessage = ''
          error.map((message: string) => {
            errorMessage += trans(message) + '\n'
          })
          showToast({
            text1: errorMessage,
            type: 'common',
            position: 'bottom',
            props: { variant: 'error' },
          })
        }
      }
    } catch (error) {
      console.error(error)
    } finally {
      setOrderActionLoading(false)
    }
  }

  const handleRepay = () => {
    navigation.navigate('RepayOrder', { orderNo: orderDetails?.order_no })
  }

  const handleCreateAuction = async () => {
    await dispatch(setAuctionOrderId({ orderId: itemId, orderNo: orderDetails?.order_no }))
    navigation.navigate('CreateAuction', { products: orderDetails?.products })
  }

  const [fastLoad, setFastLoad] = useState(true)
  useEffect(() => setFastLoad(false), [])
  if (fastLoad) return <View />

  return (
    <>
      <BackNavigation
        navigationProps={navigation}
        routeName={trans('order history')}
        capitalize={false}
        backActions={() => navigation.navigate('OrderHistory')}
      />
      <View style={profileStyles.hrLine} />
      <View style={commonStyles.cont}>
        {loading ? (
          <OrderDetailsSkeleton />
        ) : (
          <>
            <ScrollView showsVerticalScrollIndicator={false} style={commonStyles.paddingSection}>
              <View style={commonStyles.sectionTitleContainer}>
                <Text style={[commonStyles.sectionTitle, orderDetailsStyle.idNum]}>
                  {trans('Order Number')}
                </Text>
                <Text style={orderDetailsStyle.invoiceId}>#{orderDetails?.order_no}</Text>
              </View>
              <View style={orderDetailsStyle.deliveryCont}>
                <View style={orderDetailsStyle.deliverySubCont}>
                  <Text style={orderDetailsStyle.deliveryText1}>{trans('Shipping Method')}</Text>
                  <Text style={orderDetailsStyle.deliveryText2}>
                    {orderDetails?.shipping_method}
                  </Text>
                </View>
                <View style={[orderDetailsStyle.deliverySubCont, orderDetailsStyle.borderStyle]}>
                  <Text style={orderDetailsStyle.deliveryText1}>{trans('Payment Method')}</Text>
                  <Text style={orderDetailsStyle.deliveryText2}>
                    {orderDetails?.payment_method}
                  </Text>
                </View>
              </View>

              <View style={[orderDetailsStyle.deliveryCont, gapStyle.mt15]}>
                <View style={orderDetailsStyle.deliverySubCont}>
                  <Text style={orderDetailsStyle.deliveryText1}>{trans('Order Date')}</Text>
                  <Text style={orderDetailsStyle.deliveryText2}>{orderDetails?.date_added}</Text>
                </View>
                <View style={[orderDetailsStyle.deliverySubCont, orderDetailsStyle.borderStyle]}>
                  <Text style={orderDetailsStyle.deliveryText1}>{trans('Payment Status')}</Text>
                  <Text
                    style={[
                      orderDetailsStyle.deliveryText2,
                      {
                        color: dynamicStatusText(ORDER_STATUS_LABEL[orderDetails?.order_status_id]),
                      },
                    ]}>
                    {trans(ORDER_STATUS_LABEL[orderDetails?.order_status_id])}
                  </Text>
                </View>
              </View>

              {orderDetails?.shipping_address && (
                <View style={[orderDetailsStyle.shippingAddressCont, gapStyle.mt15]}>
                  <Text style={orderDetailsStyle.shippingTitle}>{trans('Shipping Address')}</Text>
                  <View style={orderDetailsStyle.shippingTextCont}>
                    <HTMLView
                      stylesheet={htmlStyles}
                      value={orderDetails?.shipping_address?.trim()}
                    />
                  </View>
                </View>
              )}

              {ORDER_STATUS.PENDING_PAYMENT === orderDetails?.order_status_id &&
                orderDetails?.payment_info?.remark && (
                  <View style={[orderDetailsStyle.shippingAddressCont, gapStyle.mt15]}>
                    <View style={{ borderBottomWidth: 1, borderColor: themeColor.lightBorder }}>
                      <Text style={orderDetailsStyle.shippingTitle}>
                        {trans(orderDetails?.payment_info?.title || '')}
                      </Text>
                    </View>
                    <View style={orderDetailsStyle.shippingTextCont}>
                      <HTMLView
                        stylesheet={htmlStyles}
                        value={orderDetails?.payment_info?.information?.trim() || ''}
                      />
                      <HTMLView
                        stylesheet={htmlStyles}
                        value={orderDetails?.payment_info?.remark?.trim() || ''}
                      />
                    </View>
                  </View>
                )}

              <View style={orderDetailsStyle.orderProductsCont}>
                <Pressable style={cartStyle.accordion} onPress={() => setShow(!show)}>
                  <Text style={orderSummaryStyle.allProducts}>{trans('Ordered Products')}</Text>
                  {show ? (
                    <CustomSVG
                      svgIcon={UpIcon}
                      height={iconSize}
                      width={iconSize}
                      fill={themeColor.secondText}
                    />
                  ) : (
                    <CustomSVG
                      svgIcon={DownIcon}
                      height={iconSize}
                      width={iconSize}
                      fill={themeColor.secondText}
                    />
                  )}
                </Pressable>
                {show && (
                  <View style={orderSummaryStyle.allProdctsContainer}>
                    <ScrollView nestedScrollEnabled={true}>
                      {orderDetails?.products?.map((item, index) => (
                        <SelectedProduct
                          key={index}
                          item={item}
                          index={index}
                          length={orderDetails?.products?.length - 1}
                        />
                      ))}
                    </ScrollView>
                  </View>
                )}
              </View>
              <View style={orderDetailsStyle.calculationMargin}>
                {orderDetails?.totals?.map((priceSum, index) => (
                  <View key={index} style={orderDetailsStyle.subTotalCont}>
                    <Text style={[orderDetailsStyle.subText, orderDetailsStyle.subTextTitle]}>
                      {priceSum?.title}
                    </Text>
                    <Text style={[orderDetailsStyle.subText, orderDetailsStyle.subTextValue]}>
                      {priceSum?.text}
                    </Text>
                  </View>
                ))}
              </View>
            </ScrollView>
            {ORDER_STATUS.PENDING_PAYMENT === orderDetails?.order_status_id && (
              <View style={[commonStyles.bottomSectionsContainer, commonStyles.blackBg]}>
                <CustomButton
                  onPress={handleCancelOrder}
                  loading={orderActionLoading}
                  text={trans('Cancel')}
                  style={[orderConfirmedStyle.button, orderConfirmedStyle.cancelBtn, gapStyle.mt0]}
                />
                <CustomButton
                  onPress={handleRepay}
                  loading={orderActionLoading}
                  text={trans('Pay')}
                  style={[
                    orderConfirmedStyle.button,
                    orderConfirmedStyle.orderDetailBtn,
                    gapStyle.mt0,
                  ]}
                />
              </View>
            )}
            {ORDER_STATUS.COMPLETE === orderDetails?.order_status_id && (
              <View style={commonStyles.bottomSectionsContainer}>
                <CustomButton
                  onPress={handleCreateAuction}
                  loading={orderActionLoading}
                  text={trans('Join Auction')}
                  style={[
                    orderConfirmedStyle.button,
                    orderConfirmedStyle.orderDetailBtn,
                    gapStyle.mt0,
                  ]}
                />
              </View>
            )}
          </>
        )}
      </View>
    </>
  )
}

export default OrderDetails
