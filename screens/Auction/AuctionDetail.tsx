import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react'
import {
  View,
  Pressable,
  Text,
  FlatList,
  ListRenderItemInfo,
  KeyboardAvoidingView,
  Platform,
} from 'react-native'
import Toast from 'react-native-toast-message'
import { NativeStackScreenProps } from '@react-navigation/native-stack'
import auctionDetailStyle from './auctionDetail.style'
import { productListingStyle } from '../Arts/productListing.style'
import flexStyle from '@/screens/Utilities/CommonStyles/flex.style'
import ItemCarousel from '@/screens/Home/FeaturedProducts/ProductDetails/ItemCarousel/ItemCarousel'

import ItemHeader from '@/screens/Home/FeaturedProducts/ProductDetails/ItemDetails/ItemSection/ItemHeader'
import ItemQuantity from '@/screens/Home/FeaturedProducts/ProductDetails/ItemQuantity/ItemQuantity'
import { productDetailsStyle } from '@/screens/Home/FeaturedProducts/ProductDetails/productDetails.style'
import dpr from '@/screens/Utilities/CustomStyleAttribute/dpr'
import BackNavigation from '@/screens/Utilities/CustomHeader/BackNavigation'
import EmptyContent from '@/screens/Utilities/EmptyContent/EmptyContent'
import useAuth from '@/hooks/useAuth'
import useCustomToast from '@/hooks/useCustomToast'

import useLangTranslation from '@/hooks/useLangTranslation'
import NoWishlistIcon from '@/assets/svgs/empty content/noWishlist.svg'
import DownIcon from '@/assets/svgs/dropdown/down.svg'
import UpIcon from '@/assets/svgs/dropdown/up.svg'
import CustomSVG from '@/src/components/CustomSVG/CustomSVG'
import { commonStyles } from '@/screens/Utilities/CommonStyles/common.styles'
import { RootStackParamList } from '@/types/navigations'
import { gapStyle } from '@/screens/Utilities/CommonStyles/gap.style'
import { apiService } from '@/redux/slices/util/apiRequest'
import { useAppDispatch, useAppSelector } from '@/hooks/reduxHooks'
import { setAttrFilters, toggleFilterSelection } from '@/redux/slices/featureProducts/filters'
import { AuctionDetailState, LotList } from '@/types/auction'
import lotListItemStyle from '@/src/components/Auction/lotListItem.style'
import CountdownTimer from '@/src/components/CountdownTimer/CountdownTimer'
import PriceIcon from '@/assets/svgs/auction/price-icon.svg'
import AuctionIcon from '@/assets/svgs/auction/auction-icon.svg'
import BidIcon from '@/assets/svgs/auction/bid-icon.svg'
import LotListItem from '@/src/components/Auction/LotListItem'
import AuctionDetailSkeleton from '@/src/skeletons/screens/auction/AuctionDetailSkeleton'
import AuctionButtons from '@/src/components/Auction/AuctionButtons'
import CustomModal from '@/src/components/CustomModal/CustomModal'
import CustomModalContent, { ModalContent } from '@/src/components/CustomModal/CustomModalContent'

type Props = NativeStackScreenProps<RootStackParamList, 'AuctionDetail'>

const URL = `${process.env.EXPO_PUBLIC_BASE_API_URL}/auctions/lot`

const iconSize = dpr(16)

const AuctionDetail = ({ route, navigation }: Props) => {
  const { trans } = useLangTranslation()
  const {
    params: { id: lotId },
  } = route
  const showToast = useCustomToast()
  const dispatch = useAppDispatch()
  const { lng, currency, currencySymbol } = useAuth()
  const { query, mutation } = apiService

  const [loading, setLoading] = useState(true)
  const [productDetails, setProductDetails] = useState({} as AuctionDetailState)
  const { uiAttributeKey } = useAppSelector((state) => state.productFilterReducer)
  const [itemCarouselData, setItemCarouselData] = useState({
    images: [
      {
        thumb: '',
        preview: '',
        popup: '',
      },
    ],
    activeImage: 0,
  })

  const [itemHeaderData, setItemHeaderData] = useState({
    name: '',
    productNo: '',
  })

  const [productInfoSections, setProductInfoSections] = useState({
    bidHistory: {
      name: '',
      isShowing: false,
    },
    description: {
      name: 'Description',
      isShowing: false,
    },
    details: {
      name: 'Details',
      isShowing: false,
    },
    productInfo: {
      name: 'Product Info',
      isShowing: false,
    },
    auctionHistory: {
      name: 'Auction History',
      isShowing: false,
    },
  })

  const [bidPrice, setBidPrice] = useState(0)
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [modalConfig, setModalConfig] = useState<ModalContent | null>(null)
  const bidRef = useRef(0)

  const memorizeItem = useMemo(
    () =>
      ({ item }: ListRenderItemInfo<LotList>) => (
        <LotListItem item={item} isRelatedSection={true} />
      ),
    [],
  )

  const fetchData = useCallback(async () => {
    const singleItem = await query(`${URL}/${lotId}`, 'GET', {
      'X-Oc-Merchant-Language': lng,
      'X-Oc-Currency': currency,
    })
    const { success, data } = singleItem
    if (success === 1) {
      setProductDetails(data)
      setLoading(false)
      if (data?.images) {
        setItemCarouselData({
          ...itemCarouselData,
          images: data?.images,
        })
      }

      setItemHeaderData({
        name: data?.info?.name,
        productNo: data?.info?.model,
      })

      // start price might be current bid or starting price, depends on higher value
      const initPrice =
        data?.info?.current_bid_num > data?.info?.starting_price_num
          ? data?.info?.current_bid_num
          : data?.info?.starting_price_num
      if (!bidRef.current) {
        setBidPrice(initPrice + data?.info?.increment_num)
      }

      return data
    } else {
      setLoading(false)
      showToast({
        text1: trans('Something Went Wrong Please Try Again!'),
        type: 'common',
        position: 'bottom',
        props: { variant: 'error' },
      })
      return null
    }
  }, [currency, lng, lotId])

  useEffect(() => {
    let intervalId: NodeJS.Timeout | null = null

    const initializeData = async () => {
      const result = await fetchData()

      if (intervalId) {
        clearInterval(intervalId)
        intervalId = null
      }

      if (result?.info?.is_allow_bid === 1 && result?.info?.is_bid_end === 0) {
        intervalId = setInterval(() => {
          fetchData()
        }, 5000)
      }
    }

    initializeData()

    return () => {
      if (intervalId) {
        clearInterval(intervalId)
      }
    }
  }, [currency, lng, lotId])

  const confirmText = (isBuyNow: number) => {
    return (
      <Text style={[auctionDetailStyle.boldText, auctionDetailStyle.confirmText]}>
        {currencySymbol}
        {isBuyNow ? productDetails?.info?.buy_now_price_num?.toFixed(2) : bidPrice.toFixed(2)}
      </Text>
    )
  }

  const confirmAuction = async (isBuyNow: number) => {
    const canBuyNow =
      productDetails.info?.is_bid_end === 0 &&
      productDetails?.info?.is_lock_buy_now_price === 0 &&
      productDetails?.info?.buy_now_price_num > 0
    const isBidHigherThanStartPrice = !canBuyNow
      ? false
      : bidPrice > productDetails?.info?.buy_now_price_num

    setModalConfig({
      message: isBidHigherThanStartPrice
        ? trans('The bidding price is higher than the buy now price. Shall we continue?')
        : isBuyNow
          ? trans('Confirm Buy Now?')
          : trans('Confirm Bid?'),
      content: confirmText(isBuyNow),
      type: 'info',
      buttons: [
        {
          text: trans('Cancel'),
          onPress: () => setIsModalVisible(false),
          type: 'cancel',
        },
        {
          text: trans('Confirm'),
          onPress: () => {
            // Handle confirmation
            setIsModalVisible(false)
            handleAuction(isBuyNow)
          },
          type: 'confirm',
        },
      ],
    })
    setIsModalVisible(true)
  }

  const handleAuction = async (isBuyNow: number) => {
    const auctionBidData = {
      bidding_price: isBuyNow ? productDetails?.info?.buy_now_price_num : bidPrice,
      auction_id: lotId,
      is_buy_now_price: isBuyNow,
    }
    try {
      const result = await mutation(`${URL}/bid`, 'POST', auctionBidData, lng, currency)
      const { data, success, error } = result

      if (success === 1) {
        if (isBuyNow) {
          navigation.navigate('AuctionPayNow', {
            bidId: data?.bid_id,
          })
        } else {
          navigation.navigate('AuctionConfirmed', {
            bidData: data,
            lotId: lotId,
          })
        }
      } else {
        if (error[0] === 'User is not logged.') {
          showToast({
            text1: trans(error[0]),
            text2: 'Login Now',
            type: 'addToCart',
            position: 'bottom',
            props: {
              variant: 'error',
              func: () => {
                navigation.navigate('DrawerStack', {
                  screen: 'HomeScreen',
                  params: { screen: 'My Account' },
                })
                Toast.hide()
              },
            },
          })
        } else {
          showToast({
            text1: trans(error[0]),
            type: 'common',
            position: 'bottom',
            props: { variant: 'error' },
          })
        }
      }
    } catch (error) {
      showToast({
        text1: trans('Something went wrong. Please Try Again!'),
        type: 'common',
        position: 'bottom',
        props: { variant: 'error' },
      })
    }
  }

  const handleUpDownIcon = (key: string) => {
    setProductInfoSections({
      ...productInfoSections,
      [key]: {
        ...productInfoSections[key as keyof typeof productInfoSections],
        isShowing: !productInfoSections[key as keyof typeof productInfoSections].isShowing,
      },
    })
  }

  const handleSelectItem = (parentCatId: number, id: number) => {
    dispatch(toggleFilterSelection({ parentCatId, id, forceSelect: true }))
    navigation.navigate('DrawerStack', {
      screen: 'HomeScreen',
      params: {
        screen: 'ProductListing',
      },
    })
  }

  const updateApiFilter = () => {
    let filterString = ''
    uiAttributeKey.map((item) => {
      item.values.map((subItem) => {
        if (subItem.isChecked) {
          filterString += `f${item.filter_group_id}=${subItem.id}&`
        }
      })
    })
    dispatch(setAttrFilters(filterString))
  }

  useEffect(() => {
    updateApiFilter()
  }, [uiAttributeKey])

  return (
    <>
      <BackNavigation
        navigationProps={navigation}
        routeName={trans('Detail')}
        capitalize={false}
        backActions={() => navigation.navigate('AuctionListing')}
      />
      <FlatList
        data={null}
        renderItem={null}
        ListHeaderComponent={
          <>
            {!loading && itemCarouselData?.images[0]?.preview?.length > 0 && (
              <ItemCarousel data={itemCarouselData} />
            )}
            <View style={[commonStyles.globalContainer, gapStyle.pb50]}>
              {loading ? (
                <AuctionDetailSkeleton />
              ) : (
                <>
                  <ItemHeader data={itemHeaderData} />
                  {!loading && Object.keys(productDetails ?? {})?.length === 0 && (
                    <View>
                      <EmptyContent Icon={NoWishlistIcon} text={trans('No Data Found')} />
                    </View>
                  )}
                  <View style={auctionDetailStyle.bidPriceSection}>
                    <View>
                      <Text
                        style={[
                          lotListItemStyle.currentBid,
                          auctionDetailStyle.currentBid,
                          auctionDetailStyle.bidColor,
                        ]}>
                        {trans('Current Bid')}
                        {':'}
                      </Text>
                      <Text style={[auctionDetailStyle.pricing, auctionDetailStyle.bidColor]}>
                        {productDetails?.info?.current_bid_str}{' '}
                      </Text>
                      <Text
                        style={[
                          auctionDetailStyle.pricing,
                          auctionDetailStyle.bidColor,
                          { marginTop: 0 },
                        ]}>
                        {`(${productDetails?.info?.text_met_reserve})`}
                      </Text>
                    </View>
                    <View>
                      {productDetails?.info?.is_lock_buy_now_price === 0 &&
                        productDetails?.info?.buy_now_price_num > 0 && (
                          <>
                            <Text
                              style={[
                                lotListItemStyle.currentBid,
                                auctionDetailStyle.currentBid,
                                auctionDetailStyle.buyNowColor,
                              ]}>
                              {trans('Buy Now Price')}
                              {':'}
                            </Text>
                            <Text
                              style={[auctionDetailStyle.pricing, auctionDetailStyle.buyNowColor]}>
                              {productDetails?.info?.buy_now_price_str}
                            </Text>
                          </>
                        )}
                    </View>
                  </View>
                  {productDetails?.info?.is_allow_bid === 1 &&
                  productDetails?.info?.is_bid_end === 0 ? (
                    <View style={gapStyle.mt20}>
                      <Text style={auctionDetailStyle.auctionWillEnd}>
                        {trans('Auction will be end')}
                        {':'}
                      </Text>
                      <CountdownTimer
                        targetDate={productDetails?.info?.bid_end_time}
                        timerStyle={auctionDetailStyle.countDown}
                        inlineLabel={true}
                      />
                    </View>
                  ) : (
                    <View style={gapStyle.mt10}>
                      <Text style={auctionDetailStyle.auctionFinish}>{trans('Ended')}</Text>
                    </View>
                  )}
                  <Text
                    style={[
                      auctionDetailStyle.text,
                      auctionDetailStyle.lightTextColor,
                      gapStyle.mt10,
                    ]}>
                    {trans('Auction Ends')}
                    {': '}
                    <Text style={auctionDetailStyle.endDate}>
                      {productDetails?.info?.bid_end_time}
                    </Text>
                  </Text>
                  <View style={[auctionDetailStyle.auctionKeyInfo, gapStyle.pt30]}>
                    <View style={flexStyle.rowBasic}>
                      <CustomSVG
                        svgIcon={PriceIcon}
                        height={iconSize}
                        width={iconSize}
                        style={auctionDetailStyle.infoIcon}
                      />
                      <Text style={auctionDetailStyle.text}>
                        {trans('Starting Price')}
                        {':'}
                      </Text>
                    </View>
                    <Text style={[auctionDetailStyle.text, auctionDetailStyle.boldText]}>
                      {productDetails?.info?.starting_price_str}
                    </Text>
                  </View>
                  <View style={auctionDetailStyle.auctionKeyInfo}>
                    <View style={flexStyle.rowBasic}>
                      <CustomSVG
                        svgIcon={AuctionIcon}
                        height={iconSize}
                        width={iconSize}
                        style={auctionDetailStyle.infoIcon}
                      />
                      <Text style={auctionDetailStyle.text}>
                        {trans('Bid Increment')}
                        {':'}
                      </Text>
                    </View>
                    <Text style={[auctionDetailStyle.text, auctionDetailStyle.boldText]}>
                      {productDetails?.info?.bid_increment_str}
                    </Text>
                  </View>
                  {productDetails?.bids?.length > 0 && (
                    <>
                      <Pressable
                        onPress={() => handleUpDownIcon('bidHistory')}
                        style={[
                          auctionDetailStyle.auctionKeyInfo,
                          auctionDetailStyle.auctionHistory,
                        ]}>
                        <View style={flexStyle.rowBasic}>
                          <CustomSVG
                            svgIcon={BidIcon}
                            height={iconSize}
                            width={iconSize}
                            style={auctionDetailStyle.infoIcon}
                          />
                          <Text style={auctionDetailStyle.text}>{`${trans('Bid')}:`}</Text>
                        </View>
                        <View style={flexStyle.rowBasic}>
                          <Text
                            style={[
                              auctionDetailStyle.text,
                              auctionDetailStyle.boldText,
                              gapStyle.mr5,
                            ]}>
                            {productDetails?.info?.total_bid}
                          </Text>
                          {productInfoSections.bidHistory.isShowing ? (
                            <CustomSVG svgIcon={UpIcon} height={iconSize} width={iconSize} />
                          ) : (
                            <CustomSVG
                              svgIcon={DownIcon}
                              height={iconSize}
                              width={iconSize}
                              fill={'#252525'}
                            />
                          )}
                        </View>
                      </Pressable>
                      {!productInfoSections.bidHistory.isShowing && (
                        <View style={[commonStyles.hrLine, gapStyle.mt0, gapStyle.mb30]} />
                      )}
                      {productInfoSections.bidHistory.isShowing && (
                        <View style={auctionDetailStyle.historyContainer}>
                          <View style={auctionDetailStyle.historyHr}>
                            <Text style={[auctionDetailStyle.text, auctionDetailStyle.boldText]}>
                              {trans('Bid History')}
                            </Text>
                          </View>
                          {productDetails?.bids?.map((data, index) => (
                            <View
                              key={index}
                              style={index < productDetails?.bids?.length - 1 && gapStyle.mb20}>
                              <Text style={auctionDetailStyle.lightTextColor}>
                                {trans('Bid')}{' '}
                                <Text style={auctionDetailStyle.boldText}>
                                  {data?.bidding_price}{' '}
                                  {Number(data?.is_buy_now_price)
                                    ? `(${trans('Buy Now Price')})`
                                    : ''}
                                </Text>
                              </Text>
                              <Text style={auctionDetailStyle.lightTextColor}>
                                {trans('By')}{' '}
                                <Text style={auctionDetailStyle.boldText}>{data?.fullname}</Text>{' '}
                                {trans('At')}{' '}
                                <Text style={auctionDetailStyle.boldText}>{data?.date_added}</Text>
                              </Text>
                            </View>
                          ))}
                        </View>
                      )}
                    </>
                  )}
                  {productDetails?.filters?.length > 0 && (
                    <>
                      <View style={commonStyles.accordionContainer}>
                        <Pressable
                          style={commonStyles.accordion}
                          onPress={() => handleUpDownIcon('description')}>
                          <Text style={commonStyles.accordionText}>{trans('Description')}</Text>
                          {productInfoSections.description.isShowing ? (
                            <CustomSVG svgIcon={UpIcon} height={iconSize} width={iconSize} />
                          ) : (
                            <CustomSVG
                              svgIcon={DownIcon}
                              height={iconSize}
                              width={iconSize}
                              fill={'#252525'}
                            />
                          )}
                        </Pressable>
                        {!productInfoSections.description.isShowing && (
                          <View style={commonStyles.hrLine} />
                        )}
                      </View>
                      {productInfoSections.description.isShowing && (
                        <View style={productDetailsStyle.metaDataContainer}>
                          {productDetails?.filters?.map((data, index) => (
                            <Pressable
                              style={productDetailsStyle.metaDataBlock}
                              key={index}
                              onPress={() =>
                                handleSelectItem(
                                  Number(data?.filter_group_id),
                                  Number(data?.filters[0]?.filter_id),
                                )
                              }>
                              <Text style={productDetailsStyle.metaDataLabel}>{data?.name}</Text>
                              <Text
                                style={[
                                  productDetailsStyle.metaDataLabel,
                                  productDetailsStyle.metaDataValue,
                                ]}>
                                {data?.filters[0]?.name}
                              </Text>
                            </Pressable>
                          ))}
                        </View>
                      )}
                    </>
                  )}
                  {productDetails?.nft_info &&
                    Object.keys(productDetails?.nft_info)?.length > 0 && (
                      <>
                        <View style={commonStyles.accordionContainer}>
                          <Pressable
                            style={commonStyles.accordion}
                            onPress={() => handleUpDownIcon('details')}>
                            <Text style={commonStyles.accordionText}>{trans('Details')}</Text>
                            {productInfoSections.details.isShowing ? (
                              <CustomSVG svgIcon={UpIcon} height={iconSize} width={iconSize} />
                            ) : (
                              <CustomSVG
                                svgIcon={DownIcon}
                                height={iconSize}
                                width={iconSize}
                                fill={'#252525'}
                              />
                            )}
                          </Pressable>
                          {!productInfoSections.details.isShowing && (
                            <View style={commonStyles.hrLine} />
                          )}
                        </View>
                        {productInfoSections.details.isShowing && (
                          <>
                            <View style={productDetailsStyle.detailRow}>
                              <Text style={productDetailsStyle.metaDataLabel}>
                                {trans('Contract Address')}
                              </Text>
                              <Text
                                style={[
                                  productDetailsStyle.metaDataLabel,
                                  productDetailsStyle.metaDataValue,
                                  productDetailsStyle.detailOddRow,
                                ]}>
                                {productDetails?.nft_info?.contract_address.substring(0, 5)}...$
                                {productDetails?.nft_info?.contract_address.slice(-5)}
                              </Text>
                            </View>

                            <View style={productDetailsStyle.detailRow}>
                              <Text style={productDetailsStyle.metaDataLabel}>
                                {trans('Token Standard')}
                              </Text>
                              <Text
                                style={[
                                  productDetailsStyle.metaDataLabel,
                                  productDetailsStyle.metaDataValue,
                                  productDetailsStyle.detailEvenRow,
                                ]}>
                                {productDetails?.nft_info?.token_type}
                              </Text>
                            </View>

                            <View style={productDetailsStyle.detailRow}>
                              <Text style={productDetailsStyle.metaDataLabel}>
                                {trans('Token ID')}
                              </Text>
                              <Text
                                style={[
                                  productDetailsStyle.metaDataLabel,
                                  productDetailsStyle.metaDataValue,
                                  productDetailsStyle.detailOddRow,
                                ]}>
                                {productDetails?.nft_info?.token_id}
                              </Text>
                            </View>

                            <View style={productDetailsStyle.detailRow}>
                              <Text style={productDetailsStyle.metaDataLabel}>
                                {trans('Chain')}
                              </Text>
                              <Text
                                style={[
                                  productDetailsStyle.metaDataLabel,
                                  productDetailsStyle.metaDataValue,
                                  productDetailsStyle.detailEvenRow,
                                ]}>
                                {productDetails?.nft_info?.chain}
                              </Text>
                            </View>
                          </>
                        )}
                      </>
                    )}
                  <View>
                    {productDetails?.product_info?.width ||
                    productDetails?.product_info?.height ||
                    productDetails?.product_info?.length ||
                    productDetails?.product_info?.weight ? (
                      <>
                        <View style={commonStyles.accordionContainer}>
                          <Pressable
                            style={commonStyles.accordion}
                            onPress={() => handleUpDownIcon('productInfo')}>
                            <Text style={commonStyles.accordionText}>{trans('Product Info')}</Text>
                            {productInfoSections.productInfo.isShowing ? (
                              <CustomSVG svgIcon={UpIcon} height={iconSize} width={iconSize} />
                            ) : (
                              <CustomSVG
                                svgIcon={DownIcon}
                                height={iconSize}
                                width={iconSize}
                                fill={'#252525'}
                              />
                            )}
                          </Pressable>
                          {!productInfoSections.productInfo.isShowing && (
                            <View style={commonStyles.hrLine} />
                          )}
                        </View>
                        {productInfoSections.productInfo.isShowing && (
                          <>
                            {productDetails?.product_info?.length && (
                              <View style={productDetailsStyle.detailRow}>
                                <Text style={productDetailsStyle.metaDataLabel}>
                                  {productDetails?.product_info?.text_dimensions_length}
                                </Text>
                                <Text
                                  style={[
                                    productDetailsStyle.metaDataLabel,
                                    productDetailsStyle.metaDataValue,
                                    productDetailsStyle.detailEvenRow,
                                  ]}>
                                  {productDetails?.product_info?.length}
                                </Text>
                              </View>
                            )}
                            {productDetails?.product_info?.width && (
                              <View style={productDetailsStyle.detailRow}>
                                <Text style={productDetailsStyle.metaDataLabel}>
                                  {productDetails?.product_info?.text_dimensions_width}
                                </Text>
                                <Text
                                  style={[
                                    productDetailsStyle.metaDataLabel,
                                    productDetailsStyle.metaDataValue,
                                    productDetailsStyle.detailEvenRow,
                                  ]}>
                                  {productDetails?.product_info?.width}
                                </Text>
                              </View>
                            )}
                            {productDetails?.product_info?.height && (
                              <View style={productDetailsStyle.detailRow}>
                                <Text style={productDetailsStyle.metaDataLabel}>
                                  {productDetails?.product_info?.text_dimensions_height}
                                </Text>
                                <Text
                                  style={[
                                    productDetailsStyle.metaDataLabel,
                                    productDetailsStyle.metaDataValue,
                                    productDetailsStyle.detailEvenRow,
                                  ]}>
                                  {productDetails?.product_info?.height}
                                </Text>
                              </View>
                            )}
                            {productDetails?.product_info?.dimensions_o && (
                              <View style={productDetailsStyle.detailRow}>
                                <Text style={productDetailsStyle.metaDataLabel}>
                                  {productDetails?.product_info?.text_dimensions_o}
                                </Text>
                                <Text
                                  style={[
                                    productDetailsStyle.metaDataLabel,
                                    productDetailsStyle.metaDataValue,
                                    productDetailsStyle.detailEvenRow,
                                  ]}>
                                  {productDetails?.product_info?.dimensions_o}
                                </Text>
                              </View>
                            )}
                            {productDetails?.product_info?.weight && (
                              <View style={productDetailsStyle.detailRow}>
                                <Text style={productDetailsStyle.metaDataLabel}>
                                  {productDetails?.product_info?.text_weight}
                                </Text>
                                <Text
                                  style={[
                                    productDetailsStyle.metaDataLabel,
                                    productDetailsStyle.metaDataValue,
                                    productDetailsStyle.detailEvenRow,
                                  ]}>
                                  {productDetails?.product_info?.weight}
                                </Text>
                              </View>
                            )}
                          </>
                        )}
                      </>
                    ) : null}
                  </View>
                  {productDetails?.histories?.length > 0 && (
                    <>
                      <View style={commonStyles.accordionContainer}>
                        <Pressable
                          style={commonStyles.accordion}
                          onPress={() => handleUpDownIcon('auctionHistory')}>
                          <Text style={commonStyles.accordionText}>{trans('Auction History')}</Text>
                          {productInfoSections.auctionHistory.isShowing ? (
                            <CustomSVG svgIcon={UpIcon} height={iconSize} width={iconSize} />
                          ) : (
                            <CustomSVG
                              svgIcon={DownIcon}
                              height={iconSize}
                              width={iconSize}
                              fill={'#252525'}
                            />
                          )}
                        </Pressable>
                        {!productInfoSections.auctionHistory.isShowing && (
                          <View style={commonStyles.hrLine} />
                        )}
                      </View>
                      {productInfoSections.auctionHistory.isShowing && (
                        <>
                          {productDetails?.histories?.map((data, index) => (
                            <View
                              key={index}
                              style={
                                index < productDetails?.histories?.length - 1 && gapStyle.mb20
                              }>
                              <Text style={auctionDetailStyle.lightTextColor}>
                                {trans('Price')}{' '}
                                <Text style={auctionDetailStyle.boldText}>
                                  {data?.bidding_price}
                                </Text>
                              </Text>
                              <Text style={auctionDetailStyle.lightTextColor}>
                                {trans('From')}{' '}
                                <Text style={auctionDetailStyle.boldText}>
                                  {data?.bid_fullname}
                                </Text>{' '}
                                {trans('To')}{' '}
                                <Text style={auctionDetailStyle.boldText}>
                                  {data?.auction_fullname}
                                </Text>{' '}
                              </Text>
                              <Text style={auctionDetailStyle.lightTextColor}>
                                {trans('At')}{' '}
                                <Text style={auctionDetailStyle.boldText}>{data?.date_added}</Text>
                              </Text>
                            </View>
                          ))}
                        </>
                      )}
                    </>
                  )}
                  <View>
                    {productDetails?.lists?.length > 0 && (
                      <View style={productDetailsStyle.relatedProductWrapper}>
                        <View>
                          <Text style={productDetailsStyle.relatedProductTitle}>
                            {trans('Related Products')}
                          </Text>
                        </View>
                        <FlatList
                          key={`grid-double`}
                          data={productDetails?.lists}
                          keyExtractor={(_, i) => 'key-A' + i}
                          renderItem={memorizeItem}
                          showsVerticalScrollIndicator={false}
                          numColumns={2}
                          columnWrapperStyle={productListingStyle.justifyContent}
                          contentContainerStyle={{ rowGap: dpr(20) }}
                          refreshing={false}
                        />
                      </View>
                    )}
                  </View>
                </>
              )}
            </View>
          </>
        }
      />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0}>
        {(productDetails?.info?.is_allow_bid === 1 ||
          (productDetails.info?.is_bid_end === 0 &&
            productDetails?.info?.is_lock_buy_now_price === 0 &&
            productDetails?.info?.buy_now_price_num > 0)) && (
          <View style={auctionDetailStyle.bottomSectionsContainer}>
            {productDetails?.info?.is_allow_bid === 1 && (
              <View>
                <Text style={auctionDetailStyle.priceControlText}>
                  {trans('Please bid on items')}
                  {':'}
                </Text>
                <View style={auctionDetailStyle.priceControlSection}>
                  <ItemQuantity
                    startPrice={Number(productDetails?.info?.starting_price_num)}
                    increment={Number(productDetails?.info?.increment_num)}
                    bidPrice={bidPrice}
                    setBidPrice={setBidPrice}
                    bidRef={bidRef}
                  />
                  <Pressable
                    onPress={() => confirmAuction(0)}
                    style={[
                      productDetailsStyle.cartBtn,
                      productDetails?.info?.is_allow_bid
                        ? productDetailsStyle.externalLink
                        : productDetailsStyle.disabledAddToCart,
                    ]}
                    disabled={!productDetails?.info?.is_allow_bid}>
                    <Text
                      style={[
                        productDetailsStyle.cartBtnText,
                        productDetails?.info?.is_allow_bid
                          ? productDetailsStyle.externalLink
                          : productDetailsStyle.disabledAddToCart,
                      ]}>
                      {trans('Bid')}
                    </Text>
                  </Pressable>
                </View>
              </View>
            )}
            {productDetails.info?.is_bid_end === 0 &&
              productDetails?.info?.is_lock_buy_now_price === 0 &&
              productDetails?.info?.buy_now_price_num > 0 && (
                <AuctionButtons
                  buyNow={
                    productDetails.info?.is_bid_end === 0 &&
                    productDetails?.info?.is_lock_buy_now_price === 0 &&
                    productDetails?.info?.buy_now_price_num > 0
                  }
                  cartLoading={false}
                  handleAuction={confirmAuction}
                  allowBid={!!productDetails?.info?.is_allow_bid}
                />
              )}
          </View>
        )}
      </KeyboardAvoidingView>
      <CustomModal isVisible={isModalVisible} onCloseModal={() => setIsModalVisible(false)}>
        {modalConfig && <CustomModalContent {...modalConfig} />}
      </CustomModal>
    </>
  )
}

export default AuctionDetail
