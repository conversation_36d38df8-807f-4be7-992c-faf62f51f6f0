import React, { useCallback, useEffect, useRef, useState } from 'react'
import { View, FlatList } from 'react-native'

import BottomSheet from '@gorhom/bottom-sheet'
import { useAnimatedStyle, useSharedValue, withSpring } from 'react-native-reanimated'
import { NativeStackScreenProps } from '@react-navigation/native-stack'
import { RootParamList, useFocusEffect } from '@react-navigation/native'
import { searchFilterStyles } from '../Filter/SearchFilter1/searchFilter1.style'
import auctionListingStyle from './auctionListing.style'
import BackNavigation from '../Utilities/CustomHeader/BackNavigation'
import { productListingStyle } from '@/screens/Utilities/CommonStyles/productListing.style'
import { featureDisplayStyle } from '@/screens/Home/FeatureDIsplay/featureDisplay.style'
import EmptyContent from '@/screens/Utilities/EmptyContent/EmptyContent'
import CustomActiveIndicator from '@/src/components/CustomLoader/CustomActiveIndicator'
import useLangTranslation from '@/hooks/useLangTranslation'
import SingleProduct from '@/src/components/SingleProduct/SingleProduct'
import ProductSearchInput from '@/screens/Filter/FilterHome/ProductSearchInput'
import NoWishlistIcon from '@/assets/svgs/empty content/noWishlist.svg'
import Filters from '@/screens/Filter/Filters/Filters'
import SelectItemBottomSheet from '@/src/components/SelectItemBottomSheet/SelectItemBottomSheet'
import useAuth from '@/hooks/useAuth'

import { apiService } from '@/redux/slices/util/apiRequest'
import FadeInView from '@/src/components/FadeInView/FadeInView'
import CategoryTabs from '@/src/components/CategoryTabs/CategoryTabs'
import { Product } from '@/types/productDetailState'
import SelectInput from '@/src/components/CustomInput/SelectInput/SelectInput'
import CustomSVG from '@/src/components/CustomSVG/CustomSVG'
import DownArrowFill from '@/assets/svgs/downArrowFill.svg'
import AuctionLotListSkeleton from '@/src/skeletons/screens/auction/AuctionLotListSkeleton'
import AuctionResultListSkeleton from '@/src/skeletons/screens/auction/AuctionResultListSkeleton'
import { AuctionFilter, AuctionResultList, LotList } from '@/types/auction'
import LotListItem from '@/src/components/Auction/LotListItem'
import AuctionResultListItem from '@/src/components/Auction/AuctionResultListItem'
import dpr from '@/screens/Utilities/CustomStyleAttribute/dpr'
import { commonStyles } from '@/screens/Utilities/CommonStyles/common.styles'
import { gapStyle } from '@/screens/Utilities/CommonStyles/gap.style'

const URL = `${process.env.EXPO_PUBLIC_BASE_API_URL}/auctions`

type Props = NativeStackScreenProps<RootParamList, 'AuctionListing'>

interface Sort {
  name: string
  key: string | number
  sortOrder: string | null
}

const initFilter: AuctionFilter = {
  status: '',
  search: '',
  hot: '',
  latest: '',
  nft: '',
  sort: '',
  limit: '12',
  order: 'ASC',
  page: '1',
  cat: 'lot',
}

const AuctionListing = (props: Props) => {
  const { navigation } = props
  const { trans } = useLangTranslation()
  const routeFilter = props?.route?.params
  const { lng, currency } = useAuth()
  const allCategory = [
    {
      category_id: 1,
      parent_id: 0,
      name: trans('Lot'),
      categories: [],
    },
    {
      category_id: 2,
      parent_id: 0,
      name: trans('Auction Result'),
      categories: [],
    },
  ]
  const [attrFilters, setAttrFilters] = useState('')
  const { query } = apiService

  const [list, setList] = useState<LotList[] | AuctionResultList[]>([])
  const [loading, setLoading] = useState(true)
  const [loadMore, setLoadMore] = useState(false)
  const [pagination, setPagination] = useState({
    total: 0,
    page: 0,
    limit: 0,
  })
  const [tab, setTab] = useState({
    parent_category_id: 1,
    child_category_id: 0,
  })

  //filter
  const sortByRef = useRef<BottomSheet>(null)
  const sortByArray = [
    { name: trans('Sort By'), key: '', sortOrder: null },
    { name: trans('All'), key: '', sortOrder: null },
    { name: trans('Bidding'), key: 1, sortOrder: null },
    { name: trans('Ended'), key: 3, sortOrder: null },
    { name: trans('Sold'), key: 2, sortOrder: null },
    { name: trans("Filter-What's New"), key: 'latest', sortOrder: null },
    { name: trans('Most View'), key: 'hot', sortOrder: null },
    { name: trans('NFT Artwork'), key: 'nft', sortOrder: null },
    { name: trans('Date (New To Old)'), key: 'p.date_added', sortOrder: 'DESC' },

    { name: trans('Alphabetically, A-Z'), key: 'pd.name', sortOrder: 'ASC' },
    { name: trans('Alphabetically, Z-A'), key: 'pd.name', sortOrder: 'DESC' },
    { name: trans('Price (High to Low)'), key: 'p.price', sortOrder: 'DESC' },
    { name: trans('Price (Low to High)'), key: 'p.price', sortOrder: 'ASC' },
  ]
  const [searchText, setSearchText] = useState('')
  const [sortBy, setSortBy] = useState<Sort>(sortByArray[0])
  const [filters, setFilters] = useState(initFilter)
  const abortControllerRef = useRef<AbortController | null>(null)
  const prevFiltersRef = useRef<string>('')
  const requestIdRef = useRef<number>(0)

  const filterParams = (param: AuctionFilter) => {
    return new URLSearchParams(
      Object.entries(param).filter(([_, value]) => value !== '' && _ !== 'cat'),
    ).toString()
  }

  const handleFetchData = useCallback(
    async (param: AuctionFilter) => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
      abortControllerRef.current = new AbortController()
      const requestId = ++requestIdRef.current

      const tmpFilters = filterParams(param)
      const apiURL = tmpFilters.length > 0 ? `${URL}/${param?.cat}?${tmpFilters}` : URL
      try {
        setLoading(true)
        const result = await query(apiURL, 'GET', {
          'X-Oc-Merchant-Language': lng,
          'X-Oc-Currency': currency,
        })

        if (requestId !== requestIdRef.current) {
          return
        }

        const { data: resultData, success } = result || {}
        if (success === 1) {
          const { lists: resultList, pagination: resultPagination } = resultData
          setList(resultList)
          setPagination({
            total: resultPagination.total,
            page: resultPagination.page,
            limit: resultPagination.limit,
          })
          setFilters((prev) => ({ ...prev, page: String(resultPagination.page) }))
        }
      } catch (error) {
        if (error?.name !== 'AbortError') {
          console.error('Fetch error:', error)
        }
      } finally {
        if (requestId === requestIdRef.current) {
          setLoading(false)
        }
      }
    },
    [lng, currency, query],
  )

  const handleMoreData = useCallback(async () => {
    if (loading || pagination.total / Number(pagination.limit) <= pagination.page) {
      return
    }

    const currentRequestId = requestIdRef.current

    try {
      setLoadMore(true)
      const tmpFilters = Object.assign({}, filters)
      tmpFilters.page = String(Number(tmpFilters.page) + 1)
      tmpFilters.cat = tab.parent_category_id === 1 ? 'lot' : 'auction-result'

      const newURL = new URLSearchParams(
        Object.entries(tmpFilters).filter(([_, value]) => value !== '' && _ !== 'cat'),
      ).toString()

      const apiURL = newURL.length > 0 ? `${URL}/${tmpFilters.cat}?${newURL}&${attrFilters}` : URL

      const result = await query(apiURL, 'GET', {
        'X-Oc-Merchant-Language': lng,
        'X-Oc-Currency': currency,
      })

      if (currentRequestId !== requestIdRef.current) {
        return
      }

      const { data: resultData, success } = result || {}
      if (success === 1) {
        const { lists: resultList, pagination: resultPagination } = resultData
        setList((prevList) => prevList.concat(resultList))
        setPagination({
          total: resultPagination.total,
          page: resultPagination.page,
          limit: resultPagination.limit,
        })
        setFilters((prev) => ({ ...prev, page: String(resultPagination?.page) }))
      }
    } catch (error) {
      console.error(error)
    } finally {
      if (currentRequestId === requestIdRef.current) {
        setLoadMore(false)
      }
    }
  }, [loading, pagination, filters, tab.parent_category_id, attrFilters, query, lng, currency])

  const handleRefresh = async () => {
    const tmpFilters = Object.assign({}, filters)
    tmpFilters.page = '1'
    setFilters((prev) => ({ ...prev, page: '1' }))
    handleFetchData(tmpFilters)
  }

  const handleCategoryChange = (parentId: number, childId: number) => {
    setTab({
      parent_category_id: parentId,
      child_category_id: childId,
    })
    setSearchText('')
    const newFilters = {
      ...initFilter,
      cat: parentId === 1 ? 'lot' : 'auction-result',
      page: '1',
    }
    setFilters(newFilters)
    setSortBy(sortByArray[0])
    handleFetchData(newFilters)
  }

  const handleSortBy = (item: Sort) => {
    setSortBy(item)
    //in sorting UI, it is a mixture of filtering and sorting to get the api data
    // pd.nameASC, pd.nameDESC, p.priceAsc, p.priceDESC is sorting

    // Determine the type of filter parameter
    let newFilters = { ...filters }
    const specialSort = ['latest', 'hot', 'nft', 'sort_order']
    const isSpecialSort = specialSort.includes(item.key.toString())
    if (isSpecialSort) {
      newFilters = {
        ...newFilters,
        [item.key]: '1',
      }
      // Reset other filter parameters
      specialSort.forEach((key) => {
        if (key !== item.key) {
          delete newFilters[key as keyof typeof Filters]
        }
      })
      ;['order', 'sort', 'status'].forEach((key) => {
        if (newFilters[key as keyof typeof Filters]) {
          delete newFilters[key as keyof typeof Filters]
        }
      })
    } else if (typeof item.key === 'number') {
      newFilters = {
        ...newFilters,
        status: item.key,
      }
    } else {
      // handle sorting parameter
      newFilters = {
        ...newFilters,
        sort: item.key,
        order: item.sortOrder ?? 'ASC',
      }
      // Remove special sort parameters if they exist
      ;['latest', 'hot', 'nft', 'status'].forEach((key) => {
        delete newFilters[key as keyof typeof Filters]
      })
    }
    // reset page to 1
    newFilters = {
      ...newFilters,
      page: '1',
    }
    setFilters(newFilters)
    handleFetchData(newFilters)
  }

  useCallback(() => {
    const currentFilters = {
      ...filters,
      search: searchText,
    }
    handleFetchData(currentFilters)
  }, [filters, handleFetchData, searchText])

  useEffect(() => {
    const filtersWithoutPage = { ...filters }
    delete (filtersWithoutPage as Partial<AuctionFilter>).page
    const currentFiltersString = JSON.stringify(filtersWithoutPage)

    if (prevFiltersRef.current !== currentFiltersString) {
      handleFetchData(filters)
      prevFiltersRef.current = currentFiltersString
    }
  }, [filters, handleFetchData])

  useEffect(() => {
    if (attrFilters) {
      const newFilters = {
        ...filters,
        page: '1',
      }
      setFilters(newFilters)
      handleFetchData(newFilters)
    } else {
      handleFetchData(filters)
    }
  }, [attrFilters])

  useEffect(() => {
    const newFilters = {
      ...filters,
      page: '1',
      search: searchText,
    }
    setFilters(newFilters)
    handleFetchData(newFilters)
  }, [searchText])

  return (
    <FadeInView>
      <BackNavigation navigationProps={props.navigation} routeName={trans('Auction')} />
      <View style={[commonStyles.hrLineFull, gapStyle.mt0]} />
      <ProductSearchInput searchText={searchText} setSearchText={setSearchText} />
      <View style={productListingStyle.categoryContainer}>
        <CategoryTabs activeTab={tab} categories={allCategory} onPress={handleCategoryChange} />
      </View>
      <View
        style={[
          featureDisplayStyle.const,
          productListingStyle.filterComponentsContainer,
          auctionListingStyle.filterContainer,
        ]}>
        <View style={searchFilterStyles.sortContainer}>
          <SelectInput
            style={productListingStyle.dropdown1BtnStyle}
            placeholder={trans('{{x}}', { x: sortBy.name })}
            onPress={() => sortByRef.current?.snapToIndex(0)}
            title={sortBy?.name}
            icon={<CustomSVG svgIcon={DownArrowFill} fill={'#2C2C2C'} />}
          />
        </View>
      </View>
      <View style={[productListingStyle.const, auctionListingStyle.listContainer]}>
        <View>
          {loading ? (
            tab?.parent_category_id === 1 ? (
              <AuctionLotListSkeleton />
            ) : (
              <AuctionResultListSkeleton />
            )
          ) : list?.length > 0 ? (
            tab.parent_category_id === 1 ? (
              <FlatList
                key={'lot-list'}
                data={list as LotList[]}
                keyExtractor={(_, i) => 'key' + i}
                renderItem={({ item }) => <LotListItem item={item} isRelatedSection={false} />}
                showsVerticalScrollIndicator={false}
                initialNumToRender={12}
                windowSize={10}
                onEndReachedThreshold={1}
                onEndReached={handleMoreData}
                refreshing={false}
                onRefresh={handleRefresh}
                contentContainerStyle={{ rowGap: dpr(20) }}
              />
            ) : (
              <FlatList
                key={'auction-list'}
                data={list as AuctionResultList[]}
                keyExtractor={(_, i) => 'key' + i}
                renderItem={({ item }) => <AuctionResultListItem item={item} />}
                showsVerticalScrollIndicator={false}
                initialNumToRender={12}
                windowSize={10}
                onEndReachedThreshold={1}
                onEndReached={handleMoreData}
                refreshing={false}
                onRefresh={handleRefresh}
                contentContainerStyle={{ rowGap: dpr(20) }}
              />
            )
          ) : (
            <FlatList
              data={null}
              renderItem={null}
              refreshing={false}
              onRefresh={handleRefresh}
              showsVerticalScrollIndicator={false}
              ListHeaderComponent={
                <EmptyContent Icon={NoWishlistIcon} text={trans('No Auction Found')} />
              }
            />
          )}
          {loadMore && <CustomActiveIndicator />}
        </View>
      </View>
      <SelectItemBottomSheet
        snapPoint={[480]}
        selectRef={sortByRef}
        data={sortByArray}
        name={'SortBy'}
        onPress={handleSortBy}
      />
    </FadeInView>
  )
}

export default AuctionListing
