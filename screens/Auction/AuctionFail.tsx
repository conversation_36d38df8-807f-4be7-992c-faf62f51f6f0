import React, { useEffect, useState } from 'react'
import { NativeStackScreenProps } from '@react-navigation/native-stack'
import { View } from 'react-native-animatable'
import { Text } from 'react-native'
import { commonStyles } from '../Utilities/CommonStyles/common.styles'
import { gapStyle } from '../Utilities/CommonStyles/gap.style'
import useLangTranslation from '@/hooks/useLangTranslation'
import AuctionResult from '@/src/components/Auction/AuctionResult'
import { useAppDispatch } from '@/hooks/reduxHooks'
import useAuth from '@/hooks/useAuth'
import { AuctionResultProps } from '@/types/auction'
import { getMyAuctiondetail } from '@/redux/slices/auction/getMyAuctionDetail'
import { RootStackParamList } from '@/types/navigations'
import AuctionResultSkeleton from '@/src/skeletons/screens/auction/AuctionResultSkeleton'

type AuctionFailProps = NativeStackScreenProps<RootStackParamList, 'AuctionFail'>

const AuctionFail = (props: AuctionFailProps) => {
  const { trans } = useLangTranslation()

  const dispatch = useAppDispatch()
  const { lng, currency } = useAuth()
  const [data, setData] = useState<{
    name: string
    price: string
    date: string
    status: number
    bidId: string
    orderId?: string | undefined
    remark?: string | undefined
    payment_info?:
      | {
          information?: string | undefined
          remark?: string | undefined
          title?: string | undefined
        }
      | undefined
  }>()
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchBidData = async (id: number) => {
      try {
        const res = await dispatch(getMyAuctiondetail({ bidId: id, type: 'my-bid', lng, currency }))
        const { success, data: resData } = res?.payload || {}
        if (success) {
          setData({
            ...data,
            name: resData?.bid_info?.name,
            price: resData?.bid_order_info?.total,
            date: resData?.bid_order_info?.order_date,
            status: resData?.bid_info?.status,
            bidId: resData?.bid_order_info?.bid_id,
            payment_info: resData?.payment_info,
            orderId: resData?.bid_order_info?.order_no,
          })
        }
      } catch (error) {
        console.error(`fetchBidData`, error)
      } finally {
        setLoading(false)
      }
    }

    if (props.route.params?.bidId) {
      const { bidId } = props.route.params
      fetchBidData(bidId)
    }
  }, [props.route.params])

  return (
    <>
      <View style={commonStyles.container}>
        <View style={commonStyles.customHeaderContainer}>
          <Text style={commonStyles.headerName}>{trans('Payment')}</Text>
        </View>
      </View>
      <View style={[commonStyles.hrLineFull, gapStyle.mt0]} />
      {loading ? (
        <AuctionResultSkeleton />
      ) : (
        data && (
          <AuctionResult
            type="fail"
            title={trans('Sorry')}
            subtitle={trans('Your order is not completed. Please retry again.')}
            data={data}
          />
        )
      )}
    </>
  )
}

export default AuctionFail
