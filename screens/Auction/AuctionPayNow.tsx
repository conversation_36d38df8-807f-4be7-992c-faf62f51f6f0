import React, { useEffect, useState } from 'react'
import { View, Text } from 'react-native'
import { NativeStackScreenProps } from '@react-navigation/native-stack'
import { AUCTION_BIT_STATUS_LABEL } from './auctionStatus'
import ApplyWalletAddress from '../ShoppingCart/OrderSummary/ApplyWalletAddress'
import { orderDetailsStyle } from '@/screens/Profile/OrderHistory/OrderDetails/orderDetails.style'
import useAuth from '@/hooks/useAuth'
import useLangTranslation from '@/hooks/useLangTranslation'
import { commonStyles, dynamicStatusText } from '@/screens/Utilities/CommonStyles/common.styles'
import { orderConfirmedStyle } from '@/screens/ShoppingCart/OrderConfirmed/orderConfirmed.style'
import { gapStyle } from '@/screens/Utilities/CommonStyles/gap.style'
import useCustomToast from '@/hooks/useCustomToast'
import CustomButton from '@/src/components/CustomButton/CustomButton'
import { useAppDispatch, useAppSelector } from '@/hooks/reduxHooks'
import { RootStackParamList } from '@/types/navigations'
import SelectPaymentMethods from '@/src/components/SelectPaymentMethods/SelectPaymentMethods'
import {
  getAuctionPayNowSession,
  PayNowPayload,
  postAuctionPayNow,
} from '@/redux/slices/auction/paynow'
import { themeColor } from '@/theme/theme'
import AuctionPayNowSkeleton from '@/src/skeletons/screens/auction/AuctionPayNowSkeleton'

type Props = NativeStackScreenProps<RootStackParamList, 'AuctionPayNow'>

const AuctionPayNow = (props: Props) => {
  const { navigation } = props
  const { trans } = useLangTranslation()
  const { bidId } = props.route.params
  const dispatch = useAppDispatch()
  const { lng } = useAuth()
  const { payNowSession, loading } = useAppSelector((state) => state.auctionPayNowReducer || {})
  const showToast = useCustomToast()
  const [paymentMethod, setPaymentMethod] = useState('')
  const [walletAddress, setWalletAddress] = useState('')
  const [orderActionLoading, setOrderActionLoading] = useState(false)
  let isMounted = true

  const proceedToPayment = async () => {
    if (!paymentMethod) {
      showToast({
        text1: trans('Please select one payment method.'),
        type: 'common',
        position: 'bottom',
        props: { variant: 'error' },
      })
      return
    }

    try {
      setOrderActionLoading(true)
      const requestData = {
        bid_id: bidId,
        payment_method: paymentMethod,
      }
      const res = await dispatch(postAuctionPayNow({ requestData, lng }))
      const { success, data, error } = res?.payload || {}

      if (success === 1 && data?.[0]) {
        const { bid_id, payment_link, is_online } = data[0] as PayNowPayload

        if (is_online === false && bid_id) {
          navigation.navigate('AuctionSuccess', {
            bidId: bid_id,
          })
        } else {
          if (payment_link) {
            navigation.navigate('PaymentWebview', {
              url: payment_link,
            })
          }
        }
      } else {
        showToast({
          text1: error ? trans(error) : trans('Order process failed. Please try again.'),
          type: 'common',
          position: 'bottom',
          props: { variant: 'error' },
        })
      }
    } catch (error) {
      console.debug(error)
      showToast({
        text1: trans('Order process failed. Please try again.'),
        type: 'common',
        position: 'bottom',
        props: { variant: 'error' },
      })
    } finally {
      setOrderActionLoading(false)
    }
  }

  useEffect(() => {
    ;(async () => {
      if (isMounted) {
        await dispatch(getAuctionPayNowSession({ bidId, lng }))
      }
      return () => {
        isMounted = false
      }
    })()
  }, [])

  return (
    <>
      <View style={commonStyles.container}>
        <View style={commonStyles.customHeaderContainer}>
          <Text style={commonStyles.headerName}>{trans('Payment')}</Text>
        </View>
      </View>
      <View style={[commonStyles.hrLineFull, gapStyle.mt0]} />

      <View style={[orderDetailsStyle.bodyContainer, gapStyle.pb0]}>
        {loading ? (
          <AuctionPayNowSkeleton />
        ) : (
          <View style={[commonStyles.paddingSection, gapStyle.mb20]}>
            <View style={orderConfirmedStyle.topPosition}>
              <Text style={orderConfirmedStyle.topText1}>{trans('Order Summary')}</Text>
            </View>
            <View style={[orderDetailsStyle.deliveryCont, gapStyle.mb15]}>
              <View style={[orderDetailsStyle.deliverySubCont, orderDetailsStyle.borderStyle]}>
                <Text style={orderDetailsStyle.deliveryText1}>{trans('Product Name')}</Text>
                <Text style={orderDetailsStyle.fieldContent}>{payNowSession?.info?.name}</Text>
              </View>
              <View style={[orderDetailsStyle.deliverySubCont, orderDetailsStyle.borderStyle]}>
                <Text style={orderDetailsStyle.deliveryText1}>{trans('Grand Total')}</Text>
                <Text style={orderDetailsStyle.fieldContent}>
                  {payNowSession?.info?.bid_price_str}
                </Text>
              </View>
            </View>
            <View style={orderDetailsStyle.deliveryCont}>
              <View style={orderDetailsStyle.deliverySubCont}>
                <Text style={orderDetailsStyle.deliveryText1}>{trans('Order Date')}</Text>
                <Text style={orderDetailsStyle.deliveryText2}>{payNowSession?.info?.bid_time}</Text>
              </View>
              <View style={[orderDetailsStyle.deliverySubCont, orderDetailsStyle.borderStyle]}>
                <Text style={orderDetailsStyle.deliveryText1}>{trans('Payment Status')}</Text>
                <Text
                  style={[
                    orderDetailsStyle.deliveryText2,
                    {
                      color: dynamicStatusText(
                        AUCTION_BIT_STATUS_LABEL[payNowSession?.info?.status],
                      ),
                    },
                  ]}>
                  {payNowSession?.info?.status_str}
                </Text>
              </View>
            </View>

            <View style={gapStyle.mt20}>
              <SelectPaymentMethods
                paymentMethod={paymentMethod}
                setPaymentMethod={setPaymentMethod}
              />
            </View>
            <ApplyWalletAddress walletAddress={walletAddress} setWalletAddress={setWalletAddress} />
          </View>
        )}
        <View style={[orderDetailsStyle.bottomSectionRow, { marginTop: 'auto' }]}>
          <CustomButton
            onPress={() => navigation.goBack()}
            style={[orderConfirmedStyle.button, orderConfirmedStyle.homeBtn, gapStyle.mt0]}
            text={trans('Cancel')}
            textColor={themeColor.primaryText}
            loading={orderActionLoading}
            customIcon={require('@/assets/lottie/loadingBlack.json')}
          />
          <CustomButton
            onPress={proceedToPayment}
            style={[orderConfirmedStyle.button, orderConfirmedStyle.orderDetailBtn, gapStyle.mt0]}
            text={trans('Pay Now')}
            loading={orderActionLoading}
          />
        </View>
      </View>
    </>
  )
}

export default AuctionPayNow
