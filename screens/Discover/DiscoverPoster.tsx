import React from 'react'
import { Dimensions, ImageBackground, Pressable, Text, View } from 'react-native'
import { NavigationProp } from '@react-navigation/native'
import { discoverStyle } from './discover.style'
import { DiscoverState } from '@/redux/slices/discover/discover'
import { RootStackParamList } from '@/types/navigations'

interface DiscoverPosterProps {
  item: DiscoverState
  navigation: Omit<NavigationProp<RootStackParamList>, 'getState'>
  isRelatedDiscover?: boolean
}

const DiscoverPoster = ({ item, navigation, isRelatedDiscover = false }: DiscoverPosterProps) => {
  const { width } = Dimensions.get('window')
  return (
    <Pressable
      style={discoverStyle.item}
      onPress={() => {
        navigation.navigate('DiscoverDetail', { postId: item.post_id })
      }}>
      <ImageBackground
        source={{ uri: item.thumb }}
        style={[discoverStyle.itemBackground, isRelatedDiscover ? { height: width / 2 } : {}]}
      />
      <View style={discoverStyle.textSection}>
        <Text
          style={[
            discoverStyle.itemTitle,
            isRelatedDiscover ? discoverStyle.relatedItemTitle : {},
          ]}>
          {item.title}
        </Text>
        <Text style={discoverStyle.itemDate}>{`${item.day}/${item.month}/${item.year}`}</Text>
      </View>
    </Pressable>
  )
}

export default DiscoverPoster
