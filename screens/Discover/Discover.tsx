import { useEffect, useMemo, useRef, useState, useCallback } from 'react'
import { FlatList, Text, View } from 'react-native'
import { NativeStackScreenProps } from '@react-navigation/native-stack'
import { TFunction } from 'i18next'
import { discoverStyle } from './discover.style'
import DiscoverPoster from './DiscoverPoster'
import dpr from '../Utilities/CustomStyleAttribute/dpr'
import { gapStyle } from '@/screens/Utilities/CommonStyles/gap.style'
import EmptyContent from '@/screens/Utilities/EmptyContent/EmptyContent'
import BackNavigation from '@/screens/Utilities/CustomHeader/BackNavigation'
import { commonStyles } from '@/screens/Utilities/CommonStyles/common.styles'
import useLangTranslation from '@/hooks/useLangTranslation'
import { useAppDispatch, useAppSelector } from '@/hooks/reduxHooks'
import { getDiscover } from '@/redux/slices/discover/discover'
import useAuth from '@/hooks/useAuth'
import DiscoverSkeleton from '@/src/skeletons/screens/home/<USER>'
import { RootStackParamList } from '@/types/navigations'
import CategoryTabs from '@/src/components/CategoryTabs/CategoryTabs'
import { getDiscoverCategory } from '@/redux/slices/discover/discoverCategory'
import CategoryTabsSkeleton from '@/src/skeletons/screens/Categories/CategoryTabsSkeleton'
import NoWishlistIcon from '@/assets/svgs/empty content/noWishlist.svg'

type Props = NativeStackScreenProps<RootStackParamList, 'Discover'>

const ListHeaderComponent = ({ trans }: { trans: TFunction<'translation', undefined> }) => (
  <EmptyContent Icon={NoWishlistIcon} text={trans('No Discover Story Found')} />
)

const Discover = (props: Props) => {
  const { trans } = useLangTranslation()
  const { accessToken, lng } = useAuth()
  const dispatch = useAppDispatch()
  const { discoverList, loading } = useAppSelector((state) => state.discoverReducer)
  const { discoverCategoryList, loading: catLoading } = useAppSelector(
    (state) => state.discoverCategoryReducer,
  )
  const [tab, setTab] = useState(discoverCategoryList?.[0]?.category_id || 0)

  const memorizeItem = useMemo(() => DiscoverPoster, [])

  const handleRefresh = useCallback(() => {
    dispatch(getDiscoverCategory({ lng }))
    dispatch(getDiscover({ lng, post_category_id: tab > 0 ? tab : undefined }))
  }, [accessToken, lng, tab])

  const prevAccessTokenRef = useRef('')
  const prevLngRef = useRef('')

  useEffect(() => {
    const accessTokenChanged = accessToken !== prevAccessTokenRef.current
    const lngChanged = lng !== prevLngRef.current

    if (accessTokenChanged || lngChanged) {
      dispatch(getDiscoverCategory({ lng }))
      dispatch(getDiscover({ lng, post_category_id: tab > 0 ? tab : undefined }))

      prevAccessTokenRef.current = accessToken
      prevLngRef.current = lng
    } else if (tab !== undefined) {
      dispatch(getDiscover({ lng, post_category_id: tab > 0 ? tab : undefined }))
    }
  }, [accessToken, lng, tab])

  return (
    <View style={commonStyles.globalScreenContainer}>
      <BackNavigation navigationProps={props.navigation} routeName={trans('Discover')} />
      <View style={[commonStyles.hrLineFull, gapStyle.mt0]} />
      <View style={commonStyles.globalContainer}>
        <View style={discoverStyle.sectionTitle}>
          <Text style={discoverStyle.title}>{trans('Discover')}</Text>
        </View>
        <View style={discoverStyle.categoryContainer}>
          {catLoading ? (
            <CategoryTabsSkeleton />
          ) : (
            <CategoryTabs
              activeTab={tab}
              categories={discoverCategoryList}
              onPress={(activeTab) => setTab(activeTab)}
            />
          )}
        </View>
        {loading ? (
          <DiscoverSkeleton />
        ) : discoverList.length === 0 ? (
          <FlatList
            data={null}
            renderItem={null}
            refreshing={false}
            onRefresh={handleRefresh}
            showsVerticalScrollIndicator={false}
            ListHeaderComponent={<ListHeaderComponent trans={trans} />}
          />
        ) : (
          <FlatList
            data={discoverList}
            keyExtractor={(_, i) => `fp${i}`}
            renderItem={({ item }) => <DiscoverPoster item={item} navigation={props.navigation} />}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ gap: dpr(20) }}
            numColumns={1}
            initialNumToRender={3}
            refreshing={false}
            onRefresh={handleRefresh}
          />
        )}
      </View>
    </View>
  )
}

export default Discover
