import { StyleSheet } from 'react-native'

import { Dimensions } from 'react-native'
import dpr from '../Utilities/CustomStyleAttribute/dpr'
import { themeColor } from '@/theme/theme'
const { width } = Dimensions.get('screen')

export const discoverStyle = StyleSheet.create({
  activeTabLine: {
    borderBottomWidth: 3,
    borderColor: '#285128',
  },
  activeTabText: {
    color: themeColor.primaryText,
    fontFamily: 'DMSans_500Medium',
    fontSize: dpr(16),
    paddingBottom: dpr(3),
  },
  categoryContainer: {
    flexDirection: 'row',
    gap: dpr(20),
    marginBottom: dpr(20),
  },
  container: {
    paddingBottom: dpr(50),
    paddingHorizontal: dpr(20),
  },
  item: {
    flexDirection: 'column',
    padding: dpr(5),
    paddingBottom: dpr(30),
  },
  itemBackground: {
    aspectRatio: 3 / 2,
    marginBottom: dpr(20),
    resizeMode: 'contain',
    width: '100%',
  },
  itemDate: {
    color: themeColor.primaryText,
    fontFamily: 'DMSans_400Regular',
    fontSize: dpr(12),
    fontStyle: 'italic',
    lineHeight: dpr(16),
    marginTop: dpr(10),
  },
  itemTitle: {
    color: themeColor.primaryText,
    fontFamily: 'EBGaramond_400Regular',
    fontSize: dpr(28),
  },
  pb: {
    paddingBottom: dpr(30),
  },
  relatedItemTitle: {
    fontSize: dpr(14),
    fontWeight: '500',
  },
  sectionTitle: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: dpr(20),
    marginTop: dpr(30),
  },
  tabText: {
    color: themeColor.secondText,
    fontFamily: 'DMSans_400Regular',
    fontSize: dpr(16),
  },
  textSection: {
    maxWidth: width - dpr(80),
  },
  title: {
    fontFamily: 'DMSans_500Medium',
    fontSize: dpr(18),
    fontWeight: '500',
    lineHeight: dpr(23),
  },
  viewMore: {
    color: themeColor.secondText,
    fontFamily: 'DMSans_400Regular',
    fontSize: dpr(12),
    lineHeight: dpr(16),
  },
  viewMoreLoading: {
    color: themeColor.thirdText,
    fontFamily: 'DMSans_400Regular',
    fontSize: dpr(12),
    lineHeight: dpr(16),
  },
})
