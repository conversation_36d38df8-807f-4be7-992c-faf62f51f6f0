import { StyleSheet } from 'react-native'
import dpr from '../CustomStyleAttribute/dpr'
import { themeColor } from '@/theme/theme'

const gapStyle = StyleSheet.create({
  //margin top
  mt0: {
    marginTop: 0,
  },
  mt5: {
    marginTop: dpr(5),
  },
  mt10: {
    marginTop: dpr(10),
  },
  mt15: {
    marginTop: dpr(15),
  },
  mt19: {
    marginTop: dpr(19),
  },
  mt20: {
    marginTop: dpr(20),
  },
  mt25: {
    marginTop: dpr(25),
  },
  mt30: {
    marginTop: dpr(30),
  },

  //padding top
  pt24: {
    paddingTop: dpr(24),
  },

  //margin right
  mr25: {
    marginRight: dpr(25),
  },

  //margin bottom
  mb0: {
    marginBottom: 0,
  },
  mb5: {
    marginBottom: dpr(5),
  },
  mb10: {
    marginBottom: dpr(10),
  },
  mb15: {
    marginBottom: dpr(15),
  },
  mb20: {
    marginBottom: dpr(20),
  },
  mb22: {
    marginBottom: dpr(22),
  },
  mb30: {
    marginBottom: dpr(30),
  },

  //padding top
  pt0: {
    paddingTop: 0,
  },
  pt15: {
    paddingTop: dpr(15),
  },
  pt30: {
    paddingTop: dpr(30),
  },
  //padding bottom
  pb0: {
    paddingBottom: 0,
  },
  pb30: {
    paddingBottom: dpr(30),
  },
  pb50: {
    paddingBottom: dpr(50),
  },
  pb200: {
    paddingBottom: dpr(200),
  },
  pb350: {
    paddingBottom: dpr(350),
  },

  //margin left
  ml5: {
    marginLeft: dpr(5),
  },
  ml15: {
    marginLeft: dpr(15),
  },
  mlAuto: {
    marginLeft: 'auto',
  },
  mr5: {
    marginRight: dpr(5),
  },
})

const pb_0_10 = (index: number, length: number) => ({
  paddingBottom: index === length ? 0 : dpr(10),
  borderBottomWidth: index === length ? 0 : 1,
  borderBottomColor: index === length ? 'transparent' : themeColor.lightBorder,
})

export { gapStyle, pb_0_10 }
