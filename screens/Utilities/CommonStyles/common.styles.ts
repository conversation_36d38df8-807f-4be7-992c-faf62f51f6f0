import { Dimensions, StyleSheet, TextStyle, ViewStyle } from 'react-native'
import dpr from '../CustomStyleAttribute/dpr'
import { themeColor } from '@/theme/theme'

const { width } = Dimensions.get('screen')

export const commonStyles = StyleSheet.create({
  accordion: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: dpr(20),
  },
  accordionContainer: {
    marginTop: dpr(20),
  },
  accordionText: {
    color: themeColor.primaryText,
    fontFamily: 'DMSans_500Medium',
    fontSize: dpr(16),
  },
  aterisk: {
    color: themeColor.primaryWarning,
  },
  basicText: {
    color: themeColor.primaryText,
    fontFamily: 'DMSans_400Regular',
    fontSize: dpr(14),
  },
  bigHeaderText: {
    color: themeColor.primaryText,
    fontFamily: 'DMSans_500Medium',
    fontSize: dpr(28),
    fontWeight: '500',
  },
  blackBg: {
    backgroundColor: themeColor.secondaryBackground,
  },
  bottomSectionsContainer: {
    alignItems: 'center',
    backgroundColor: themeColor.primaryBackground,
    elevation: 24,
    flexDirection: 'row',
    gap: dpr(20),
    justifyContent: 'space-between',
    marginTop: 'auto',
    padding: dpr(20),
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 12,
    },
    shadowOpacity: 0.6,
    shadowRadius: 20,
    zIndex: 1,
  },
  cont: {
    backgroundColor: themeColor.primaryBackground,
    flex: 1,
  },
  container: {
    backgroundColor: themeColor.primaryBackground,
    height: dpr(64),
    paddingBottom: dpr(4),
    paddingTop: dpr(15),
  },
  customHeaderContainer: {
    alignItems: 'center',
    display: 'flex',
    flexDirection: 'row',
    height: dpr(40),
    justifyContent: 'center',
    position: 'relative',
  },
  customHeaderIcon: {
    height: dpr(30),
    left: dpr(0),
    paddingLeft: dpr(20),
    position: 'absolute',
    top: dpr(20),
    width: dpr(60),
  },
  customhubHeaderIcon: {
    paddingLeft: 0,
  },
  disabledButton: {
    backgroundColor: themeColor.secondText,
    color: themeColor.primaryColoredText,
  },
  flex: {
    flex: 1,
  },
  footerSpacing: {
    paddingBottom: dpr(85),
  },
  globalBodyContainer: {
    paddingHorizontal: dpr(20),
    paddingVertical: dpr(30),
  },
  globalContainer: {
    backgroundColor: themeColor.primaryBackground,
    flex: 1,
    paddingHorizontal: dpr(20),
  },
  globalScreenContainer: {
    backgroundColor: themeColor.primaryBackground,
    flex: 1,
  },
  hamburgerMenu: {
    paddingLeft: dpr(20),
    width: dpr(24),
    zIndex: 1,
  },
  headerLogo: {
    height: dpr(40),
    width: dpr(136),
  },
  headerName: {
    color: themeColor.primaryText,
    fontFamily: 'DMSans_700Bold',
    fontSize: dpr(20),
  },
  headerSection: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  hrLine: {
    borderBottomWidth: 1,
    borderColor: themeColor.hrLine,
    marginTop: dpr(20),
    width: width - dpr(30),
  },
  hrLineFull: {
    borderBottomWidth: 1,
    borderColor: themeColor.hrLine,
    marginTop: dpr(20),
    width: width,
  },
  hubContainer: {
    alignItems: 'center',
    backgroundColor: themeColor.hubBackground,
    flexDirection: 'row',
    marginVertical: dpr(15),
  },
  hubHeaderName: {
    color: themeColor.primaryColoredText,
    fontFamily: 'DMSans_700Bold',
    fontSize: dpr(20),
  },
  hubHrLine: {
    marginTop: 0,
    width: width - dpr(40),
  },
  iosHeaderLogo: {
    height: dpr(40),
    width: dpr(160),
  },
  menuContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    marginVertical: dpr(15),
  },
  menuIcon: {
    height: dpr(16),
    width: dpr(24),
  },
  menuPlaceholder: {
    width: dpr(24),
  },
  navigationContainer: {
    alignItems: 'flex-start',
    backgroundColor: themeColor.primaryBackground,
    flexDirection: 'row',
    height: dpr(64),
    paddingBottom: dpr(15),
    paddingTop: dpr(15),
  },
  paddingSection: {
    paddingHorizontal: dpr(20),
  },
  routeNameContainer: {
    alignItems: 'center',
    flex: 1,
    height: dpr(40),
    justifyContent: 'center',
  },
  secionTitleHrLine: {
    borderBottomColor: themeColor.primaryButton,
    borderBottomWidth: 3,
    marginTop: dpr(5),
    width: dpr(70),
  },
  sectionTitle: {
    fontFamily: 'DMSans_700Bold',
    fontSize: dpr(18),
    fontWeight: '700',
    lineHeight: dpr(23),
  },
  sectionTitleContainer: {
    marginVertical: dpr(30),
  },
  stickyButtonContainer: {
    paddingBottom: dpr(20),
    paddingHorizontal: dpr(20),
  },
})

export const buttonStyles = (bgColor?: string, color?: string) =>
  StyleSheet.create({
    container: {
      alignItems: 'center',
      backgroundColor: bgColor ?? themeColor.secondaryBackground,
      height: dpr(40),
      justifyContent: 'center',
      marginTop: dpr(30),
      minHeight: dpr(40),
      // padding: dpr(10),
    },
    text: {
      color: color ?? themeColor.primaryColoredText,
      fontFamily: 'Roboto_500Medium',
      fontSize: dpr(16),
      fontWeight: '500',
    },
  })

export const buttonfw = (bgColor: string = themeColor.secondaryBackground): ViewStyle => ({
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: bgColor,
  flex: 1,
  height: dpr(40),
  minHeight: dpr(40),
  marginTop: dpr(30),
  padding: dpr(10),
})

export const buttonfwText = (color: string = themeColor.primaryColoredText): TextStyle => ({
  color: color,
  fontFamily: 'Roboto_500Medium',
  fontSize: dpr(16),
  fontWeight: '500',
})

export const dynamicTextColor = (textColor: string): TextStyle => ({
  color: textColor,
})

export const formStyle = StyleSheet.create({
  labelText: {
    color: themeColor.primaryText,
    fontFamily: 'Roboto_500Medium',
    fontSize: dpr(16),
    marginBottom: dpr(10),
  },
  noteText: {
    color: themeColor.primaryWarning,
    fontFamily: 'Roboto_500Medium_Italic',
    fontSize: dpr(13),
    marginTop: dpr(5),
  },
  verifiText: {
    color: '#fff',
    fontSize: dpr(10),
  },
})

export const htmlStyles = StyleSheet.create({
  body: {
    color: themeColor.primaryText,
    fontFamily: 'DMSans_400Regular',
    fontSize: dpr(16),
    marginBottom: dpr(20),
  },
  br: {
    height: 2,
  },
  h1: {
    color: themeColor.primaryText,
    fontFamily: 'DMSans_500Medium',
    fontSize: dpr(18),
    fontWeight: '500',
  },
  h2: {
    color: themeColor.primaryText,
    fontFamily: 'DMSans_500Medium',
    fontSize: dpr(18),
    fontWeight: '500',
    margin: 0,
    padding: 0,
  },
  li: {
    marginBottom: dpr(20),
  },
  p: {
    color: themeColor.primaryText,
    fontFamily: 'DMSans_400Regular',
    fontSize: dpr(16),
  },
})

export const hubHtmlStyles = StyleSheet.create({
  body: {
    color: themeColor.primaryColoredText,
    fontSize: dpr(16),
  },
  br: {
    height: 2,
  },
  h1: {
    color: themeColor.primaryColoredText,
    fontFamily: 'DMSans_500Medium',
    fontSize: dpr(18),
    fontWeight: '500',
  },
  h2: {
    color: themeColor.primaryColoredText,
    fontFamily: 'DMSans_500Medium',
    fontSize: dpr(18),
    fontWeight: '500',
    margin: 0,
    padding: 0,
  },
  p: {
    color: themeColor.primaryColoredText,
    fontFamily: 'DMSans_400Regular',
    fontSize: dpr(16),
  },
})

export const dynamicStatusText = (status: string) => {
  if (!status) {
    return '#000000'
  }
  const statusStr = status.toLowerCase()
  return statusStr === 'failed' || statusStr === 'cancelled' || statusStr === 'reject'
    ? '#F22020'
    : statusStr === 'processing' ||
        statusStr === 'pending payment' ||
        statusStr === 'pending' ||
        statusStr === 'in auction'
      ? '#DEA512'
      : statusStr === 'complete' ||
          statusStr === 'completed' ||
          statusStr === 'paid' ||
          statusStr === 'accept' ||
          statusStr === 'accepted, payment pending' ||
          statusStr === 'refunded' ||
          statusStr === 'sold' ||
          statusStr === 'ended'
        ? '#198755'
        : statusStr === 'on hold'
          ? '#FD7F0C'
          : '#000000'
}

export const skeletonWrapper = (height: number, minusWidth: number) => ({
  height: dpr(height),
  width: dpr(minusWidth),
})

export const mb5 = (error: string | undefined | boolean) => ({
  marginBottom: error ? dpr(5) : 0,
})
