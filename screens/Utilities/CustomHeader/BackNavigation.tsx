import React from 'react'
import { View, Text, Pressable, StyleSheet } from 'react-native'
import Share from 'react-native-share'
import dpr from '@/screens/Utilities/CustomStyleAttribute/dpr'
import CustomSVG from '@/src/components/CustomSVG/CustomSVG'
import BackIcon from '@/assets/svgs/back_arrow.svg'
import HubBackIcon from '@/assets/svgs/hub_back_arrow.svg'
import ShareIcon from '@/assets/svgs/icon_share.svg'

type Props = {
  navigationProps?: any
  routeName: string
  capitalize?: boolean
  hubRouting?: boolean
  backActions?: () => void
  shareLink?: string
}

const BackNavigation = (props: Props) => {
  const { navigationProps, routeName, capitalize, hubRouting, backActions, shareLink } = props

  const splitRouteName = routeName.split(' ')
  const nameArray = splitRouteName.map((cap) => cap.charAt(0).toUpperCase() + cap.slice(1))
  const capitalizeString = nameArray.join(' ')

  const backScreen = () => {
    if (backActions) {
      backActions()
    } else {
      navigationProps.goBack()
    }
  }

  const handleShare = async () => {
    await Share.open({ url: shareLink })
  }

  return (
    <View style={hubRouting ? styles.hubContainer : styles.container}>
      <View style={styles.titleWrap}>
        <Text style={hubRouting ? styles.hubTitle : styles.title}>
          {capitalize
            ? capitalizeString
            : routeName === 'NFT Detail'
              ? 'DETAIL'
              : routeName.toUpperCase()}
        </Text>
      </View>
      {navigationProps && (
        <Pressable
          onPress={() => backScreen()}
          style={hubRouting ? styles.hubIconWrap : styles.iconWrap}>
          <CustomSVG
            svgIcon={hubRouting ? HubBackIcon : BackIcon}
            height={dpr(16)}
            width={dpr(16)}
          />
        </Pressable>
      )}
      {shareLink && (
        <Pressable onPress={handleShare} style={styles.sharIconWrap}>
          <CustomSVG svgIcon={ShareIcon} isRtl={1} height={dpr(16)} width={dpr(16)} />
        </Pressable>
      )}
    </View>
  )
}

export default BackNavigation

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    flexDirection: 'row',
    height: dpr(64),
  },
  hubContainer: {
    alignItems: 'center',
    backgroundColor: '#393939',
    flexDirection: 'row',
    height: dpr(64),
  },
  hubIconWrap: {
    height: dpr(30),
    left: dpr(0),
    paddingLeft: dpr(20),
    position: 'absolute',
    top: dpr(24),
    width: dpr(60),
    zIndex: 9999,
  },
  hubTitle: {
    color: '#FFFFFF',
    fontFamily: 'DMSans_700Bold',
    fontSize: dpr(20),
    paddingHorizontal: dpr(15),
    textAlign: 'center',
  },
  iconWrap: {
    backgroundColor: 'rgba(255, 255, 255,0.5)',
    height: dpr(30),
    left: dpr(0),
    paddingLeft: dpr(20),
    position: 'absolute',
    top: dpr(24),
    width: dpr(60),
    zIndex: 9999,
  },
  sharIconWrap: {
    height: dpr(30),
    paddingLeft: dpr(20),
    position: 'absolute',
    right: dpr(0),
    top: dpr(24),
    width: dpr(60),
    zIndex: 9999,
  },
  title: {
    color: '#252525',
    fontFamily: 'DMSans_700Bold',
    fontSize: dpr(20),
    fontWeight: '700',
    paddingHorizontal: dpr(15),
    textAlign: 'center',
  },
  titleWrap: {
    flex: 1,
    textAlign: 'center',
  },
})
