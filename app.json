{"expo": {"name": "TMR Arts", "slug": "tmrarts", "owner": "tmrartsdev", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "scheme": "com.tmrarts", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#FFFFFF"}, "updates": {"fallbackToCacheTimeout": 0}, "assetBundlePatterns": ["**/*"], "ios": {"icon": "./assets/ios.icon.png", "supportsTablet": false, "bundleIdentifier": "com.tmrarts", "buildNumber": "4", "infoPlist": {"NSPhotoLibraryUsageDescription": "The app accesses your photos to let you share them with your friends.", "LSApplicationQueriesSchemes": ["com.tmrarts", "mailto", "tel", "metamask", "trust", "safe", "rainbow", "uniswap"], "ITSAppUsesNonExemptEncryption": false}, "entitlements": {"aps-environment": "development"}}, "android": {"versionCode": 6, "adaptiveIcon": {"foregroundImage": "./assets/icon.png", "backgroundColor": "#FFFFFF"}, "package": "com.tmrarts", "permissions": ["android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE"], "blockedPermissions": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION", "android.permission.ACCESS_BACKGROUND_LOCATION", "android.permission.READ_PHONE_STATE", "android.permission.QUERY_ALL_PACKAGES", "android.permission.GET_TASKS", "android.permission.ACCESS_WIFI_STATE", "android.permission.READ_MEDIA_IMAGES", "android.permission.READ_MEDIA_VIDEO"]}, "web": {"favicon": "./assets/favicon.png"}, "userInterfaceStyle": "automatic", "extra": {"eas": {"projectId": "dae3b059-a7c2-47af-9639-f55831c848f8"}}}}