import { StyleSheet } from 'react-native'
import dpr from '../../../screens/Utilities/CustomStyleAttribute/dpr'
import { themeColor } from '@/theme/theme'
import { Styles } from '@/types/style'

export const photoGalleryStyle = StyleSheet.create({
  downloadButton: {
    padding: 10,
    position: 'absolute',
    tintColor: themeColor.primaryBackground,
    top: dpr(40),
    zIndex: 999,
    right: dpr(60),
  },
  castButton: {
    height: 24,
    padding: 10,
    position: 'absolute',
    right: dpr(20),
    tintColor: themeColor.primaryBackground,
    top: dpr(50),
    width: 24,
    zIndex: 999,
  },
  castButtonContainer: {
    alignItems: 'center',
    borderRadius: 20,
    padding: 5,
  },
  castButtonText: {
    color: 'white',
    marginLeft: 5,
  },
  closeIcon: {
    padding: 10,
    position: 'absolute',
    left: dpr(10),
    top: dpr(50),
    zIndex: 999,
  },
  cont: {
    alignItems: 'center',
    marginBottom: dpr(15),
    width: dpr('wf'),
  },
  footerCont: {
    alignItems: 'center',
    flexGrow: 1,
    justifyContent: 'center',
    width: dpr('wf'),
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    left: 0,
    padding: 10,
    position: 'absolute',
    right: 0,
    top: 50,
    zIndex: 1,
  },
  headerContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    zIndex: 999,
  },
  loading: {
    left: '56.25%',
    position: 'absolute',
    top: '56.25%',
    transform: [{ translateX: -50 }, { translateY: -50 }],
  },
  modalCont: {
    width: dpr('wf'),
  },
})

export const footerImage = (image: number, index: number) => ({
  height: dpr(70),
  width: dpr(70),
  borderWidth: image == index ? 3 : 0,
  borderColor: image == index ? '#FCCA19' : '#000',
  marginRight: dpr(10),
  borderRadius: 6,
})
