import React, { useEffect, useRef, useState } from 'react'
import { ActivityIndicator, Alert, Modal, Platform, Pressable, ScrollView } from 'react-native'
import ImageViewer from 'react-native-image-zoom-viewer'
import {
  useRemoteMediaClient,
  CastContext,
  CastButton,
  PlayServicesState,
} from 'react-native-google-cast'
import { useNavigationState } from '@react-navigation/native'
import { View } from 'react-native-animatable'
import * as FileSystem from 'expo-file-system'
import * as MediaLibrary from 'expo-media-library'
import { photoGalleryStyle } from './customPhotoGallery.style'
import RenderHeader from './RenderHeader'
import CustomSVG from '../CustomSVG/CustomSVG'
import CustomModalContent, { ModalContent } from '../CustomModal/CustomModalContent'
import CustomModal from '../CustomModal/CustomModal'
import downloadImageStyle from '../HubMediaGallery/downloadImage.style'
import DownloadImage from '../HubMediaGallery/DownloadImage'
import { DownloadContent } from '../NFTDetail/NFTDetail'
import { themeColor } from '@/theme/theme'
import dpr from '@/screens/Utilities/CustomStyleAttribute/dpr'
import DownloadIcon from '@/assets/svgs/icon_download.svg'
import HUB_ROUTING from '@/components/Navigation/hubRouting'
import { HubsProductImages, ProductImages } from '@/types/productDetailState'

interface CustomPhotoGalleryProps {
  isOpen: boolean
  setIsOpen: (isOpen: boolean) => void
  images: ProductImages[] | HubsProductImages[]
  index: number
  setCurrentSlideIndex: (index: number) => void
  canDownload?: boolean
  downloadContent?: DownloadContent[]
  canDownloadImage?: boolean
  canDownloadVideo?: boolean
}

export const PlayerServicesState = {
  DISABLED: 'disabled',
  INVALID: 'invalid',
  MISSING: 'missing',
  SUCCESS: 'success',
  UPDATE_REQUIRED: 'updateRequired',
  UPDATING: 'updating',
}

const CustomPhotoGallery = ({
  isOpen,
  setIsOpen,
  images,
  index,
  setCurrentSlideIndex,
  canDownload = false,
  downloadContent,
  canDownloadImage = false,
  canDownloadVideo = false,
}: CustomPhotoGalleryProps) => {
  const [updateIndex, setUpdateIndex] = useState(index)
  const [isCastAvailable, setIsCastAvailable] = useState<PlayServicesState>(
    PlayServicesState.INVALID,
  )
  const client = useRemoteMediaClient()

  const pathName = useNavigationState((state) => state.routes[state.index].name)
  const isHubRouting = HUB_ROUTING.test(pathName)

  const newImages = images?.map((item) => {
    return {
      url: 'preview' in item ? item.preview : 'image' in item ? item.image : '',
    }
  })

  const [isDLModalVisible, setIsDLModalVisible] = useState(false)
  const [modalConfig, setModalConfig] = useState<ModalContent | null>(null)
  const [downloading, setDownloading] = useState(false)
  const [isDLAvailable, setIsDLAvailable] = useState(false)

  useEffect(() => {
    const loadImages = async () => {
      if (isOpen && client) {
        try {
          client?.loadMedia({
            mediaInfo: {
              contentUrl: 'image' in images[0] ? images[0].image : '',
              contentType: 'image/jpg',
              metadata: {
                type: 'generic' as const,
                title: 'name' in images[0] ? images[0].name : '',
                images: [{ url: newImages[0]?.url }],
              },
            },
          })
        } catch (error) {
          console.error('Error loading image queue:', error)
        }
      }
    }

    loadImages()
  }, [isOpen, client])

  useEffect(() => {
    const isImageAvail =
      !!downloadContent?.find((content) => /jpg|jpeg|png/.test(content.extension)) &&
      canDownloadImage
    const isVideoAvail =
      !!downloadContent?.find((content) => content.extension === 'mp4') && canDownloadVideo
    setIsDLAvailable(isImageAvail || isVideoAvail)
  }, [downloadContent])

  //cast another image on carousel change
  const handleImageChange = (index?: number) => {
    if (index !== undefined) {
      setUpdateIndex(index)
      const imageAtIndex = images?.[index]
      if (!imageAtIndex) return
      const currentImageUri = 'image' in imageAtIndex ? imageAtIndex.image : ''

      try {
        client?.loadMedia({
          mediaInfo: {
            contentUrl: currentImageUri,
            contentType: 'image/jpg',
            metadata: {
              type: 'generic' as const,
              title: 'name' in imageAtIndex ? imageAtIndex.name : '',
              images: [{ url: newImages[index]?.url }],
            },
          },
        })
      } catch (error) {
        console.error('Error casting image:', error)
      }
    }
  }

  const handleDownloadImage = () => {
    setModalConfig({
      message: '',
      content: DownloadContentModal,
      type: 'info',
      buttons: [
        {
          text: 'Back',
          onPress: () => {
            setIsDLModalVisible(false)
          },
          type: 'secondary',
        },
      ],
    })
    setIsDLModalVisible(true)
  }

  const downloadImage = async (content: DownloadContent) => {
    try {
      setDownloading(true)

      const cleanFileName = content.name.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase()
      const fileUri = FileSystem.documentDirectory + cleanFileName + '.' + content.extension

      // Download the image
      const { uri } = await FileSystem.downloadAsync(content.image, fileUri)
      // Save the image to media library
      await MediaLibrary.createAssetAsync(uri)
      Alert.alert('Success', 'Image downloaded successfully!')
    } catch (error) {
      console.error(error)
      Alert.alert('Error', 'Failed to download image.')
    } finally {
      setDownloading(false)
    }
  }

  useEffect(() => {
    //only apply for android, ios returns undefined
    CastContext.getPlayServicesState().then((state) => {
      setIsCastAvailable(state as PlayServicesState)
    })
  }, [])

  const DownloadContentModal = (
    <ScrollView
      contentContainerStyle={downloadImageStyle.container}
      showsVerticalScrollIndicator={false}
      style={downloadImageStyle.modalContainer}>
      {downloadContent?.map((item, index) => {
        if (item.extension === 'mp4' && canDownloadVideo) {
          return <DownloadImage content={item} key={index} downloadImage={downloadImage} />
        } else if (/jpg|jpeg|png/.test(item.extension) && canDownloadImage) {
          return <DownloadImage content={item} key={index} downloadImage={downloadImage} />
        }
      })}
    </ScrollView>
  )

  return (
    <Modal visible={isOpen} style={photoGalleryStyle.modalCont}>
      <ImageViewer
        imageUrls={newImages}
        index={updateIndex}
        renderHeader={() => (
          <RenderHeader
            setIsOpen={setIsOpen}
            updateIndex={updateIndex}
            setCurrentSlideIndex={setCurrentSlideIndex}
          />
        )}
        renderIndicator={() => <></>}
        onChange={handleImageChange}
        saveToLocalByLongPress={false}
      />
      {(Platform.OS === 'ios' || isCastAvailable === PlayerServicesState.SUCCESS) &&
        isHubRouting && <CastButton style={photoGalleryStyle.castButton} />}
      {canDownload && isDLAvailable && (
        <Pressable onPress={() => handleDownloadImage()} style={photoGalleryStyle.downloadButton}>
          <CustomSVG svgIcon={DownloadIcon} height={dpr(24)} width={dpr(20)} />
        </Pressable>
      )}
      <CustomModal isVisible={isDLModalVisible} onCloseModal={() => setIsDLModalVisible(false)}>
        {modalConfig && <CustomModalContent {...modalConfig} />}
        {downloading && (
          <View
            style={{
              zIndex: 3,
              position: 'absolute',
              backgroundColor: 'rgba(0, 0, 0, 0.2)',
              padding: 20,
              borderRadius: 10,
              top: '50%',
              transform: [{ translateY: -dpr(30) }],
            }}>
            <ActivityIndicator size="large" color={themeColor.primaryButton} />
          </View>
        )}
      </CustomModal>
    </Modal>
  )
}

export default CustomPhotoGallery
