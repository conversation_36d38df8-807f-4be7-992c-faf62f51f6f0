import React from 'react'
import { memo } from 'react'
import { View, Text, Pressable } from 'react-native'
import { useNavigation } from '@react-navigation/native'
import { singleProductListViewStyle } from './singleProductListView.style'
import ProgressiveImage from '../ProgressiveImage'
import CustomSVG from '../CustomSVG/CustomSVG'
import { ProductsStyle } from '@/screens/Home/FeaturedProducts/featureProducts.style'
import dpr from '@/screens/Utilities/CustomStyleAttribute/dpr'
import { singleProductStyle } from '@/src/components/SingleProduct/singleProduct.style'
import useLangTranslation from '@/hooks/useLangTranslation'
import NFTBadgeIcon from '@/assets/svgs/nft_badge.svg'
import { Product } from '@/types/productDetailState'

const SingleProductListView = ({ item }: { item: Product }) => {
  const navigation = useNavigation()
  const productsStyle = ProductsStyle()
  const { trans } = useLangTranslation()
  return (
    <Pressable
      style={singleProductListViewStyle.container}
      onPress={() => {
        navigation.navigate('ProductDetails', { productId: item?.product_id })
      }}>
      <View style={singleProductStyle.imageContainer}>
        <View style={{ position: 'relative' }}>
          {/* {item?.is_nft && (
            <CustomSVG
              svgIcon={NFTBadgeIcon}
              width={dpr(24)}
              height={dpr(24)}
              style={{ position: 'absolute', top: 0, left: 4, zIndex: 1 }}
            />
          )} */}
          <ProgressiveImage source={{ uri: item?.image }} style={singleProductStyle.img} />
        </View>
      </View>

      <View style={singleProductListViewStyle.infoContainer}>
        <Text style={productsStyle.text} numberOfLines={2}>
          {item?.name}
        </Text>
        <Text style={productsStyle.productNo}>
          {trans('product no: ')}
          {item?.model}
        </Text>
        <Text style={productsStyle.priceTag}>{item?.price}</Text>
      </View>
    </Pressable>
  )
}

export default memo(SingleProductListView)
