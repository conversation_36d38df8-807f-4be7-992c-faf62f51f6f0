import React from 'react'
import { memo } from 'react'
import { View, Text, Pressable } from 'react-native'
import { NativeStackNavigationProp } from '@react-navigation/native-stack'
import { useNavigation } from '@react-navigation/native'
import { ProductsStyle } from '../../../screens/Home/FeaturedProducts/featureProducts.style'
import { singleProductStyle } from './singleProduct.style'
import ProgressiveImage from '../ProgressiveImage'
import { PRODUCT_DETAILS } from '../../../components/Navigation/RouteNames'
import useLangTranslation from '../../../hooks/useLangTranslation'
import CustomSVG from '../CustomSVG/CustomSVG'
import { ProductState } from '@/redux/slices/featureProducts/featureProducts'
import { RootStackParamList } from '@/types/navigations'
import NFTBadgeIcon from '@/assets/svgs/nft_badge.svg'
import dpr from '@/screens/Utilities/CustomStyleAttribute/dpr'
import { Product } from '@/types/productDetailState'

interface SingeProductProps {
  item: Product
}

const SingleProduct = (props: SingeProductProps) => {
  const { item } = props || {}
  const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>()
  const { product_id, thumb, name, price, model, is_nft } = item || {}

  const { trans } = useLangTranslation()

  const productsStyle = ProductsStyle()

  return (
    <View style={productsStyle.item}>
      <Pressable
        onPress={() => {
          navigation?.push(PRODUCT_DETAILS, { productId: product_id })
        }}>
        <View style={singleProductStyle.imageContainer}>
          <View style={{ position: 'relative' }}>
            {/* {is_nft && (
              <CustomSVG
                svgIcon={NFTBadgeIcon}
                width={dpr(24)}
                height={dpr(24)}
                style={{ position: 'absolute', top: 0, left: 4, zIndex: 1 }}
              />
            )} */}
            <ProgressiveImage source={{ uri: thumb }} style={singleProductStyle.img} />
          </View>
        </View>
        <Text style={productsStyle.text} numberOfLines={1}>
          {name}
        </Text>
        <Text style={productsStyle.productNo} numberOfLines={1}>
          {trans('product no: ')}
          {model}
        </Text>
        <Text style={productsStyle.priceTag}>{price ?? 0}</Text>
      </Pressable>
    </View>
  )
}

export default memo(SingleProduct)
