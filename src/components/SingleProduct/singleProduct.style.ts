import { StyleSheet, Dimensions } from 'react-native'
import dpr from '../../../screens/Utilities/CustomStyleAttribute/dpr'

const { width } = Dimensions.get('screen')
export const singleProductStyle = StyleSheet.create({
  featuredBadge: {
    backgroundColor: '#FF6C2E',
    borderRadius: 2,
    color: '#FFFFFF',
    fontFamily: 'Roboto_500Medium',
    fontSize: dpr(10),
    marginBottom: dpr(5),
    paddingHorizontal: dpr(7),
    paddingVertical: 3,
    textAlign: 'center',
  },
  imageContainer: {
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    flexDirection: 'row',
    height: width / 2 - dpr(100),
    justifyContent: 'center',
    marginBottom: dpr(10),
    position: 'relative',
    width: width / 2 - dpr(30),
  },
  img: {
    height: width / 2 - dpr(100),
    width: width / 2 - dpr(30),
  },
  nftImageContainer: {
    backgroundColor: 'black',
    borderRadius: dpr(10),
  },
  nftImg: {
    height: '100%',
    width: '100%',
    resizeMode: 'contain',
    borderRadius: dpr(10),
  },
  offBadge: {
    backgroundColor: '#FCCA19',
    borderRadius: 2,
    color: '#252525',
    fontFamily: 'Roboto_500Medium',
    fontSize: dpr(10),
    marginBottom: dpr(5),
    paddingHorizontal: dpr(7),
    paddingVertical: 3,
    textAlign: 'center',
  },
  offerBadge: {
    left: dpr(10),
    position: 'absolute',
    top: dpr(10),
    zIndex: 1,
  },
  outStockBadge: {
    backgroundColor: '#F9E8E8',
    borderRadius: 2,
    color: '#C8191C',
    fontFamily: 'Roboto_500Medium',
    fontSize: dpr(10),
    paddingHorizontal: dpr(7),
    paddingVertical: 3,
    textAlign: 'center',
  },
  topRatedBadge: {
    color: '#FFFFFF',
    fontFamily: 'Roboto_500Medium',
    fontSize: dpr(10),
    marginLeft: dpr(2),
  },
  topRatedBadgeCont: {
    alignItems: 'center',
    backgroundColor: '#00B14F',
    borderRadius: 2,
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: dpr(5),
    paddingHorizontal: dpr(7),
    paddingVertical: 3,
  },
})