import React, { useEffect, useRef, useState } from 'react'
import { Pressable, Image, View, Modal, Platform, Alert, ActivityIndicator, FlatList } from 'react-native'
import { ResizeMode, Video } from 'expo-av'
import CastContext, {
  CastButton,
  MediaRepeatMode,
  MediaStreamType,
  PlayServicesState,
  useRemoteMediaClient,
} from 'react-native-google-cast'
import { ScrollView } from 'react-native-gesture-handler'
import * as FileSystem from 'expo-file-system'
import * as MediaLibrary from 'expo-media-library'
import CustomSVG from '../CustomSVG/CustomSVG'
import { photoGalleryStyle } from '../CustomImageGallery/customPhotoGallery.style'
import CustomModal from '../CustomModal/CustomModal'
import CustomModalContent, { ModalContent } from '../CustomModal/CustomModalContent'
import { DownloadContent } from '../NFTDetail/NFTDetail'
import DownloadImage from './DownloadImage'
import downloadImageStyle from './downloadImage.style'
import dpr from '@/screens/Utilities/CustomStyleAttribute/dpr'
import { itemCarouselStyle } from '@/screens/Home/FeaturedProducts/ProductDetails/ItemCarousel/itemCarousel.style'
import LeftArrowIcon from '@/assets/svgs/lefticon_white.svg'
import DownloadIcon from '@/assets/svgs/icon_download.svg'
import PlayIcon from '@/assets/svgs/video_light_2.svg'
import { PlayerServicesState } from '@/src/components/CustomImageGallery/CustomPhotoGallery'
import { themeColor } from '@/theme/theme'
import { NFTImage } from '@/types/hubDetailState'
import SingleImage from '@/screens/Home/FeaturedProducts/ProductDetails/ItemCarousel/SingleImage'
import { useNavigationState } from '@react-navigation/native'
import HUB_ROUTING from '@/components/Navigation/hubRouting'

interface HubMediaGalleryProps {
  item: {
    image: string
    image_cache: string
    video: string
    name: string
  }
  carouselData: any
  canDownload: boolean
  downloadContent: DownloadContent[]
  canDownloadImage?: boolean
  canDownloadVideo?: boolean
}

const HubMediaGallery = ({
  item,
  carouselData,
  canDownload,
  downloadContent,
  canDownloadImage,
  canDownloadVideo,
}: HubMediaGalleryProps) => {
  const client = useRemoteMediaClient()
  const [isOpen, setIsOpen] = useState(false)
  const video = useRef<Video>(null)
  const [isCastAvailable, setIsCastAvailable] = useState<PlayServicesState>(
    PlayServicesState.INVALID,
  )
  const [isDLModalVisible, setIsDLModalVisible] = useState(false)
  const [currentSlideIndex, setCurrentSlideIndex] = useState(0)
  const [modalConfig, setModalConfig] = useState<ModalContent | null>(null)
  const [downloading, setDownloading] = useState(false)
  const [isDLAvailable, setIsDLAvailable] = useState(false)
  const pathName = useNavigationState((state) => state.routes[state.index].name)
  
  const isHubRouting = HUB_ROUTING.test(pathName)

  useEffect(() => {
    const isImageAvail =
      !!downloadContent?.find((content) => /jpg|jpeg|png/.test(content.extension)) &&
      canDownloadImage
    const isVideoAvail =
      !!downloadContent?.find((content) => content.extension === 'mp4') && canDownloadVideo
    setIsDLAvailable(!!(isImageAvail || isVideoAvail))
  }, [downloadContent])

  const setOpen = () => {
    setIsOpen(true)
  }

  const loadVideo = async () => {
    try {
      const queueItem = {
        mediaInfo: {
          contentUrl: item?.video,
          contentType: 'video/mp4',
          metadata: {
            type: 'generic' as const,
            title: item?.name,
            images: [{ url: item?.image_cache }],
          },
        },
        mediaStreamType: MediaStreamType.BUFFERED,
      }

      await client?.loadMedia({
        queueData: {
          items: [queueItem],
          repeatMode: MediaRepeatMode.ALL,
        },
      })
    } catch (error) {
      console.error('Error setting up looping video:', error)
    }
  }

  const downloadImage = async (content: DownloadContent) => {
    try {
      setDownloading(true)

      const cleanFileName = content.name.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase()
      const fileUri = FileSystem.documentDirectory + cleanFileName + '.' + content.extension

      // Download the image
      const { uri } = await FileSystem.downloadAsync(content.image, fileUri)
      // Save the image to media library
      await MediaLibrary.createAssetAsync(uri)
      Alert.alert(
        'Success',
        `${content.extension.toLowerCase() === 'mp4' ? 'Video' : 'Image'} downloaded successfully!`,
      )
    } catch (error) {
      console.error(error)
      Alert.alert('Error', 'Failed to download image.')
    } finally {
      setDownloading(false)
    }
  }

  const DownloadContentModal = (
    <ScrollView
      contentContainerStyle={downloadImageStyle.container}
      showsVerticalScrollIndicator={false}
      style={downloadImageStyle.modalContainer}>
      {downloadContent?.map((item, index) => {
        if (item.extension === 'mp4' && canDownloadVideo) {
          return <DownloadImage content={item} key={index} downloadImage={downloadImage} />
        } else if (/jpg|jpeg|png/.test(item.extension) && canDownloadImage) {
          return <DownloadImage content={item} key={index} downloadImage={downloadImage} />
        }
      })}
    </ScrollView>
  )

  const handleDownloadImage = () => {
    setModalConfig({
      message: '',
      content: DownloadContentModal,
      type: 'info',
      buttons: [
        {
          text: 'Back',
          onPress: () => {
            setIsDLModalVisible(false)
          },
          type: 'secondary',
        },
      ],
    })
    setIsDLModalVisible(true)
  }

  const updateCurrentSlideIndex = (event: { nativeEvent: { contentOffset: { x: any }; layoutMeasurement: { width: any } } }) => {
    const contentOffsetX = event.nativeEvent.contentOffset.x; // 当前滚动的位置
    const viewSize = event.nativeEvent.layoutMeasurement.width; // 可见区域的宽度
    const currentIndex = Math.floor(contentOffsetX / viewSize); // 计算当前索引
    setCurrentSlideIndex(currentIndex); // 更新状态
  };

  useEffect(() => {
    if (isOpen && client) {
      loadVideo()
    }
  }, [isOpen, client])

  useEffect(() => {
    //only apply for android, ios returns undefined
    CastContext.getPlayServicesState().then((state) => {
      setIsCastAvailable(state as PlayServicesState)
    })
  }, [])

  return (
    <>
      <View style={itemCarouselStyle.singleImgCont}>
        {carouselData ? (
          // <>
          //   <Image
          //     source={{
          //       uri: item?.image || undefined,
          //     }}
          //     style={itemCarouselStyle.imageSize}
          //   />
          //   <CustomSVG
          //     svgIcon={PlayIcon}
          //     height={dpr(60)}
          //     width={dpr(60)}
          //     style={itemCarouselStyle.playerIcon}
          //   />
          // </>
          <>
            <FlatList
              data={carouselData}
              key={`grid-${carouselData?.length}`}
              keyExtractor={(item, index) => index.toString()} // 使用唯一ID作为key
              renderItem={({ item, index }) => {
                return (
                  <>
                    {item.animation_url ? (
                      <Pressable onPress={setOpen}>
                        <Image
                          source={{
                            uri: item.image || undefined,
                          }}
                          style={itemCarouselStyle.imageSize}
                        />
                          <CustomSVG
                            svgIcon={PlayIcon}
                            height={dpr(60)}
                            width={dpr(60)}
                            style={itemCarouselStyle.playerIcon}
                          />
                      </Pressable>
                    ) : <SingleImage
                        item={item}
                        index={index}
                        allImages={carouselData}
                        setCurrentSlideIndex={setCurrentSlideIndex}
                        canDownload={canDownload}
                        downloadContent={downloadContent}
                        canDownloadImage={canDownloadImage}
                        canDownloadVideo={canDownloadVideo}
                  />}
                  </>
                );
              }}
              horizontal
              pagingEnabled
              scrollEnabled={true}
              snapToAlignment="center"
              scrollEventThrottle={16}
              decelerationRate={'fast'}
              showsHorizontalScrollIndicator={false}
              onMomentumScrollEnd={updateCurrentSlideIndex}
              overScrollMode="never"
            />
            {Object.keys(carouselData).length > 1 && (
              <View style={itemCarouselStyle.indidatorCont}>
                {carouselData.map((_: any, index: number) => (
                  <View
                    key={`key${index}`}
                    style={[
                      itemCarouselStyle.indicator,
                      currentSlideIndex === index && itemCarouselStyle.activeIndicator,
                    ]}
                  />
                ))}
              </View>
            )}
          </>

        ) : (
          <Image
            // eslint-disable-next-line @typescript-eslint/no-require-imports
            source={require('@/assets/images/image-loader-icon.png')}
            style={itemCarouselStyle.imageSize}
          />
        )}
      </View>
      <Modal
        style={{ zIndex: 1 }}
        visible={isOpen}
        onShow={() => {
          video?.current?.playAsync()
        }}>
        <Pressable
          onPress={() => {
            setIsOpen(false)
          }}
          style={photoGalleryStyle.closeIcon}>
          <CustomSVG svgIcon={LeftArrowIcon} height={dpr(15)} width={dpr(15)} />
        </Pressable>
        {(Platform.OS === 'ios' || isCastAvailable === PlayerServicesState.SUCCESS) && (
          isHubRouting && <CastButton style={photoGalleryStyle.castButton} />
        )}
        {canDownload && isDLAvailable && (
          <Pressable
            onPress={() => {
              handleDownloadImage()
            }}
            style={photoGalleryStyle.downloadButton}>
            <CustomSVG svgIcon={DownloadIcon} height={dpr(24)} width={dpr(20)} />
          </Pressable>
        )}
        <View
          style={{
            flex: 1,
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: '#252525',
          }}>
          <Video
            ref={video}
            style={{ alignSelf: 'center', width: dpr('wf'), height: 275 }}
            source={{
              uri: item?.video,
            }}
            resizeMode={ResizeMode.CONTAIN}
            isLooping
            useNativeControls={false}
          />
          <CustomModal isVisible={isDLModalVisible} onCloseModal={() => setIsDLModalVisible(false)}>
            {modalConfig && <CustomModalContent {...modalConfig} />}
            {downloading && (
              <View
                style={{
                  zIndex: 3,
                  position: 'absolute',
                  backgroundColor: 'rgba(0, 0, 0, 0.2)',
                  padding: 20,
                  borderRadius: 10,
                  top: '50%',
                  transform: [{ translateY: -dpr(30) }],
                }}>
                <ActivityIndicator size="large" color={themeColor.primaryButton} />
              </View>
            )}
          </CustomModal>
        </View>
      </Modal>
    </>
  )
}

export default HubMediaGallery
