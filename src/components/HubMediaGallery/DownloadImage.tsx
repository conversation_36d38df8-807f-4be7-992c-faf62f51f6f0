import React from 'react'
import { View, Image, Text, Pressable } from 'react-native'
import { commonStyles } from '@/screens/Utilities/CommonStyles/common.styles'
import CustomSVG from '../CustomSVG/CustomSVG'
import DownloadIcon from '@/assets/svgs/icon_download_black.svg'
import downloadImageStyle from './downloadImage.style'
import { DownloadContent } from '../NFTDetail/NFTDetail'

interface DownloadImageProps {
  content: DownloadContent
  downloadImage: (content: DownloadContent) => Promise<void>
}

const DownloadImage = ({ content, downloadImage }: DownloadImageProps) => {
  return (
    <Pressable onPress={() => downloadImage(content)} style={downloadImageStyle.row}>
      <View style={downloadImageStyle.productContainer}>
        <Image source={{ uri: content.thumb }} style={downloadImageStyle.img} />
        <View style={{ flexDirection: 'column', rowGap: 5 }}>
          <Text style={commonStyles.basicText}>{content.name}</Text>
          <Text style={commonStyles.basicText}>{content.extension.toUpperCase()}</Text>
        </View>
      </View>
      <CustomSVG svgIcon={DownloadIcon} style={{ width: 20, height: 20 }} />
    </Pressable>
  )
}

export default DownloadImage
