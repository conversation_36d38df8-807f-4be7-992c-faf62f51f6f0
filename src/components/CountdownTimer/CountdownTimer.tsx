import React, { useState, useEffect, useMemo, useCallback } from 'react'
import { Text, TextStyle, View } from 'react-native'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'
import duration from 'dayjs/plugin/duration'
import { Styles } from '@/types/style'
import dpr from '@/screens/Utilities/CustomStyleAttribute/dpr'
import { themeColor } from '@/theme/theme'
import useLangTranslation from '@/hooks/useLangTranslation'

// Initialize plugins
dayjs.extend(utc)
dayjs.extend(timezone)
dayjs.extend(duration)

interface CountdownProps {
  targetDate: string | Date
  targetTimezone?: string // e.g., 'Asia/Tokyo', 'America/New_York'
  onComplete?: () => void
  style?: Styles
  timerStyle?: TextStyle
  timerUnitStyle?: TextStyle
  inlineLabel?: boolean
}

const CountdownTimer: React.FC<CountdownProps> = ({
  targetDate,
  onComplete,
  timerStyle,
  timerUnitStyle,
  inlineLabel = false,
}) => {
  const userTimezone = dayjs.tz.guess()

  const { trans } = useLangTranslation()

  // Memoize the target date in the specified timezone
  const targetDateTime = useMemo(() => {
    return dayjs(targetDate)
  }, [targetDate])

  // Calculate time difference
  const calculateTimeLeft = useCallback(() => {
    const now = dayjs()

    if (userTimezone !== 'Asia/Hong_Kong') {
      now.tz('Asia/Hong_Kong')
    }

    const diff = targetDateTime.diff(now)

    if (diff <= 0) {
      return {
        days: 0,
        hours: 0,
        minutes: 0,
        seconds: 0,
      }
    }

    const timeDiff = dayjs.duration(diff)
    return {
      days: Math.floor(timeDiff.asDays()),
      hours: Math.floor(timeDiff.asHours() % 24),
      minutes: timeDiff.minutes(),
      seconds: timeDiff.seconds(),
    }
  }, [targetDateTime])

  const [timeLeft, setTimeLeft] = useState(calculateTimeLeft())

  // Format numbers with leading zeros
  const formatNumber = useCallback((num: number) => {
    return num.toString().padStart(2, '0')
  }, [])

  // Memoize formatted time strings
  const timeStrings = useMemo(
    () => ({
      days: formatNumber(timeLeft.days),
      hours: formatNumber(timeLeft.hours),
      minutes: formatNumber(timeLeft.minutes),
      seconds: formatNumber(timeLeft.seconds),
    }),
    [timeLeft, formatNumber],
  )

  useEffect(() => {
    const timer = setInterval(() => {
      const newTimeLeft = calculateTimeLeft()
      setTimeLeft(newTimeLeft)

      // Check if countdown is complete
      if (Object.values(newTimeLeft).every((v) => v === 0)) {
        onComplete?.()
        clearInterval(timer)
      }
    }, 1000)

    return () => clearInterval(timer)
  }, [calculateTimeLeft, onComplete])

  const TimeUnit = ({ value, label }: { value: string; label: string }) => (
    <View style={{ flexDirection: inlineLabel ? 'row' : 'column', alignItems: 'center' }}>
      <Text
        style={{
          fontSize: inlineLabel ? dpr(16) : dpr(28),
          color: themeColor.lightGreen,
          ...timerStyle,
        }}>
        {value}
      </Text>
      <Text
        style={{
          fontSize: inlineLabel ? dpr(16) : dpr(12),
          color: inlineLabel ? themeColor.lightGreen : themeColor.secondText,

          ...timerUnitStyle,
        }}>
        {label}
      </Text>
    </View>
  )

  const Separator = () => (
    <Text
      style={{
        fontSize: dpr(32),
        color: themeColor.lightGreen,
        marginHorizontal: dpr(2),
        marginTop: -2,
        ...timerStyle,
      }}>
      :
    </Text>
  )

  return (
    <View
      style={{
        paddingVertical: dpr(5),
        flexDirection: 'row',
        alignItems: 'flex-start',
      }}>
      {timeLeft.days > 0 ? (
        <>
          <TimeUnit value={timeStrings.days} label={inlineLabel ? 'd' : trans('Days')} />
          <Separator />
        </>
      ) : (
        <Text>0</Text>
      )}
      <TimeUnit value={timeStrings.hours} label={inlineLabel ? 'h' : trans('Hours')} />
      <Separator />
      <TimeUnit value={timeStrings.minutes} label={inlineLabel ? 'm' : trans('Minutes')} />
      <Separator />
      <TimeUnit value={timeStrings.seconds} label={inlineLabel ? 's' : trans('Seconds')} />
    </View>
  )
}

// Helper functions for common timezone operations
const TimezoneHelpers = {
  // Get user's local timezone
  getLocalTimezone: () => dayjs.tz.guess(),

  // Convert time between timezones
  convertTime: (date: Date | string, fromTz: string, toTz: string) => {
    return dayjs.tz(date, fromTz).tz(toTz)
  },

  // Check if date is past in specific timezone
  isPast: (date: Date | string) => {
    const targetDate = dayjs(date)
    const currentDate = dayjs.tz.guess() !== 'Asia/Hong_Kong' ? dayjs.tz('Asia/Hong_Kong') : dayjs()
    return targetDate.isBefore(currentDate)
  },
}

export { TimezoneHelpers }
export default CountdownTimer
