import React, { useCallback, memo } from 'react'
import BottomSheet, {
  BottomSheetBackdrop,
  useBottomSheetSpringConfigs,
  BottomSheetHandle,
  BottomSheetFlatList,
  BottomSheetBackdropProps,
} from '@gorhom/bottom-sheet'
import { StyleProp, ViewStyle } from 'react-native'
import { useSharedValue } from 'react-native-reanimated'
import { bottomSheetStyles } from './bottomSheet.style'

interface CustomBottomSheetProps {
  style?: StyleProp<ViewStyle>
  bsRef: React.RefObject<any>
  bgColor?: string
  bottomInset?: number
  children: React.ReactNode
  isScrollable?: boolean
  isHeaderComponent?: boolean
  headerComponent?: React.ReactNode
  snapIndex?: number
  onChange?: (index: number) => void
}

const CustomBottomSheet = ({
  style = {},
  bsRef,
  bgColor = '#FFFFFF',
  bottomInset,
  snapIndex,
  children,
  isScrollable = false,
  isHeaderComponent = false,
  headerComponent,
  onChange,
}: CustomBottomSheetProps) => {
  const bottomSheetStyle = bottomSheetStyles(bgColor)
  const animatedIndex = useSharedValue(0)
  const animatedPosition = useSharedValue(0)

  const renderBackdrop = useCallback((props: BottomSheetBackdropProps) => {
    return (
      <BottomSheetBackdrop {...props} appearsOnIndex={0} disappearsOnIndex={-1} opacity={0.2} />
    )
  }, [])

  const animationConfigs = useBottomSheetSpringConfigs({
    damping: 80,
    overshootClamping: true,
    restDisplacementThreshold: 0.1,
    restSpeedThreshold: 0.1,
    stiffness: 700,
  })
  const renderBottomSheetHandle = () => {
    return (
      <BottomSheetHandle
        style={bottomSheetStyle.bottomSheetStyle}
        indicatorStyle={bottomSheetStyle.bottomSheetIndicator}
        animatedIndex={animatedIndex}
        animatedPosition={animatedPosition}
      />
    )
  }

  return (
    <BottomSheet
      ref={bsRef}
      index={snapIndex ?? -1}
      enablePanDownToClose={true}
      backdropComponent={renderBackdrop}
      animationConfigs={animationConfigs}
      handleComponent={renderBottomSheetHandle}
      bottomInset={bottomInset ?? 0}
      backgroundStyle={bottomSheetStyle.bottomSheet}
      enableDynamicSizing={true}
      onChange={onChange}>
      {isHeaderComponent && typeof headerComponent === 'object' && headerComponent}
      <BottomSheetFlatList
        keyboardShouldPersistTaps="always"
        onLayout={isScrollable ? undefined : () => {}}
        contentContainerStyle={[bottomSheetStyle.contentContainer, style]}
        ListHeaderComponent={() => <>{children}</>}
        scrollEnabled={true}
        data={[]}
        renderItem={null}
      />
    </BottomSheet>
  )
}

export default memo(CustomBottomSheet)
