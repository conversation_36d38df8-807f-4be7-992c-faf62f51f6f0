import { View, Text, TouchableWithoutFeedback, StyleSheet } from 'react-native'
import Toast from 'react-native-toast-message'
import { Ionicons } from '@expo/vector-icons'
import { styles } from './toastConfig.styles'
import dpr from '../../../screens/Utilities/CustomStyleAttribute/dpr'
import useLangTranslation from '../../../hooks/useLangTranslation'
import { themeColor } from '@/theme/theme'

export const toastConfig = {
  common: ({ text1, props }) => {
    const handleHideToast = () => {
      Toast.hide()
    }

    const extraStyles = Styles(props)

    return (
      <View style={[styles.container, extraStyles.container]}>
        <Text style={[styles.text1, styles.commonText1]}>{text1}</Text>
        <TouchableWithoutFeedback onPress={() => handleHideToast()}>
          <Ionicons name="close" size={20} color="#898989" />
        </TouchableWithoutFeedback>
      </View>
    )
  },
  addToCart: ({ text1, text2, props }) => {
    return (
      <View style={styles.container}>
        <Text style={[styles.text1, styles.cartText]}>{text1}</Text>
        <TouchableWithoutFeedback
          onPress={() => {
            props.func()
          }}>
          <View style={styles.cartBtn}>
            <Text style={styles.btnColor}>{text2}</Text>
          </View>
        </TouchableWithoutFeedback>
      </View>
    )
  },
  undo: ({ text1, props }) => {
    const { trans } = useLangTranslation()
    const extraStyles = Styles(props)
    return (
      <View style={[styles.container, extraStyles.paddingHorizontal]}>
        <Text style={[styles.undoText1, extraStyles.paddingLeft]}>{text1}</Text>
        <TouchableWithoutFeedback
          onPress={() => {
            props.func()
          }}>
          <View style={styles.undoBtn}>
            <Text>{trans('Undo')}</Text>
          </View>
        </TouchableWithoutFeedback>
      </View>
    )
  },
}

const Styles = (props) =>
  StyleSheet.create({
    container: {
      backgroundColor:
        props.variant === 'success'
          ? 'rgba(44, 44, 44, 0.95)'
          : props.variant === 'error'
            ? themeColor.primaryWarning
            : 'rgba(44, 44, 44, 0.95)',
    },
    paddingHorizontal: {
      paddingHorizontal: 0,
    },
    paddingLeft: {
      paddingLeft: dpr(12),
    },
  })
