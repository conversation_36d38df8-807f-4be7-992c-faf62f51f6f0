import { StyleSheet } from 'react-native'
import dpr from '../../../screens/Utilities/CustomStyleAttribute/dpr'
import { themeColor } from '@/theme/theme'

export const styles = StyleSheet.create({
  btnColor: {
    color: themeColor.secondyButton,
  },
  cartBtn: {
    // width: dpr(70),
  },
  cartText: {
    width: dpr('wf') - dpr(100),
  },
  commonText1: {
    width: dpr('wf') - dpr(70),
  },
  container: {
    alignItems: 'center',
    backgroundColor: 'rgba(44, 44, 44, 0.95)',
    flexDirection: 'row',
    gap: dpr(10),
    justifyContent: 'space-between',
    minHeight: dpr(48),
    paddingHorizontal: dpr(12),
    width: dpr('wf'),
  },
  redColor: {
    color: 'red',
  },
  text1: {
    alignSelf: 'center',
    color: '#FFFFFF',
    fontFamily: dpr(13),
    fontFamily: 'DMSans_500Medium',
    lineHeight: dpr(18),
  },
  undoBtn: {
    alignItems: 'center',
    backgroundColor: '#FCCA19',
    height: '100%',
    justifyContent: 'center',
    paddingHorizontal: dpr(3),
    width: dpr(70),
  },
  undoText1: {
    alignSelf: 'center',
    color: '#FFFFFF',
    fontFamily: dpr(13),
    fontFamily: 'DMSans_500Medium',
    lineHeight: dpr(18),
    width: dpr('wf') - dpr(100),
  },
})
