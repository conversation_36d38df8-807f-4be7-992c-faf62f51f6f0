import React, { useEffect, useState } from 'react'
import { View, Pressable, Text, Linking } from 'react-native'
import { ScrollView } from 'react-native-gesture-handler'

import { useAccount } from 'wagmi'
import { NativeStackNavigationProp } from '@react-navigation/native-stack'
import HubMediaGallery from '../HubMediaGallery/HubMediaGallery'
import DownIcon from '@/assets/svgs/dropdown/hub_down.svg'
import UpIcon from '@/assets/svgs/dropdown/hub_up.svg'
import LinkIcon from '@/assets/svgs/link_alt.svg'
import LikedEmptyIcon from '@/assets/svgs/unLike.svg'
import LikedFillIcon from '@/assets/svgs/liked.svg'
import CustomSVG from '@/src/components/CustomSVG/CustomSVG'
import { commonStyles } from '@/screens/Utilities/CommonStyles/common.styles'
import { themeColor } from '@/theme/theme'
import { gapStyle } from '@/screens/Utilities/CommonStyles/gap.style'
import ItemHeader from '@/screens/Home/FeaturedProducts/ProductDetails/ItemDetails/ItemSection/ItemHeader'
import { productDetailsStyle } from '@/screens/Home/FeaturedProducts/ProductDetails/productDetails.style'
import ItemCarousel from '@/screens/Home/FeaturedProducts/ProductDetails/ItemCarousel/ItemCarousel'

import useLangTranslation from '@/hooks/useLangTranslation'
import { NFTAttributes, NFTImage, NFTItems } from '@/types/hubDetailState'
import dpr from '@/screens/Utilities/CustomStyleAttribute/dpr'
import { apiService } from '@/redux/slices/util/apiRequest'
import useAuth from '@/hooks/useAuth'
import useCustomToast from '@/hooks/useCustomToast'
import { useAppDispatch, useAppSelector } from '@/hooks/reduxHooks'
import { RootStackParamList } from '@/types/navigations'
import { FilterRawList } from '@/screens/Filter/Filters/HubFilters'
import { ExhibitionHallData } from '@/types/exhibition'
import {
  setHubAttrfilters as setPublicFilters,
  toggleHubFilterSelection as togglePublicFilter,
} from '@/redux/slices/hub/hubPublicFilter'
import {
  setHubAttrfilters as setPrivateFilters,
  toggleHubFilterSelection as togglePrivateFilter,
} from '@/redux/slices/hub/hubPrivateFilter'
import {
  setHubAttrfilters as setShowroomFilters,
  toggleHubFilterSelection as toggleShowroomFilter,
} from '@/redux/slices/hub/hubShowroomFilter'
import {
  setHubAttrfilters as setExhibitionFilters,
  toggleHubFilterSelection as toggleExhibitionFilter,
} from '@/redux/slices/hub/hubExhibitionFilter'

interface NFTDetailProps {
  item: NFTItems | ExhibitionHallData
  navigation: NativeStackNavigationProp<RootStackParamList, 'HubDetail'>
  canDownload: boolean
  canDownloadImage: boolean
  canDownloadVideo: boolean
  screen: string
  exhibitionHallId: number
}

export interface DownloadContent {
  id: number
  image: string
  thumb: string
  extension: string
  name: string
}

const iconSize = dpr(16)
const URL = `${process.env.EXPO_PUBLIC_BASE_API_URL}/hub/nft/like`

const NFTDetail = (props: NFTDetailProps) => {
  const {
    item,
    navigation,
    canDownload,
    canDownloadImage,
    canDownloadVideo,
    screen,
    exhibitionHallId,
  } = props
  const { mutation } = apiService
  const { lng } = useAuth()
  const { address } = useAccount()
  const showToast = useCustomToast()
  const dispatch = useAppDispatch()
  const { hubUIFilters: publicFilters } = useAppSelector((state) => state.hubPublicFilterReducer)
  const { hubUIFilters: privateFilters } = useAppSelector((state) => state.hubPrivateFilterReducer)
  const { hubUIFilters: showroomFilters } = useAppSelector(
    (state) => state.hubShowroomFilterReducer,
  )
  const { hubUIFilters: exhibitionFilters } = useAppSelector(
    (state) => state.hubExhibitionFilterReducer,
  )
  const [toggleNFT, setToggleNFT] = useState({
    like_total: item?.like_total,
    is_like: item?.is_like,
  })
  const [downloadContent, setDownloadContent] = useState<DownloadContent[]>([] as DownloadContent[])

  const itemCarouselData = {
    images: [
      {
        image: item?.image,
        animation_url: item?.animation_url,
      },
      ...item?.images || [],
    ],
    activeImage: 0,
  }

  const publicMuseumCarouseData = {
    images: [
      {
        image: item?.image,
        animation_url: item?.animation_url,
      },
    ],
    activeImage: 0,
  }

  const hubGalleryData = {
    image: item?.image,
    image_cache: item?.image_cache,
    video: item?.animation_url,
    name: item?.name,
  }

  const itemHeaderData = {
    name: item?.name,
    productNo: '',
  }
  const { trans } = useLangTranslation()

  const [productInfoSections, setProductInfoSections] = useState({
    description: {
      name: 'Description',
      isShowing: true,
    },
    details: {
      name: 'Details',
      isShowing: false,
    },
    product_info: {
      name: 'Product Info',
      isShowing: false,
    },
  })

  const handleUpDownIcon = (key: string) => {
    setProductInfoSections({
      ...productInfoSections,
      [key]: {
        ...productInfoSections[key as keyof typeof productInfoSections],
        isShowing: !productInfoSections[key as keyof typeof productInfoSections].isShowing,
      },
    })
  }

  const toggleLike = async () => {
    if (!address) {
      showToast({
        text1: 'Please connect to your wallet first.',
        type: 'common',
        position: 'bottom',
        props: { variant: 'error' },
      })
      return
    }
    try {
      const apiURL = `${URL}/${item?.id}`
      const result = await mutation(
        apiURL,
        'PUT',
        {
          wallet_address: address,
        },
        lng,
      )
      if (result.success === 1) {
        setToggleNFT({
          like_total: result.data.like_total,
          is_like: result.data.is_like,
        })
      }
    } catch (error) {
      console.error('error', error)
    }
  }

  const getHubUIFilters = (screen: string) => {
    switch (screen) {
      case 'PublicMuseum':
        return publicFilters
      case 'PrivateMuseum':
        return privateFilters
      case 'Showroom':
        return showroomFilters
      case 'ExhibitionDetail':
        return exhibitionFilters
    }
  }

  const handleSelectItem = (item: NFTAttributes) => {
    // update UI Filter
    //switch case for setting up the uifilter
    const hubUIFilters = getHubUIFilters(screen)
    if (!hubUIFilters) return

    hubUIFilters.find((filter: FilterRawList) => {
      return filter.trait_type === item.trait_type
    })
    const attrId = hubUIFilters
      .find((filter: FilterRawList) => filter.trait_type === item.trait_type)
      ?.attributes.find(
        (subFilter: NFTAttributes) => subFilter.value === item.value,
      )?.nft_attribute_id
    if (attrId) {
      switch (screen) {
        case 'PublicMuseum':
          dispatch(togglePublicFilter({ parentCat: item?.trait_type, id: attrId }))
          break
        case 'PrivateMuseum':
          dispatch(togglePrivateFilter({ parentCat: item?.trait_type, id: attrId }))
          break
        case 'Showroom':
          dispatch(toggleShowroomFilter({ parentCat: item?.trait_type, id: attrId }))
          break
        case 'ExhibitionDetail':
          dispatch(toggleExhibitionFilter({ parentCat: item?.trait_type, id: attrId }))
          break
      }
    }
    //filter format for nft attributes
    const filterObject = [{ trait_type: item.trait_type, active: true, attributes: [item.value] }]
    switch (screen) {
      case 'PublicMuseum':
        dispatch(setPublicFilters(filterObject))
        break
      case 'PrivateMuseum':
        dispatch(setPrivateFilters(filterObject))
        break
      case 'Showroom':
        dispatch(setShowroomFilters(filterObject))
        break
      case 'ExhibitionDetail':
        dispatch(setExhibitionFilters(filterObject))
        break
    }

    if (screen === 'ExhibitionDetail') {
      navigation.navigate('ExhibitionDetail', {
        hallId: exhibitionHallId,
      })
    } else {
      navigation.navigate('DrawerStack', {
        screen: 'Hub',
        params: {
          screen: screen,
        },
      })
    }
  }

  useEffect(() => {
    if (item) {
      const list = []
      const startIndex = 2
      if (item?.images?.length > 0) {
        item?.images.map((image: NFTImage, index) => {
          list.push({
            ...image,
            name: `Image ${startIndex + index}`,
          })
        })
      }
      
      if (item?.animation_url) {
        list.push({
          id: startIndex + (Number(item?.images?.length) || 0),
          image: item?.animation_url,
          thumb: item?.image_cache,
          extension: 'mp4',
          name: 'Video',
        })
      }
      list.unshift({
        id: 1,
        image: item?.image,
        thumb: item?.image_cache,
        extension: 'jpg',
        name: 'Full View',
      })
      setDownloadContent(list)
    }
  }, [item])

  const openYoutube = (link: string) => {
    Linking.openURL(link)
  }
  const isPublicMuseum = screen === "PublicMuseum";
  //to-to:
  // 3 types of meida display
  // 1. image only -> carousel (read only, cross button)
  // 2. image + animated video --> video player, play video
  // 3. image + animated video + download --> carousel (with download support)

  return (
    <ScrollView
      showsVerticalScrollIndicator={false}
      style={{ backgroundColor: themeColor.hubBackground }}>
      {item.animation_url ? (
        <HubMediaGallery
          item={hubGalleryData}
          carouselData={ isPublicMuseum ? publicMuseumCarouseData.images : itemCarouselData.images}
          canDownload={!canDownloadImage && !canDownloadVideo ? false : canDownload}
          downloadContent={downloadContent}
          canDownloadImage={canDownloadImage}
          canDownloadVideo={canDownloadVideo}
        />
      ) : (
        <ItemCarousel
          data={isPublicMuseum ? publicMuseumCarouseData : itemCarouselData }
          isHub={true}
          canDownload={!canDownloadImage && !canDownloadVideo ? false : canDownload}
          downloadContent={downloadContent}
          canDownloadImage={canDownloadImage}
          canDownloadVideo={canDownloadVideo}
        />
      )}
      <View
        style={[
          commonStyles.globalContainer,
          gapStyle.pb50,
          { backgroundColor: themeColor.hubBackground },
        ]}>
        <>
          <View style={productDetailsStyle.likedContainer}>
            <Pressable onPress={toggleLike}>
              {toggleNFT?.is_like ? (
                <CustomSVG svgIcon={LikedFillIcon} height={iconSize} width={iconSize} />
              ) : (
                <CustomSVG svgIcon={LikedEmptyIcon} height={iconSize} width={iconSize} />
              )}
            </Pressable>
            <Text style={productDetailsStyle.likedText}>{toggleNFT?.like_total}</Text>
          </View>
          <ItemHeader data={itemHeaderData} isHub={true} />
          {item?.attributes?.length > 0 && (
            <>
              <View style={commonStyles.accordionContainer}>
                <Pressable
                  style={commonStyles.accordion}
                  onPress={() => handleUpDownIcon('description')}>
                  <Text style={[commonStyles.accordionText, productDetailsStyle.hubTextColor]}>
                    {trans('Description')}
                  </Text>
                  {productInfoSections.description.isShowing ? (
                    <CustomSVG svgIcon={UpIcon} height={iconSize} width={iconSize} />
                  ) : (
                    <CustomSVG
                      svgIcon={DownIcon}
                      height={iconSize}
                      width={iconSize}
                      fill={'#FFFFFF'}
                    />
                  )}
                </Pressable>
                {!productInfoSections.description.isShowing && (
                  <View style={[commonStyles.hrLine, commonStyles.hubHrLine]} />
                )}
              </View>
              {productInfoSections.description.isShowing && (
                <>
                  <View style={productDetailsStyle.metaDataContainer}>
                    {item?.attributes?.map((data: NFTAttributes, index: number) => (
                      <Pressable
                        style={productDetailsStyle.metaDataBlock}
                        key={index}
                        onPress={() => handleSelectItem(data)}>
                        <Text
                          style={[
                            productDetailsStyle.metaDataLabel,
                            productDetailsStyle.hubTextColor,
                          ]}>
                          {data?.trait_type}
                        </Text>
                        <Text
                          style={[
                            productDetailsStyle.metaDataLabel,
                            productDetailsStyle.metaDataValue,
                          ]}>
                          {data?.value}
                        </Text>
                      </Pressable>
                    ))}
                  </View>
                  {'is_show_description' in item &&
                  item.is_show_description &&
                  item.addi_description ? (
                    <View style={productDetailsStyle.metaDataContainer}>
                      <Text
                        style={[
                          productDetailsStyle.metaDataLabel,
                          productDetailsStyle.hubTextColor,
                          { marginTop: dpr(10) },
                        ]}>
                        {item?.addi_description}
                      </Text>
                    </View>
                  ) : null}
                </>
              )}
            </>
          )}
          {Object.keys(item).length > 0 && (
            <>
              <View style={commonStyles.accordionContainer}>
                <Pressable
                  style={commonStyles.accordion}
                  onPress={() => handleUpDownIcon('details')}>
                  <Text style={[commonStyles.accordionText, productDetailsStyle.hubTextColor]}>
                    {trans('Details')}
                  </Text>
                  {productInfoSections.details.isShowing ? (
                    <CustomSVG svgIcon={UpIcon} height={iconSize} width={iconSize} />
                  ) : (
                    <CustomSVG
                      svgIcon={DownIcon}
                      height={iconSize}
                      width={iconSize}
                      fill={'#FFFFFF'}
                    />
                  )}
                </Pressable>
                {!productInfoSections.details.isShowing && (
                  <View style={[commonStyles.hrLine, commonStyles.hubHrLine]} />
                )}
              </View>
              {productInfoSections.details.isShowing && (
                <>
                  <View style={productDetailsStyle.detailRow}>
                    <Text
                      style={[productDetailsStyle.metaDataLabel, productDetailsStyle.hubTextColor]}>
                      {trans('Owner Address')}
                    </Text>
                    <Text
                      style={[
                        productDetailsStyle.metaDataLabel,
                        productDetailsStyle.metaDataValue,
                        productDetailsStyle.detailOddRow,
                      ]}>
                      {item?.wallet_address.substring(0, 5)}...$
                      {item?.wallet_address.slice(-5)}
                    </Text>
                  </View>

                  <View style={productDetailsStyle.detailRow}>
                    <Text
                      style={[productDetailsStyle.metaDataLabel, productDetailsStyle.hubTextColor]}>
                      {trans('Contract Address')}
                    </Text>
                    <Text
                      style={[
                        productDetailsStyle.metaDataLabel,
                        productDetailsStyle.metaDataValue,
                        productDetailsStyle.detailOddRow,
                      ]}>
                      {item?.contract_address.substring(0, 5)}...$
                      {item?.contract_address.slice(-5)}
                    </Text>
                  </View>

                  <View style={productDetailsStyle.detailRow}>
                    <Text
                      style={[productDetailsStyle.metaDataLabel, productDetailsStyle.hubTextColor]}>
                      {trans('Token Standard')}
                    </Text>
                    <Text
                      style={[
                        productDetailsStyle.metaDataLabel,
                        productDetailsStyle.metaDataValue,
                        productDetailsStyle.detailEvenRow,
                        productDetailsStyle.hubTextColor,
                      ]}>
                      {item?.token_type}
                    </Text>
                  </View>

                  <View style={productDetailsStyle.detailRow}>
                    <Text
                      style={[productDetailsStyle.metaDataLabel, productDetailsStyle.hubTextColor]}>
                      {trans('Token ID')}
                    </Text>
                    <Text
                      style={[
                        productDetailsStyle.metaDataLabel,
                        productDetailsStyle.metaDataValue,
                        productDetailsStyle.detailOddRow,
                        productDetailsStyle.hubTextColor,
                      ]}>
                      {item?.token_id}
                    </Text>
                  </View>

                  <View style={productDetailsStyle.detailRow}>
                    <Text
                      style={[productDetailsStyle.metaDataLabel, productDetailsStyle.hubTextColor]}>
                      {trans('Chain')}
                    </Text>
                    <Text
                      style={[
                        productDetailsStyle.metaDataLabel,
                        productDetailsStyle.metaDataValue,
                        productDetailsStyle.detailEvenRow,
                        productDetailsStyle.hubTextColor,
                      ]}>
                      {item?.chain}
                    </Text>
                  </View>
                </>
              )}
            </>
          )}
          {item?.description.length > 0 && (
            <>
              <View style={commonStyles.accordionContainer}>
                <Pressable
                  style={commonStyles.accordion}
                  onPress={() => handleUpDownIcon('product_info')}>
                  <Text style={[commonStyles.accordionText, productDetailsStyle.hubTextColor]}>
                    {trans('Product Info')}
                  </Text>
                  {productInfoSections.product_info.isShowing ? (
                    <CustomSVG svgIcon={UpIcon} height={iconSize} width={iconSize} />
                  ) : (
                    <CustomSVG
                      svgIcon={DownIcon}
                      height={iconSize}
                      width={iconSize}
                      fill={'#FFFFFF'}
                    />
                  )}
                </Pressable>
                {!productInfoSections.product_info.isShowing && (
                  <View style={[commonStyles.hrLine, commonStyles.hubHrLine]} />
                )}
              </View>
              {productInfoSections.product_info.isShowing && (
                <Text style={[productDetailsStyle.metaDataLabel, productDetailsStyle.hubTextColor]}>
                  {item?.description}
                </Text>
              )}
              {item?.youtube_link?.length > 0 && (
                <>
                  <Pressable
                    style={commonStyles.accordion}
                    onPress={() => openYoutube(item?.youtube_link)}>
                    <Text style={[commonStyles.accordionText, productDetailsStyle.hubTextColor]}>
                      Youtube
                    </Text>
                    <CustomSVG svgIcon={LinkIcon} height={24} width={24} />
                  </Pressable>
                </>
              )}
            </>
          )}
        </>
      </View>
    </ScrollView>
  )
}

export default NFTDetail
