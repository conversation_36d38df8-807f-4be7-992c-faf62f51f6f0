import React, { ReactNode, useEffect } from 'react'
import { View, Text, ScrollView, Image, Dimensions } from 'react-native'
import { NativeStackNavigationProp } from '@react-navigation/native-stack'
import HTMLView from 'react-native-htmlview'

import { useNavigation } from '@react-navigation/native'
import RenderHTML from 'react-native-render-html'
import { dynamicContentStyle } from './dynamicContent.style'
import { RootStackParamList } from '@/types/navigations'
import { useAppDispatch, useAppSelector } from '@/hooks/reduxHooks'
import useAuth from '@/hooks/useAuth'
import { getInformation } from '@/redux/slices/information/information'
import { gapStyle } from '@/screens/Utilities/CommonStyles/gap.style'
import SkeletonElement from '@/src/skeletons/SkeletonElement'
import EmptyContent from '@/screens/Utilities/EmptyContent/EmptyContent'
import NoContentIcon from '@/assets/svgs/empty content/noAddress.svg'
import { commonStyles, htmlStyles } from '@/screens/Utilities/CommonStyles/common.styles'
import BackNavigation from '@/screens/Utilities/CustomHeader/BackNavigation'
import dpr from '@/screens/Utilities/CustomStyleAttribute/dpr'

interface DynamicContentProps {
  skeleton: ReactNode
  infoId: number
}

const DynamicContent = (props: DynamicContentProps) => {
  const { skeleton, infoId } = props
  const { lng } = useAuth()
  const dispatch = useAppDispatch()
  const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>()
  const { information, loading } = useAppSelector((state) => state.informationReducer)
  const width = Dimensions.get('window').width

  useEffect(() => {
    dispatch(getInformation({ lng, id: infoId }))
  }, [lng])

  return (
    <View style={commonStyles.globalScreenContainer}>
      <BackNavigation
        navigationProps={navigation}
        routeName={information?.name || ''}
        capitalize={false}
      />
      <View style={[commonStyles.hrLineFull, gapStyle.mt0]} />
      <View style={commonStyles.globalContainer}>
        <ScrollView showsVerticalScrollIndicator={false}>
          {loading ? (
            skeleton
          ) : Object.keys(information).length === 0 ? (
            <View style={dynamicContentStyle.header}>
              <EmptyContent Icon={NoContentIcon} text={'Welcome to TMR Arts.'} />
            </View>
          ) : (
            <>
              <View style={dynamicContentStyle.header}>
                <RenderHTML
                  contentWidth={width}
                  source={{ html: information?.description?.trim().replace(/\r\n\r\n/g, '') || '' }}
                  tagsStyles={{ ...htmlStyles, p: { margin: dpr(2), padding: 0 } }}
                />
              </View>
              {/* Duplicate content on About Us */}
              {infoId !== 1 && (
                <View>
                  {information?.contents?.map((content, index) => (
                    <View key={index} style={dynamicContentStyle.section}>
                      {content?.image && (
                        <Image style={dynamicContentStyle.image} source={{ uri: content?.image }} />
                      )}
                      <View>
                        <Text style={dynamicContentStyle.sectionTitle}>{content.name}</Text>
                        <RenderHTML
                          contentWidth={width}
                          source={{
                            html: content?.description?.trim().replace(/\r\n\r\n/g, '') || '',
                          }}
                          tagsStyles={{ ...htmlStyles, p: { margin: dpr(2), padding: 0 } }}
                        />
                      </View>
                    </View>
                  ))}
                </View>
              )}
            </>
          )}
        </ScrollView>
      </View>
    </View>
  )
}

export default DynamicContent
