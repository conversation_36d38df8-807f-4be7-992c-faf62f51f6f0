import React, { memo } from 'react'
import { FlatList } from 'react-native'
import CustomBottomSheet from '../CustomBottomSheet/CustomBottomSheet'
import SelectSingleItem from './SelectSingleItem'
import CustomActiveIndicator from '../CustomLoader/CustomActiveIndicator'
import { SortBy } from '@/screens/Filter/SearchFilter1/SearchFilter1'

interface SelectItemBottomSheetProps {
  snapPoint?: number[]
  selectRef: React.RefObject<any>
  data: SortBy[] | any
  name: string
  loading?: boolean
  isHeaderComponent?: boolean
  onPress: (item: any) => void
  bottomInset?: number
  onChange?: (index: number) => void
}

const SelectItemBottomSheet = ({
  snapPoint = [300, 300],
  selectRef,
  data,
  name,
  loading = false,
  isHeaderComponent,
  onPress,
  bottomInset,
  onChange,
}: SelectItemBottomSheetProps) => {
  if (loading) {
    return <CustomActiveIndicator />
  }

  return (
    <CustomBottomSheet
      bsRef={selectRef}
      bottomInset={bottomInset ? bottomInset : 0}
      isScrollable={true}
      isHeaderComponent={isHeaderComponent}
      onChange={onChange}>
      <FlatList
        data={data}
        renderItem={({ item }) => (
          <SelectSingleItem selectRef={selectRef} item={item} onPress={onPress} />
        )}
        keyExtractor={(_, key) => `bs${key}`}
      />
    </CustomBottomSheet>
  )
}

export default memo(SelectItemBottomSheet)
