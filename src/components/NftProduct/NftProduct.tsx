import React, { memo } from 'react'
import { View, Text, Pressable, Platform } from 'react-native'
import * as Linking from 'expo-linking'
import { NativeStackNavigationProp } from '@react-navigation/native-stack'
import { useNavigation } from '@react-navigation/native'
import { nftProductStyle } from './nftProduct.style'
import ProgressiveImage from '../ProgressiveImage'
import CustomSVG from '../CustomSVG/CustomSVG'
import { ProductsStyle } from '@/screens/Home/FeaturedProducts/featureProducts.style'
import { PRODUCT_DETAILS } from '@/components/Navigation/RouteNames'
import useLangTranslation from '@/hooks/useLangTranslation'
import { ProductState } from '@/redux/slices/featureProducts/featureProducts'
import { RootStackParamList } from '@/types/navigations'
import NFTBadgeIcon from '@/assets/svgs/nft_badge.svg'
import dpr from '@/screens/Utilities/CustomStyleAttribute/dpr'

interface NftProductProps {
  item: ProductState
}

const NftProduct = (props: NftProductProps) => {
  const { item } = props || {}
  const { product_id, thumb, name, opensea_link, is_nft } = item || {}

  const { trans } = useLangTranslation()
  const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>()

  const productsStyle = ProductsStyle()
  return (
    <View style={productsStyle.item}>
      <Pressable
        onPress={() => {
          navigation?.push(PRODUCT_DETAILS, { productId: product_id })
        }}>
        <View style={nftProductStyle.imageContainer}>
          <View style={{ position: 'relative' }}>
            {/* {is_nft && (
              <CustomSVG
                svgIcon={NFTBadgeIcon}
                width={dpr(24)}
                height={dpr(24)}
                style={{ position: 'absolute', top: 0, left: 4, zIndex: 1 }}
              />
            )} */}
            <ProgressiveImage source={{ uri: thumb }} style={nftProductStyle.img} />
          </View>
        </View>
      </Pressable>
      <Text style={productsStyle.text} numberOfLines={1}>
        {name}
      </Text>
      {Platform.OS !== 'ios' && (
        <Pressable onPress={() => Linking.openURL(opensea_link)} style={nftProductStyle.viewButton}>
          <Text style={nftProductStyle.viewButtonText}>{trans('View On Opensea')}</Text>
        </Pressable>
      )}
    </View>
  )
}

export default memo(NftProduct)
