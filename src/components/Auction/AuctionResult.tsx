import React from 'react'
import { View } from 'react-native-animatable'
import { Text, TextStyle } from 'react-native'
import { NativeStackNavigationProp } from '@react-navigation/native-stack'
import HTMLView from 'react-native-htmlview'
import { useNavigation } from '@react-navigation/native'
import CustomButton from '../CustomButton/CustomButton'
import { orderDetailsStyle } from '@/screens/Profile/OrderHistory/OrderDetails/orderDetails.style'
import { AUCTION_BIT_STATUS_LABEL } from '@/screens/Auction/auctionStatus'
import { orderConfirmedStyle } from '@/screens/ShoppingCart/OrderConfirmed/orderConfirmed.style'
import { gapStyle } from '@/screens/Utilities/CommonStyles/gap.style'
import {
  commonStyles,
  dynamicStatusText,
  htmlStyles,
} from '@/screens/Utilities/CommonStyles/common.styles'
import useLangTranslation from '@/hooks/useLangTranslation'
import CustomSVG from '@/src/components/CustomSVG/CustomSVG'
import SuccessTickIcon from '@/assets/svgs/successTick.svg'
import { RootStackParamList } from '@/types/navigations'
import FailIcon from '@/assets/svgs/fail.svg'
import { themeColor } from '@/theme/theme'

interface Props {
  type: 'success' | 'fail' | 'confirmed'
  title: string
  subtitle: string
  data: {
    name: string
    price: string
    date: string
    status: number
    bidId: string
    orderId?: string
    remark?: string
    payment_info?: {
      information?: string
      remark?: string
      title?: string
    }
  }
  lotId?: number
}

const Header: React.FC<{
  type: 'success' | 'fail' | 'confirmed'
  title: string
  subtitle: string
}> = ({ type, title, subtitle }) => {
  const icons = {
    success: SuccessTickIcon,
    fail: FailIcon,
    confirmed: SuccessTickIcon,
  }

  return (
    <View style={orderConfirmedStyle.topPosition}>
      <View style={orderConfirmedStyle.iconAlign}>
        <Text style={orderConfirmedStyle.topIcon}>
          <CustomSVG svgIcon={icons[type]} />
        </Text>
      </View>
      <Text style={orderConfirmedStyle.topText1}>{title}</Text>
      <Text style={orderConfirmedStyle.topText2}>{subtitle}</Text>
    </View>
  )
}

const DetailRow: React.FC<{
  label: string
  value: string
  withBorder?: boolean
  textStyle?: TextStyle
}> = ({ label, value, withBorder, textStyle }) => (
  <View
    style={[orderDetailsStyle.deliverySubCont, withBorder ? orderDetailsStyle.borderStyle : {}]}>
    <Text style={orderDetailsStyle.deliveryText1}>{label}</Text>
    <Text style={[orderDetailsStyle.deliveryText2, textStyle]}>{value}</Text>
  </View>
)

const AuctionResult = (props: Props) => {
  const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>()
  const { trans } = useLangTranslation()
  const { type, title, subtitle, data, lotId } = props

  return (
    <View style={[orderDetailsStyle.bodyContainer, gapStyle.pb0]}>
      <View style={[commonStyles.paddingSection, gapStyle.mb20]}>
        <Header type={type} title={title} subtitle={subtitle} />
        {data?.orderId && (
          <View style={[orderDetailsStyle.deliveryCont, gapStyle.mb15]}>
            <DetailRow label={trans('Order No')} value={data?.orderId} />
            <DetailRow label="" value="" withBorder />
          </View>
        )}
        <View style={[orderDetailsStyle.deliveryCont, gapStyle.mb15]}>
          <DetailRow label={trans('Product Name')} value={data?.name} />
          <DetailRow label={trans('Grand Total')} value={data?.price} withBorder />
        </View>
        <View style={[orderDetailsStyle.deliveryCont, gapStyle.mb15]}>
          <DetailRow label={trans('Order Date')} value={data?.date} />
          <DetailRow
            label={trans('Payment Status')}
            textStyle={{ color: dynamicStatusText(AUCTION_BIT_STATUS_LABEL[Number(data?.status)]) }}
            value={trans(AUCTION_BIT_STATUS_LABEL[Number(data.status)])}
            withBorder
          />
        </View>

        {data?.remark && (
          <View style={[orderDetailsStyle.shippingAddressCont, gapStyle.mt15]}>
            <View style={orderDetailsStyle.shippingTextCont}>
              <Text style={orderDetailsStyle.deliveryText1}>{data?.remark?.trim()}</Text>
            </View>
          </View>
        )}

        {data?.payment_info?.remark && (
          <View style={[orderDetailsStyle.shippingAddressCont, gapStyle.mt15]}>
            <View style={{ borderBottomWidth: 1, borderColor: themeColor.lightBorder }}>
              <Text style={orderDetailsStyle.shippingTitle}>
                {trans(data?.payment_info?.title || '')}
              </Text>
            </View>
            <View style={orderDetailsStyle.shippingTextCont}>
              <HTMLView
                stylesheet={htmlStyles}
                value={data?.payment_info?.information?.trim() || ''}
              />
              <HTMLView stylesheet={htmlStyles} value={data?.payment_info?.remark?.trim() || ''} />
            </View>
          </View>
        )}
      </View>

      <View style={[orderDetailsStyle.bottomSectionRow, { marginTop: 'auto' }]}>
        <CustomButton
          onPress={() =>
            lotId
              ? navigation.navigate('AuctionDetail', {
                  id: Number(lotId),
                })
              : navigation.navigate('DrawerStack', {
                  screen: 'HomeScreen',
                  params: { screen: 'Home' },
                })
          }
          style={[orderConfirmedStyle.button, orderConfirmedStyle.homeBtn, gapStyle.mt0]}
          text={lotId ? trans('Back') : trans('Home')}
          textColor={themeColor.primaryText}
          loading={false}
          customIcon={require('@/assets/lottie/loadingBlack.json')}
        />
        <CustomButton
          onPress={() =>
            navigation.navigate('MyBidDetail', {
              bidId: Number(data?.bidId),
            })
          }
          style={[orderConfirmedStyle.button, orderConfirmedStyle.orderDetailBtn, gapStyle.mt0]}
          text={trans('My Bid')}
          loading={false}
        />
      </View>
    </View>
  )
}

export default AuctionResult
