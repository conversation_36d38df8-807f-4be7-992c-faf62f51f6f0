import React from 'react'
import { Image, Pressable, Text } from 'react-native'
import { useNavigation } from '@react-navigation/native'
import { NativeStackNavigationProp } from '@react-navigation/native-stack'
import lotListItemStyle from './lotListItem.style'
import CountdownTimer from '../CountdownTimer/CountdownTimer'
import { singleProductStyle } from '../SingleProduct/singleProduct.style'
import { LotList } from '@/types/auction'
import useLangTranslation from '@/hooks/useLangTranslation'
import productStyle from '@/screens/Utilities/CommonStyles/product.style'
import { RootStackParamList } from '@/types/navigations'
import { ProductsStyle } from '@/screens/Home/FeaturedProducts/featureProducts.style'

const LotListItem = ({ item, isRelatedSection }: { item: LotList; isRelatedSection?: boolean }) => {
  const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>()
  const { trans } = useLangTranslation()
  const productsStyle = ProductsStyle()

  return (
    <Pressable
      style={isRelatedSection ? productsStyle.item : lotListItemStyle.container}
      onPress={() => navigation?.push('AuctionDetail', { id: item?.auction_id })}>
      <Image
        source={{ uri: item?.thumb }}
        style={isRelatedSection ? singleProductStyle.imageContainer : lotListItemStyle.img}
      />
      {item?.is_bid_end ? (
        <Text style={lotListItemStyle.countDown}>{item?.text_expired}</Text>
      ) : (
        <CountdownTimer
          targetDate={item?.bid_end_time}
          timerStyle={lotListItemStyle.countDown}
          inlineLabel={true}
        />
      )}
      <Text style={productStyle.text} numberOfLines={1}>
        {item?.name}
      </Text>
      <Text style={productStyle.productNo}>
        {trans('product no')}
        {': '}
        {item?.model}
      </Text>
      <Text style={[productStyle.productNo, lotListItemStyle.currentBid]}>
        {trans('Current Bid')}
        {':'}
      </Text>
      <Text style={lotListItemStyle.priceTag}>{item?.current_bid}</Text>
    </Pressable>
  )
}

export default LotListItem
