export const config = {
  screens: {
    DrawerStack: {
      screens: {
        HomeScreen: {
          screens: {
            Cart: {
              screens: {
                OrderDone: {
                  path: 'order-done/:orderId',
                  parse: {
                    orderId: (orderId: string) => `${orderId}`,
                  },
                },
                OrderFailed: {
                  path: 'order-fail/:orderId',
                  parse: {
                    orderId: (orderId: string) => `${orderId}`,
                  },
                },
              },
            },
            ProductListing: {
              path: 'product-listing',
            },
          },
        },
      },
    },
    ProductDetails: {
      path: 'product-detail/:productId',
      parse: {
        productId: (productId: string) => `${productId}`,
      },
    },
    OrderHistory: {
      path: 'order-history',
    },
    OrderDetails: {
      path: 'order-detail/:itemId',
      parse: {
        itemId: (itemId: string) => `${itemId}`,
      },
    },
    AuctionDetail: {
      path: 'auction-detail/:id',
      parse: {
        id: (id: string) => `${id}`,
      },
    },
    AuctionPayNow: {
      path: 'auction-pay-now/:bidId',
      parse: {
        bidId: (bidId: string) => `${bidId}`,
      },
    },
    AuctionSuccess: {
      path: 'auction-order-done/:bidId',
      parse: {
        bidId: (bidId: string) => `${bidId}`,
      },
    },
    AuctionFail: {
      path: 'auction-order-fail/:bidId',
      parse: {
        bid: (bidId: string) => `${bidId}`,
      },
    },
    MyBidDetail: {
      path: 'my-bid-detail/:bidId',
      parse: {
        bidId: (bidId: string) => `${bidId}`,
      },
    },
    MyLotDetail: {
      path: 'my-lot-detail/:auctionId',
      parse: {
        auctionId: (auctionId: string) => `${auctionId}`,
      },
    },
  },
}
