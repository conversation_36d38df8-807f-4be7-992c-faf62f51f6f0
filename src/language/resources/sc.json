{"Loading": "加载中", "Your account is successfully created!": "您的帐户已成功创建！", "Resend your code. Please check your email!": "重新发送您的代码。请查看你的邮箱！", "Check Your Mail": "请查收你的邮件", "A 6 digit code has been sent to {{x}}.....@{{y}}.Use the code here.": "一个 6 位代码已发送至 {{x}}.....@{{y}}。使用此处的代码。", "Invalid OTP": "无效一次性密码", "Resend Code": "重新发送验证码", "Did not receive any code? Check your spam folder.": "没有收到任何代码？检查您的垃圾邮件文件夹。", "or resend code": "或重新发送代码", "Forget Password": "忘记密码", "Don't worry. Please enter the email address associated with your account.": "不用担心。请输入您帐户的电邮地址。", "Email Address": "电邮地址", "e.g, <EMAIL>": "例如，<EMAIL>", "Submit": "提交", "Sign In": "登入", "Password": "密码", "Forgot Password": "忘记密码", "Login": "登录", "or use other accounts": "或使用其他帐户", "Sign in with Google": "使用谷歌登录", "Sign in with Facebook": "使用脸书登入", "Don't have an account": "没有帐号", "Register Now": "现在注册", "Email is required": "必需填写电邮", "Enter a valid email address": "输入一个有效的电邮地址", "Password is required": "必需填写密码", "Password Successfully Changed": "密码修改成功", "Use your new password to login now.": "立即使用您的新密码登录。", "Something wrong!": "有事吗！", "Note: ": "笔记：", "Password must be contain {{x}} characters long": "密码必须包含 {{x}} 个字符长", "Let's Sign Up": "让我们注册", "Your Name": "你的名字", "e.g, Johnson Dammusa": "例如，约翰逊·达穆萨", "Create Account": "创建账户", "Already have an account": "已有帐号", "Login Now": "现在登录", "Your name is required": "必需填写名字", "Name is too large": "名称太大", "Name should be at least 3 characters.": "名称应至少包含 3 个字符。", "Password must contain{{x}} {{y}} and {{z}} characters long; Password must contain uppercase, lowercase, symbols and 6 characters long.": "密码必须包含 {{x}} {{y}} 和 {{z}} 字符长；密码必须包含大写、小写、符号和 6 个字符长度。", "Reset Password": "重设密码", "Set new passwords.": "设置新密码。", "New Password": "新密码", "Confirm Password": "确认密码", "Cancel": "取消", "I'm not a robot": "我不是机器人", "Please verify that you are not a robot.": "请确认您不是机器人。", "No Account": "没有账号", "Create or login now": "立即创建或登录", "Home": "首页", "Track Order": "跟踪订单", "My Wishlist": "我的收藏", "Refund": "退款", "Settings": "设定 ", "Logout": "登出", "Logout Successful.": "注销成功。", "Please wait": "请稍等", "Categories": "类别", "Cart": "购物车", "Profile": "会员中心", "Image size must be less then {{x}} mb": "图片大小必须小于 {{x}} mb", "File size is too large": "文件太大", "No Category Found": "未找到类别", "Sub Categories": "子类别", "Filter": "筛选", "Sort By: {{x}}": "排序依据：{{x}}", "Best Sellers": "最畅销", "Best Sellers List is Empty": "畅销书列表为空", "Search": "搜索", "Price Low to High": "价格从低到高", "Price High to Low": "价格从高到低", "Avg. Ratting": "平均。评级", "Most Popular": "最受欢迎", "Popular Products List is Empty": "热门产品列表为空", "Recent Search": "最近搜索", "You haven't search anything yet.": "您还没有搜索任何内容。", "Price Range": "价格范围", "Min.": "最少限度", "Max.": "最大限度", "Update Price": "更新价格", "Select {{x}}": "选择{{x}}", "Filters": "筛选", "Ratings": "收视率", "Not Selected": "未选中的", "Reset": "重设", "Done": "完毕", "items found for": "找到的项目", "Top Categories": "热门类别", "Trending Items": "热门商品", "New Arrivals": "新品上架", "Best Seller": "畅销书", "Flash Sales": "闪购", "No Product Found": "没有找到产品", "Featured Products": "特色产品", "Delivery Options": "交付选项", "Cash on Delivery": "货到付款", "Available": "可用的", "Warranty": "保修单", "Select Address": "选择地址", "Add to Cart": "添加到购物车", "Description": "描述", "Specification": "规格", "Videos": "影片", "See More": "看更多", "See Less": "少看", "This is a grouped product. Select the ones you want.": "这是一个组合产品。选择你想要的。", "Option": "选项", "Out Of Stock": "缺货", "In Stock": "有存货", "{{x}} Items Remaining": "{{x}} 项剩余", "Only {{x}} left in stock.": "库存仅剩 {{x}}。", "Visit Cart": "查看购物车", "Variations": "变化", "Sold By": "所售", "{{x}} Review": "{{x}} 评论", "{{y}} Reviews": "{{y}} 评论", "Related Items": "相关项目", "Rating & Reviews: {{x}} ({{y}})": "评分", "Verified Purchase": "验证购买", "See All": "看到所有", "No Reviews Yet": "还没有评论", "Be the first to review this item": "成为第一个复查该产品", "Write a Review": "写评论", "Customer Gallery": "客户图库", "reviews": "评论", "Average of {{x}} reviews": "{{x}} 条评论的平均值", "Customers Gallery": "客户图库", "Feedbacks": "反馈", "Rating field is required": "评分字段是必需的", "Review submit unsuccessful. Please try again!": "审核提交不成功。请再试一次！", "Add Your Ratings": "添加您的评分", "{{x}} Stars": "{{x}} 星", "Add Photos": "添加照片", "Click here to upload photos": "点击这里上传照片", "Write Your Experience": "写下你的经历", "What would you like to write about your experience with this product?": "关于您使用该产品的体验，您想写些什么？", "{{x}} characters remaining": "剩余 {{x}} 个字符", "Submit Review": "提交评论", "Item is removed from wishlist.": "项目已从愿望清单中删除。", "my wishlist": "我的收藏", "Clear All": "全部清除", "No Items Saved For Later": "没有为以后保存的项目", "No Internet Connection": "没有网络连接", "You have gone offline. Make sure Wi-Fi or cellular data is on and try again.": "您已下线。确保 Wi-Fi 或蜂窝数据已开启，然后重试。", "Try Again": "再试一次", "Payment": "支付", "Phone number max 45 char long": "电话号码最长 45 个字符", "The street address may not be greater than 191 characters.": "街道地址不得超过 191 个字符。", "Postcode only Number": "仅限邮政编码", "Something Went Wrong Please Try Again!": "出了点问题请重试！", "Address {{x}} has been deleted": "地址 {{x}} 已被删除", "Address {{x}}": "地址 {{x}}", "Add New Address": "添加新地址", "First Name": "名", "Last Name": "姓", "Company Name": "公司名", "Optional": "可选的", "Phone Number": "电话号码", "Street Address": "街道地址", "Country*": "国家*", "Select Country": "选择国家", "State": "状态", "Province*": "省*", "Select State": "选择州", "City*": "城市*", "Select City": "选择城市", "Postcode": "邮政编码", "ZIP": "压缩", "Type of Place": "地方类型", "Office": "办公室", "Use as default Address in the future": "将来用作默认地址", "Delete this Address": "删除此地址", "Please fill in all the required fields": "请填写所有必填字段", "Save Address": "保存地址", "Address Book": "地址簿", "No Address Has Been Fixed": "没有固定地址", "Used as default address": "用作默认地址", "Make as default address": "设为默认地址", "edit profile": "编辑个人资料", "Profile Display": "资料显示", "Change Photo": "更改照片", "Personal Information": "个人信息", "Gender": "性别", "Male": "男性", "Female": "女性", "Date of Birth": "出生日期", "Day": "天", "Month": "月", "Year": "年", "Address": "地址", "Please fill in all the required fields ": "请填写所有必填字段", "Save Changes": "保存更改", "Phone number max 15 char long": "电话号码最长 15 个字符", "File does not remove. Please try again!": "文件不删除。请再试一次！", "Reviews does not delete": "点评不删", "Edit Review": "编辑评论", "Reviewed on": "审查于", "Your Ratings": "您的评分", "Stars": "星星", "Your Photos": "你的照片", "Your Experience": "您的体验", "Update Review": "更新回顾", "my refund": "我的退款", "You Have No Refund Requests Yet": "您还没有退款申请", "Last Refund": "最后退款", "Qty": "数量", "See Refund Lists": "查看退款清单", "Request A Refund": "申请退款", "refund details": "退款详情", "REFERENCE": "参考", "Status": "状态", "Product Details": "产品详情", "Refund Reason": "退款原因", "Uploaded Pictures": "上传图片", "Your Messages": "您的讯息", "Write A Message": "写一个信息", "Enter your message here": "在这里输入您的信息", "Send Message": "发信息", "All Time": "整天", "Today": "今天", "Last 7 days": "最近 7 天", "Last 30 Days": "最近30天", "Last 12 Month": "最近 12 个月", "All Status": "所有状态", "Opened": "开业", "In Progress": "进行中", "Accepted": "公认", "Declined": "拒绝", "refund list": "退款清单", "Filter By": "筛选方式", "refund": "退款", "There Have No Products For Refund": "没有可以退款的产品", "Select Order Number": "选择订单号", "Select Option": "选择选项", "Select Product": "选择产品", "Select Quantity": "选择数量", "Select Your Reason": "选择您的理由", "Select Reason": "选择原因", "Images of the Product": "产品图片", "You must upload minimum 2 images. One of the product and another of the receipt papers.": "您必须上传至少 2 张图片。一张产品和另一张收据纸。", "Must be upload 2 or more images": "必须上传 2 张或更多图片", "Send Request": "发送请求", "Review you wrote": "你写的评论", "my reviews": "我的评论", "No Reviews Given": "没有评论", "order details": "订单详细信息", "ID Number": "身份证号", "Shipping Method": "送货方式", "Payment Status": "支付状态", "Order Date": "订购日期", "Estimated Delivery": "预计交货", "{{x}} days (all items)": "{{x}} 天（所有项目）", "Shipping Address": "收件地址", "Street": "街道", "City": "城市", "Country": "国家", "Ordered Products": "订购产品", "Subtotal": "小计", "Shipping": "船运", "Tax": "税", "Discount": "折扣", "Grand Total": "订单总额", "Proceed to payment": "继续付款", "No Order Available": "没有可用的订单", "order history": "订单历史", "My Account": "我的账户", "Edit profile": "编辑个人资料", "In Cart": "在购物车", "Wishlist": "心愿单", "All Orders": "所有订单", "My Address": "我的地址", "Order History": "订单历史", "My Reviews": "我的评论", "Refund Requests": "退款申请", "Register": "登记", "select language": "选择语言", "settings": "设置", "Language": "语言", "Checkout": "前往结帐", "Select Your Address": "选择您的地址", "Thank you": "谢谢", "Your order has been received.": "您的订单已收到。", "Invoice Number": "发票号码", "View Details": "查看详情", "Back to Home": "回到首页", "This Field Is Required": "此字段是必需的", "Order process failed. Please try again.": "订单处理失败。请再试一次。", "order summary": "订单摘要", "All Products ({{x}})": "所有产品 ({{x}})", "Select Shipping Method": "选择送货方式", "Coupon offer": "优惠券优惠", "Have a coupon? Apply Now.": "有优惠券吗？现在申请。", "Type coupon code": "输入优惠券代码", "Apply": "使用", "Proceed to Payment": "继续付款", "Estimated Time: {{x}} days": "预计时间：{{x}} 天", "Something went wrong": "出问题了", "my cart": "我的车", "No Items Saved For Order": "没有为订单保存的项目", "Select All ({{x}} Items)": "选择全部（{{x}} 项）", "Delete Selected Items": "删除所选项目", "Total Price": "总价", "Proceed to Checkout": "进行结算", "Not Available": "无法使用", "track order": "跟踪订单", "Order Reference": "订单参考", "For more details, please": "欲了解更多详情，请", "login to your account.": "登录到您的帐户。", "Total": "总价", "Item": "物品", "Track your order": "追踪您的订单", "Have An Order": "有订单", "Enter the track code of your order below and know the progress of your order delivery": "在下方输入您订单的track code，即可了解您的订单派送进度", "e.g. VKADYFBOV7": "例如 VKADYFBOV7", "Track Now": "立即追踪", "Press to Send Notification": "按发送通知", "Vendor": "小贩", "All Products": "所有产品", "Vendor Profile": "供应商简介", "Vendor Reviews": "供应商评论", "Get Started": "开始", "Back online": "重新上线", "No connection": "无连接", "Search {{x}}": "搜索 {{x}}", "Featured": "精选", "Top Rated": "最高评价", "Stock Out": "脱销", "Undo": "撤消", "Top Seller": "最佳销售员", "Review": "审查", "Reviews": "点评", "Chat Now Feature Coming Soon...": "立即聊天功能即将推出...", "Have any query": "有任何疑问", "Send us a message": "给我们发信息", "Chat Now": "现在聊天", "Positive Seller Ratings": "积极的卖家评级", "Shipment on Time": "准时发货", "Seller’s Cancellation": "卖家取消", "Seller Reviews": "卖家评论", "Average Rating": "平均评分", "reviewCount": "评论次数", "Product Reviews": "产品评论", "Thanks for the review. It will be published soon.": "感谢您的评论。它将很快发布。", "Wishlist id is invalid.": "愿望清单 ID 无效。", "The :x has been successfully saved": ":x 已成功保存", "Something went wrong, please try again.": "出问题了，请重试。", "Address does not found.": "找不到地址。", "Products added to cart successfully.": "产品已成功添加到购物车。", "This product is facing license validation issue. Please contact admin to fix the issue.": "此产品面临许可证验证问题。请联系管理员解决问题。", "Review not found.": "未找到评论。", "File remove successfully.": "文件删除成功。", "Invalid Request": "非法请求", "Reviews does not exist.": "评论不存在。", "Customer Info": "顾客信息", "No changes found.": "未发现任何变化。", "Old password is wrong.": "旧密码错误。", "The :x has been successfully saved.": ":x 已成功保存。", "admin account can't be deleted.": "管理员帐户无法删除。", "Password does not match": "密码不匹配", "Your :x has been successfully deleted.": "您的 :x 已成功删除。", "Account": "帐户", "Order not found.": "找不到订单。", "Your wallet is empty.": "你的钱包是空的。", "Product added to your wishlist.": "产品已添加到您的愿望清单。", "Wishlist id is required.": "愿望清单 ID 是必需的。", "The :x has been successfully deleted.": ":x 已成功删除。", ":x does not exist.": ":x 不存在。", "Product": "产品", "Related Products": "相关产品", "Cross Sale": "交叉销售", "Up Sale": "向上销售", "Attribute": "属性", "Attribute Group": "属性组", "Registration successful. Please verify your email.": "注册成功。请验证您的电邮。", "Invalid email or password": "无效的电邮或密码", "Please verify your email address.": "请验证您的电邮地址。", "Sorry, your account is not activated.": "抱歉，您的帐户未激活。", "Invalid Credentials": "无效证件", "Password reset link sent to your email address.": "密码重置链接已发送到您的电邮地址。", "OTP verification successful.": "OTP 验证成功。", "Nothing is updated.": "没有更新。", "Ok": "好的", "Logout successfully": "注销成功", "The OTP is required.": "必需填写OTP", "Your OTP is invalid.": "OTP 无效。", "Invalid User": "无效的用户", "Account activation successful. Please login": "账户激活成功。请登录", "Brand": "牌", "Category": "类别", "Preference": "偏爱", "Need writable permission of language directory": "需要语言目录的可写权限", "The data you are trying to access is not found.": "找不到您尝试访问的数据。", "Currency": "货币", "Email Configuration": "电邮配置", "Email Template": "电邮模板", "Product has been trashed.": "产品已被丢弃。", "Something went wrong. Product not found.": "出问题了。未找到产品。", "Product deleted permanently.": "产品被永久删除。", "Role": "角色", "Password update successfully.": "密码更新成功。", "Coupon": "优惠券", "City not found.": "未找到城市。", "Country not found.": "未找到国家。", "State not found.": "未找到状态。", "You exceeded the maximum quantity.": "您超出了最大数量。", "Refund request send successfully.": "退款请求发送成功。", "Refund not found.": "未找到退款。", "Shop": "店铺", "This is your only shop, can not be deleted.": "这是你唯一的店铺，不能删除。", "Tax rate not found.": "未找到税率。", "Tax Rate": "税率", "Product successfully added to your cart.": "产品已成功添加到您的购物车。", "Paid": "已付", "Unpaid": "未付", "Pending": "待处理", "Processing": "加工", "Completed": "已完成", "Cancelled": "取消", "Refunded": "已退款", "Failed": "失败的", "On hold": "等候接听", "Order": "命令", "Complete": "已完成", "No Warranty": "没有保修", "days": "天", "months": "几个月", "years": "年", "Lifetime": "寿命", "Days": "天", "Months": "月数", "Years": "年", "The selected email is invalid.": "所选电邮无效。", "Unprocessable Content": "不可处理的内容", "The name field is required.": "名称字段是必需的。", "The email field is required.": "电邮字段是必需的。", "The password field is required.": "密码字段是必需的。", "The email has already been taken.": "电邮已被占用。", "The password confirmation does not match.": "密码确认不匹配。", "The email must be a valid email address.": "电邮必须是有效的电邮地址。", "Password must be at least 4 characters.": "密码必须至少为 4 个字符。", "Logout successful": "注销成功", "Page Not Found.": "网页未找到。", "Service Unavailable": "暂停服务", "The Customer Info has been successfully saved.": "客户信息已成功保存。", "The gender field is required.": "性别字段是必需的。", "Pending Payment": "待付款", "Flat Rate": "扁平率", "Approve": "授权", "Accidental order": "误下的订单", "Arrived too late": "来晚了", "Missing parts or accessories": "缺少零件或配件", "Damaged during shipping": "运输过程中损坏", "Wrong item sent": "寄错物品", "Product is the wrong color": "产品颜色错误", "Better price available": "更优惠的价格", "Different from what was ordered": "与订购的不同", "Product description was inaccurate": "产品描述不准确", "Not satisfied with the quality": "对质量不满意", "Did not like the color": "不喜欢这个颜色", "Other reason": "其他原因", "Unauthorized": "未经授权", "Unapproved": "未经批准", "Unapprove": "不批准", "Free Shipping": "免费送货", "Captcha error! try again later or contact site admin.": "验证码错误！稍后再试或联系网站管理员。", "Attributes": "属性", "Details": "细节", "Password must contain {{x}} and {{z}} characters long; Password must contain uppercase, lowercase, symbols and 6 characters long.": "密码必须包含 {{x}} 和 {{z}} 个字符长；密码必须包含大写、小写、符号和 6 个字符长。", "The Address has been successfully saved.": "地址已成功保存。", "Shopping Cart": "购物车", "Submit Order": "提交订单", "Success": "成功", "Arts": "艺术品", "Wallet ID": "钱包地址", "Name": "名称", "Announcement": "公告", "Event": "活动", "Discover": "发现", "About": "关于我们", "Guide": "指南", "Hub": "<PERSON><PERSON>", "What's New": "最新消息", "View More": "查看更多", "NFT": "NFT", "Most View Products": "最多浏览产品", "Sort By": "排序方式", "Most View": "最受欢迎", "NFT Artwork": "NFT 艺术品", "Alphabetically, A-Z": "按字母顺序，A-Z", "Alphabetically, Z-A": "按字母顺序，Z-A", "Price (High to Low)": "价格（高到低）", "Price (Low to High)": "价格（低到高）", "product no": "产品编号", "Show": "显示", "Detail": "详细资讯", "No Data Found": "未找到数据", "Contract Address": "合约地址", "Token Standard": "代币标准", "Token ID": "代币 ID", "Chain": "区块链", "Product Info": "产品资讯", "N/A": "N/A", "Contact Us": "联系我们", "View On Opensea": "在 Opensea 上查看", "Sold items must be removed from the cart before proceeding to checkout.": "结帐前必须从购物车中移除已售出的商品。", "Apply Coupon!": "使用优惠券！", "Create or select an address.": "选择地址。", "Enter a valid wallet address format.": "请输入有效的钱包地址格式。", "You must agree to the terms and conditions.": "您必须同意条款和条件。", "Failed to save shipping address.": "保存送货地址失败。", "Please select one shipping / payment method.": "请选择一种送货/付款方式。", "Shipping is not requires for this order.": "此订单不需要运送。", "Select Payment Method": "选择付款方式", "Order Number": "订单号码", "Payment Method": "付款方式", "Bank Transfer": "银行转账", "Detail View": "详细资讯", "Send Code": "发送验证码", "Resend": "重新发送", "Email Verification Code": "电邮验证码", "Telephone": "电话", "Wallet Address": "钱包地址", "I have read and agree to the ": "我已阅读并同意", "Privacy Policy": "隐私政策", "Hello": "你好", "Edit Account": "编辑帐户", "Save": "保存", "Change Password": "更改密码", "Current Password": "当前密码", "Confirm New Password": "确认新密码", "Address Book Entries": "地址簿条目", "No Address Has Been Found": "未找到地址", "New Address": "新地址", "Enter a valid phone format.": "请输入有效的电话格式。", "The street address must be between 3 and 128 characters.": "街道地址必须在3到128个字符之间。", "Address has been edited successfully.": "成功编辑地址。", "Address has been added successfully.": "成功添加地址。", "Address has been deleted.": "已删除地址。", "Add Address": "新增地址", "Location": "地区", "Please Select": "请选择", "Post Code": "邮递区号", "Defualt Address": "预设地址", "Canceled": "已取消", "Expired": "已过期", "Minted": "已铸造", "Pay": "付款", "No content available": "没有相关内容", "Discover detail": "详情", "Related Discover": "相关发现", "Enquiry Form": "查询表格", "Company": "公司名称", "Enquiry": "查询内容", "Yes": "是", "No": "否", "Edit Address": "编辑地址", "Use Coupon Code": "使用优惠券", "Enter your coupon code": "输入您的优惠券代码", "Coupon Code": "优惠券代码", "Coupon is either invalid, expired or reached its usage limit!": "优惠券无效、过期或已达到使用限制！", "Please provide your wallet address. Once the order is confirmed, we will send you the product's NFT.": "请提供您的钱包地址。 一旦订单确认完成，我们将向您发送产品的 NFT。", "First name is required": "必需填写名字", "First name is too large": "名字过长", "First name should be at least 1 characters": "名字至少应为 1 个字符", "Last name is required": "必需填写姓氏", "Last name is too large": "姓氏过长", "Last name should be at least 1 characters": "姓氏至少应为 1 个字符", "Phone is required": "必需填写电话号码", "Email verification is required": "必须填写电邮验证码", "Enter a valid phone format": "请输入有效的电话格式", "Password should be at least 8 characters": "密码至少应为 8 个字符", "Confirm password is required": "必需填写确认密码", "Fps Transfer": "Fps 转账", "PayMe Transfer": "PayMe 转账", "PayPayl": "PayPayl", "Pending Confirm": "待确认", "Sorry": "抱歉", "Your order is not completed. Please retry again.": "您的订单未完成。 请再试一次。", "FAQs": "常见问题", "Terms & Conditions": "条款及细则", "Payment & Delivery": "付款及送货", "Coupon applied successfully.": "成功使用优惠券。", "Updated payment method.": "已更新付款方式。", "Auction": "竞拍", "Auction Result": "竞拍结果", "Lot": "拍品", "Current Bid": "当前竞价", "My Auction": "我的竞拍", "Buy Now Price": "一口价", "Auction will be end": "剩余时间", "Ended": "已结束", "Auction Ends": "拍卖结束时间", "Starting Price": "起拍价", "Bid Increment": "竞价递增", "Bid": "竞价", "Bid History": "竞拍记录", "By": "由", "At": "于", "Auction History": "竞拍记录", "Price": "价格", "From": "从", "To": "至", "Please bid on items": "请对物品进行竞拍", "Buy Now": "一口价", "Please select one payment method.": "请选择一种付款方式。", "Product Name": "产品名称", "Order Status": "订单状态", "Pay Now": "立即支付", "Join Auction": "参与竞拍", "Order No": "订单号码", "Accepted, Payment Pending": "已接受，待付款", "Reject": "拒绝", "Here are the details": "详细信息", "My Bid": "我的竞拍", "My Lot": "我的拍品", "All": "全部", "Accept": "接受", "In Auction": "竞拍中", "Sold": "已售出", "No Auction Found.": "未有竞拍。", "Bid Time": "竞价时间", "Back": "返回", "Last Bid": "最后竞价", "Reserve Price": "底价", "Bid end time": "竞拍结束时间", "Please select a bid to accept.": "请选择要接受的竞拍。", "The bid status has been updated successfully.": "竞拍状态已成功更新。", "Create Auction": "创建竞拍", "Select a product": "选择产品", "Please select a product to continue.": "请选择要接受竞拍的产品", "You have already created an auction for this product. Do you want to view your lot?": "您已经为此产品创建了一个竞拍。您要查看您的拍品吗？", "This NFT product has not been minted yet. Please wait for the minting process to complete.": "此NFT产品尚未铸造。请等待铸造过程完成。", "You are about to create an auction for an NFT. Before proceeding, you must verify that you own this NFT and authorize the transfer of ownership to the auction contract.": "您正在创建一个NFT的竞拍。在进行之前，您必须验证您是此 NFT 的拥有者，并授权将其所有权转移至拍卖合约。", "Confirm": "确认", "Continue": "继续", "Verify Ownership": "验证", "Set up Auction": "设置竞拍", "Connect Wallet": "连接钱包", "Confirm to create auction?": "确认创建竞拍？", "Original Price": "原价", "Select Date & Time": "选择日期和时间", "10% of Last Bid": "上次竞价的10%", "Remark: TMR ART will charge 15% commission on seller.": "备注: TMR ART 将向卖家收取15%的佣金。", "Submitted successfully": "提交成功", "Your auction order is successful and can be viewed in the Auction Page.": "您的竞拍订单已成功建立，可在竞拍页面中查看。", "Bid Details": "竞拍详情", "Please select the bid you want to accept": "请选择要接受的竞拍。", "Time": "时间", "Bidders": "出价者", "Biding Price": "竞拍价格", "Confirm Bid?": "确认竞价?", "Confirm Buy Now?": "确认一口价?", "The bidding price is higher than the buy now price. Shall we continue?": "竞价价格高于一口价。要继续吗？", "Bidding": "竞拍中", "Date (New To Old)": "日期（从新到旧）", "Filter-What's New": "最新", "Waiting for confirmation, please stay on this page.": "等待确认中，请停留在此页面。", "Failed to Process. Please retry.": "处理失败，请重试。", "Notification": "通知", "notification setting": "通知设定", "receive updates on each bid": "接收每次出價更新", "Sold Out": "已售罄"}