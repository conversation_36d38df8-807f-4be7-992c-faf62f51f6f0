import React from 'react'
import { View } from 'react-native-animatable'
import { Dimensions, FlatList, StyleSheet } from 'react-native'
import SkeletonElement from '../../SkeletonElement'
import { themeColor } from '@/theme/theme'
import dpr from '@/screens/Utilities/CustomStyleAttribute/dpr'

const { width } = Dimensions.get('screen')

const RenderItem = () => (
  <View>
    <SkeletonElement wrapperStyle={styles.skeletonWrapper} />
  </View>
)

const HubAcssetSleketon = () => {
  return (
    <FlatList
      data={[1]}
      keyExtractor={(_, i) => 'key' + i}
      renderItem={RenderItem}
      showsVerticalScrollIndicator={false}
      numColumns={2}
      columnWrapperStyle={styles.columnWrapper}
    />
  )
}

const styles = StyleSheet.create({
  columnWrapper: {
    columnGap: dpr(20),
    justifyContent: 'space-between',
    marginBottom: dpr(20),
  },
  skeletonWrapper: {
    backgroundColor: 'themeColor.secondaryBackground',
    borderRadius: dpr(10),
    height: 328,
    width: width - 46,
  },
})

export default HubAcssetSleketon
