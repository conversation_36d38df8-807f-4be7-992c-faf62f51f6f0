import 'react-native-gesture-handler'
import React from 'react'
import { StyleSheet, StatusBar, LogBox } from 'react-native'
import { SafeAreaProvider } from 'react-native-safe-area-context'
LogBox.ignoreAllLogs()
LogBox.ignoreLogs(['Non-serializable values were found in the navigation state'])

import { NavigationContainer } from '@react-navigation/native'
import './src/language/i18n'
import { GestureHandlerRootView } from 'react-native-gesture-handler'

import * as Linking from 'expo-linking'
const prefix = Linking.createURL('')

import '@walletconnect/react-native-compat'
import { WagmiProvider } from 'wagmi'
import { mainnet, sepolia } from '@wagmi/core/chains'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { createAppKit, defaultWagmiConfig, AppKit } from '@reown/appkit-wagmi-react-native'

import { PRODUCT_DETAILS } from './components/Navigation/RouteNames'
import appTheme from './theme/theme'
import SplashScreen from './src/components/SplashScreen/SplashScreen'
import Root from './components/Navigation/Root'
import JPushRegistration from './components/Authentication/JPushRegistration/JPushRegistration'
import { config } from './src/config/autolinking'

const queryClient = new QueryClient()

// 1. Get projectId from https://cloud.walletconnect.com
const projectId = process.env.EXPO_PUBLIC_WEB3_WALLET_PROJECT_ID || ''
const schema = process.env.EXPO_PUBLIC_SCHEME

// 2. Create config
const metadata = {
  name: 'TMR Arts',
  description: 'TMR Arts',
  url: 'https://www.tmrarts.com/',
  icons: ['https://framerusercontent.com/images/q0JYQPA47GC34pXxNRabmjs34oQ.png'],
  redirect: {
    native: `${process.env.EXPO_PUBLIC_SCHEME}://`,
  },
}

const chains = [mainnet, sepolia] as const

const wagmiConfig = defaultWagmiConfig({ chains, projectId, metadata })

// 3. Create modal
createAppKit({
  projectId,
  wagmiConfig,
  enableAnalytics: true, // Optional - defaults to your Cloud configuration
})

export default function App() {
  const linking = {
    prefixes: [`${schema}://`, prefix],
    config,
  }

  return (
    <>
      <GestureHandlerRootView style={styles.viewFlex}>
        <SafeAreaProvider>
          <NavigationContainer theme={appTheme} linking={linking} fallback={<SplashScreen />}>
            <StatusBar backgroundColor={'#FFF'} barStyle={'dark-content'} />
            <WagmiProvider config={wagmiConfig}>
              <QueryClientProvider client={queryClient}>
                <AppKit />
                <Root />
                <JPushRegistration />
              </QueryClientProvider>
            </WagmiProvider>
          </NavigationContainer>
        </SafeAreaProvider>
      </GestureHandlerRootView>
    </>
  )
}

const styles = StyleSheet.create({
  viewFlex: {
    flex: 1,
  },
})
