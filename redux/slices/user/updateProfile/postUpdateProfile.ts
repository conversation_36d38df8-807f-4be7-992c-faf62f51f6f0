import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'

import { ApiService } from '../../util/apiRequest'

interface FormData {
  firstname: string
  lastname: string
  email: string
  telephone: string
  wallet_address: string
}

const URL = `${process.env.EXPO_PUBLIC_BASE_API_URL}/account`

const initialState = {
  postUserInfo: null,
  loading: false,
}

export const postUpdateUserProfile = createAsyncThunk(
  'user/postUpdateUserProfile',
  async ({ formData }: { formData: FormData }, { extra }) => {
    const { mutation } = extra as ApiService
    try {
      const response = await mutation(URL, 'PUT', formData)
      return response
    } catch (err) {
      return
    }
  },
)

const postUpdateUserProfileSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(postUpdateUserProfile.pending, (state) => {
      state.loading = true
    })
    builder.addCase(postUpdateUserProfile.fulfilled, (state, { payload }) => {
      state.postUserInfo = payload
      state.loading = false
    })
    builder.addCase(postUpdateUserProfile.rejected, (state) => {
      state.loading = false
    })
  },
})

export default postUpdateUserProfileSlice.reducer
