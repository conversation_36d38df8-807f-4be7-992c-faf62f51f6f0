import { <PERSON><PERSON><PERSON> } from 'buffer'
import * as SecureStore from 'expo-secure-store'
import { AnyAction, Dispatch } from '@reduxjs/toolkit'
import { setAccessToken } from '@/redux/slices/auth/basicAuth'

export interface Query {
  (URL: string, method: string, headers?: Record<string, string>): Promise<any>
}
export interface Mutation {
  (URL: string, method: string, data: any, lng?: string, currency?: string): Promise<any>
}

export interface WalletQuery {
  (URL: string, method: string, headers?: Record<string, string>): Promise<any>
}
export interface ApiService {
  query: Query
  mutation: Mutation
  setDispatch: (dispatch: Dispatch<AnyAction>) => void
  walletQuery: WalletQuery
}
interface RefreshParams {
  old_token: string
  client_id: string
  client_secret: string
}
interface TokenData {
  accessToken: string
  expireTime: number
}

let refreshPromise: Promise<string | null> | null = null

const createApiService = (): ApiService => {
  let dispatch: Dispatch<AnyAction> | null = null

  const setDispatch = (newDispatch: Dispatch<AnyAction>) => {
    dispatch = newDispatch
  }

  const getToken = async (): Promise<string | null> => {
    const tokenData = await SecureStore.getItemAsync('oauthToken')
    if (tokenData) {
      const { accessToken, expireTime }: TokenData = JSON.parse(tokenData)
      if (Date.now() < expireTime) {
        return accessToken
      }
    }
    return null
  }

  const fetchTokens = async (requestParams?: RefreshParams): Promise<string | null> => {
    const isLoggedIn = await SecureStore.getItemAsync('isLoggedIn')
    if (isLoggedIn && !requestParams) {
      clearUserAuthIfExists()
    }

    const response = await accessTokenQuery(
      `${process.env.EXPO_PUBLIC_BASE_API_URL}/oauth2/token/client_credentials`,
      'POST',
      requestParams ?? {},
    )
    const { success, data } = response || {}
    if (success === 1) {
      const refreshTime = Date.now() + data?.expires_in * 1000
      const newCredentials = {
        accessToken: data?.access_token,
        expireTime: refreshTime,
      }
      await SecureStore.setItemAsync('oauthToken', JSON.stringify(newCredentials))
      return data?.access_token
    }
    return null
  }

  const refreshToken = async (): Promise<string | null> => {
    if (!refreshPromise) {
      refreshPromise = (async () => {
        try {
          const oauthToken = await SecureStore.getItemAsync('oauthToken')
          const isLoggedIn = await SecureStore.getItemAsync('isLoggedIn')
          if (oauthToken && isLoggedIn) {
            // check expired time
            const clientCredentials = JSON.parse(oauthToken)
            const userInfo = JSON.parse(isLoggedIn)
            // refresh token
            return fetchTokens({
              old_token: clientCredentials.accessToken,
              client_id: userInfo.customer_id,
              client_secret: process.env.EXPO_PUBLIC_CLIENT_SECRET,
            })
          } else {
            // first time login / no token
            console.debug('first time login')
            return fetchTokens()
          }
        } catch (error) {
          console.error('Error refreshing token:', error)
          return null
        } finally {
          refreshPromise = null
        }
      })()
    }
    return refreshPromise
  }

  const clearUserAuthIfExists = async () => {
    console.debug('has logged info but cannot retrieve old token, remove isLoggedIn')
    await SecureStore.deleteItemAsync('isLoggedIn')
  }

  const mutation = async (
    URL: string,
    method: string,
    data = {},
    lng = 'en',
    currency?: string,
  ) => {
    let token = null

    token = await getToken()

    if (!token) {
      token = await refreshToken()
    }

    const instance = data instanceof FormData
    const body = instance ? data : JSON.stringify(data)
    const contentType = 'application/json'
    const headers = {
      'Content-Type': contentType,
      Accept: 'application/json',
      Authorization: `Bearer ${token}`,
      'X-Oc-Merchant-Language': lng,
      'X-Oc-Merchant-Currency': currency || '',
    }
    return fetch(URL, {
      method: method,
      headers: headers,
      body: body,
    })
      .then((res) => res.json())
      .then((data) => data)
      .catch((err) => err)
  }

  const query = async (URL: string, method = 'GET', headers = {}) => {
    let token = null

    token = await getToken()

    if (!token) {
      token = await refreshToken()
    }

    return fetch(URL, {
      method,
      headers: {
        'content-type': 'application/json',
        Authorization: `Bearer ${token}`,
        ...headers,
      },
    })
      .then(async (res) => {
        return res.json()
      })
      .then((data) => data)
      .catch((err) => err)
  }

  const accessTokenQuery = (URL: string, method = 'POST', data = {}) => {
    const encodedString = Buffer.from(
      `${process.env.EXPO_PUBLIC_CLIENT_ID}:${process.env.EXPO_PUBLIC_CLIENT_SECRET}`,
    ).toString('base64')
    return fetch(URL, {
      method,
      headers: {
        'content-type': 'application/json',
        Authorization: `Basic ${encodedString}`,
      },
      body: data ? JSON.stringify(data) : null,
    })
      .then((res) => res.json())
      .then((data) => data)
      .catch((err) => err)
  }

  const walletQuery = async (URL: string, method = 'GET', headers = {}) => {
    let token = null

    token = await getToken()

    if (!token) {
      token = await refreshToken()
    }

    return fetch(URL, {
      method,
      headers: {
        'content-type': 'application/json',
        Authorization: `Bearer ${token}`,
        ...headers,
      },
    })
      .then((res) => res.json())
      .then((data) => data)
      .catch((err) => err)
  }

  return {
    setDispatch,
    query,
    mutation,
    walletQuery,
  }
}

export const apiService = createApiService()
