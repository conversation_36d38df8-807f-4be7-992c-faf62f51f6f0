import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import { ApiService } from '../util/apiRequest'

interface PushNotificationState {
  loading: boolean
  is_push_enabled: boolean
}

const URL = `${process.env.EXPO_PUBLIC_BASE_API_URL}/setting`

const initialState: PushNotificationState = {
  loading: false,
  is_push_enabled: false,
}

export const getPushNotify = createAsyncThunk('getNotify', async (_, { extra }) => {
  const { query } = extra as ApiService
  const resposne = await query(`${URL}/getUpdatePushSetting`, 'GET')
  return resposne
})

export const setPushNotify = createAsyncThunk('setNotify', async (isEnabled: number, { extra }) => {
  const { mutation } = extra as ApiService
  const resposne = await mutation(`${URL}/updatePushSetting`, 'POST', {
    is_push_enabled: isEnabled,
  })
  return resposne
})

const pushNotificationReducer = createSlice({
  name: 'pushNotification',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(getPushNotify.pending, (state) => {
      state.loading = true
    })
    builder.addCase(getPushNotify.fulfilled, (state, { payload }) => {
      const { success, data } = payload || {}
      if (success === 1) {
        state.is_push_enabled = !!data.is_push_enabled
      }
      state.loading = false
    })
    builder.addCase(getPushNotify.rejected, (state) => {
      state.loading = false
    })
    builder.addCase(setPushNotify.pending, (state) => {
      state.loading = true
    })
    builder.addCase(setPushNotify.fulfilled, (state, { payload }) => {
      const { success, data } = payload || {}
      //self update state
      state.is_push_enabled = !state.is_push_enabled
      state.loading = false
    })
    builder.addCase(setPushNotify.rejected, (state) => {
      state.loading = false
    })
  },
})

export default pushNotificationReducer.reducer
