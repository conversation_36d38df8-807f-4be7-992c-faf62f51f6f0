import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import * as SecureStore from 'expo-secure-store'

import { ApiService } from '../util/apiRequest'

export interface User {
  customer_id: number
  language_id: string
  firstname: string
  lastname: string
  email: string
  telephone: string
  newsletter: string
  ip: string
  status: string
  safe: string
  code: string
  calling_code: string | null
  wallet_address: string
  last_login_time: string
  date_added: string
  wishlist: []
  address_id: number
  custom_fields: []
  wishlist_total: number
  cart_count_products: number
}

interface LoginParams {
  data: {
    email: string
    password: string
    reg_id: string
    device_type: 'ios' | 'android' | 'web'
  }
}

const LOGIN_URL = `${process.env.EXPO_PUBLIC_BASE_API_URL}/login`
const PROFILE_URL = `${process.env.EXPO_PUBLIC_BASE_API_URL}/account`

const initialState = {
  user: {} as User,
  loading: false,
  error: [],
}

export const signInUsingEmailAndPassword = createAsyncThunk(
  'auth/login',
  async (params: LoginParams, { extra }) => {
    const { mutation } = extra as ApiService
    const { data } = params
    try {
      const response = await mutation(LOGIN_URL, 'POST', data)
      return response
    } catch (err) {}
  },
)

export const getUserProfile = createAsyncThunk(
  'user/updateUserProfile',
  async ({ lng }: { lng: string }, { extra }) => {
    const { query } = extra as ApiService
    const response = await query(PROFILE_URL, 'GET', {
      'X-Oc-Merchant-Language': lng,
    })
    return response
  },
)

const signInUsingEmailAndPasswordSlice = createSlice({
  name: 'login',
  initialState,
  reducers: {
    login: (state, { payload }) => {
      state.user = payload?.user
      state.loading = false
    },
    logout: (state) => {
      state.user = {} as User
      state.loading = false
      SecureStore.deleteItemAsync('isLoggedIn')
    },
  },
  extraReducers: (builder) => {
    builder.addCase(signInUsingEmailAndPassword.pending, (state) => {
      state.loading = true
      state.user = {} as User
    })
    builder.addCase(signInUsingEmailAndPassword.fulfilled, (state, { payload }) => {
      const { success, error, data } = payload || {}
      if (success === 1) {
        state.user = data
      } else {
        state.error = error
        state.user = {} as User
      }
      state.loading = false
    })
    builder.addCase(signInUsingEmailAndPassword.rejected, (state) => {
      state.loading = false
      if (Object.keys(state.user).length > 0) {
        state.user = {} as User
      }
    })
    builder.addCase(getUserProfile.pending, (state) => {
      state.loading = true
    })
    builder.addCase(getUserProfile.fulfilled, (state, { payload }) => {
      const { success, data } = payload
      if (success === 1) {
        state.user = data
      }
      state.loading = false
    })
    builder.addCase(getUserProfile.rejected, (state) => {
      state.loading = false
      if (Object.keys(state.user).length > 0) {
        state.user = {} as User
      }
    })
  },
})

export default signInUsingEmailAndPasswordSlice.reducer
export const { login, logout } = signInUsingEmailAndPasswordSlice.actions
