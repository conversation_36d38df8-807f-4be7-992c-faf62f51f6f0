import { combineReducers } from '@reduxjs/toolkit'
import sendVerificationReducer from '../slices/auth/sendVerification'
import allCategory from '@/redux/slices/categories/allCategory'
import featureProducts from '@/redux/slices/featureProducts/featureProducts'
import getShippingReducer from '@/redux/slices/featureProducts/deliveryOption/getShipping'
import updateUserProfile from '@/redux/slices/user/updateProfile/getUpdateProfile'
import postUpdateUserProfile from '@/redux/slices/user/updateProfile/postUpdateProfile'
import getMyAddress from '@/redux/slices/user/address/getMyAddress'
import getCountries from '@/redux/slices/user/address/getCountries'
import getStates from '@/redux/slices/user/address/getStates'
import postNewAddress from '@/redux/slices/user/address/postNewAddress'
import getOrderDetailsSlice from '@/redux/slices/user/orderDetails/orderDetails'
import searchProducts from '@/redux/slices/searchProducts/searchProducts'
import cartProductSlice from '@/redux/slices/cart/getCartProducts'
import storeItemInCartSlice from '@/redux/slices/cart/storeItemInCart'
import postOrdersReducer from '@/redux/slices/cart/order/postOrders'
import applyCouponReducer from '@/redux/slices/cart/coupon/applyCoupon'
import selectForOrderReducer from '@/redux/slices/cart/order/selectForOrder'
import registrationReducer from '@/redux/slices/auth/registration'
import signInReducer from '@/redux/slices/auth/signIn'
import homeBannerReducer from '@/redux/slices/home/<USER>'
import homeWhatsNewReducer from '@/redux/slices/home/<USER>'
import homeMostViewReducer from '@/redux/slices/home/<USER>'
import homeAdvertismentReducer from '@/redux/slices/home/<USER>'
import homeLatestStoryReducer from '@/redux/slices/home/<USER>'
import homeNftReducer from '@/redux/slices/home/<USER>'
import networkReducer from '@/redux/slices/network/network'
import languageReducer from '@/redux/slices/language/language'
import currencyReducer from '@/redux/slices/currency/currencySlice'
import basicAuthReducer from '@/redux/slices/auth/basicAuth'
import productFilterReducer from '@/redux/slices/featureProducts/filters'
import discoverReducer from '@/redux/slices/discover/discover'
import discoverCategoryReducer from '@/redux/slices/discover/discoverCategory'
import discoverDetailReducer from '@/redux/slices/discover/discoverDetail'
import informationReducer from '@/redux/slices/information/information'
import paymentMethodsReducer from '@/redux/slices/cart/getPaymentMethods'
import reCaptchaReducer from '@/redux/slices/auth/reCaptcha'
import repayOrderReducer from '@/redux/slices/cart/order/repayOrder'
import auctionPayNowReducer from '@/redux/slices/auction/paynow'
import getMyAuctionDetailReducer from '@/redux/slices/auction/getMyAuctionDetail'
import exhibitionDetailReducer from '@/redux/slices/exhibition/exhibitionDetail'
import hubPublicFilterReducer from '@/redux/slices/hub/hubPublicFilter'
import hubPrivateFilterReducer from '@/redux/slices/hub/hubPrivateFilter'
import hubShowroomFilterReducer from '@/redux/slices/hub/hubShowroomFilter'
import hubExhibitionFilterReducer from '@/redux/slices/hub/hubExhibitionFilter'
import createAuctionReducer from '@/redux/slices/auction/createAuction'
import assetList from '@/redux/slices/hub/assetList'
import assetRanking from '@/redux/slices/hub/assetRanking'
import updateHubProfile from '@/redux/slices/hub/hubUserProfile'
import hubUserProfile from '@/redux/slices/hub/hubUserProfile'
import exhibitionHall from '@/redux/slices/exhibition/exhibitionHall'
import hubArtworks from '@/redux/slices/hub/hubArtworks'
import transferNftReducer from '@/redux/slices/auction/transferNft'
import userEntryReducer from '@/redux/slices/user/userEntry/userEntrySlice'
import pushNotificationReducer from '@/redux/slices/pushNotification/pushNotification'

const rootReducer = combineReducers({
  allCategory,
  featureProducts,
  getShippingReducer,
  updateUserProfile,
  postUpdateUserProfile,
  getMyAddress,
  getCountries,
  getStates,
  postNewAddress,
  getOrderDetailsSlice,
  searchProducts,
  cartProductSlice,
  storeItemInCartSlice,
  postOrdersReducer,
  applyCouponReducer,
  selectForOrderReducer,
  registrationReducer,
  signInReducer,
  homeBannerReducer,
  homeWhatsNewReducer,
  homeNftReducer,
  homeMostViewReducer,
  homeAdvertismentReducer,
  homeLatestStoryReducer,
  networkReducer,
  languageReducer,
  currencyReducer,
  basicAuthReducer,
  productFilterReducer,
  discoverReducer,
  discoverCategoryReducer,
  discoverDetailReducer,
  informationReducer,
  paymentMethodsReducer,
  reCaptchaReducer,
  repayOrderReducer,
  auctionPayNowReducer,
  getMyAuctionDetailReducer,
  exhibitionDetailReducer,
  hubPublicFilterReducer,
  hubPrivateFilterReducer,
  hubShowroomFilterReducer,
  hubExhibitionFilterReducer,
  createAuctionReducer,
  assetList,
  assetRanking,
  hubUserProfile,
  updateHubProfile,
  exhibitionHall,
  hubArtworks,
  transferNftReducer,
  sendVerificationReducer,
  userEntryReducer,
  pushNotificationReducer,
})

export default rootReducer
