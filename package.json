{"name": "tmrarts", "version": "1.0.0", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "eject": "expo eject", "eas-build-config": "eas build:configure", "android:preview": "eas build -p android --profile preview", "android:development": "eas build --profile development --platform android", "ios:preview": "eas build -p ios --profile preview", "ios:development": "eas build --profile development-simulator --platform ios", "android:release": "yarn run android --variant Release", "android:debug": "yarn run android --variant debug", "ios:release": "yarn run ios --configuration Release", "ios:debug": "yarn run ios --configuration Debug", "lint": "prettier -c '**/*.{ts,tsx}' && eslint --cache --max-warnings=0 '**/*.{ts,tsx}'", "lint:fix": "prettier --write '**/*.{ts,tsx}' && eslint --cache --fix '**/*.{ts,tsx}'"}, "dependencies": {"@babel/preset-typescript": "7.18.6", "@expo-google-fonts/dm-sans": "0.2.2", "@expo-google-fonts/eb-garamond": "^0.2.3", "@expo-google-fonts/roboto": "0.2.2", "@expo/ngrok": "^4.1.3", "@expo/prebuild-config": "~7.0.0", "@gorhom/bottom-sheet": "4", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/datetimepicker": "8.0.1", "@react-native-community/netinfo": "11.3.1", "@react-navigation/bottom-tabs": "6.5.8", "@react-navigation/drawer": "6.5.7", "@react-navigation/material-top-tabs": "6.6.3", "@react-navigation/native": "6.1.8", "@react-navigation/native-stack": "6.9.13", "@reduxjs/toolkit": "1.7.2", "@reown/appkit-wagmi-react-native": "^1.2.4", "@tanstack/react-query": "^5.76.1", "@walletconnect/react-native-compat": "^2.20.2", "buffer": "^6.0.3", "dayjs": "^1.11.13", "expo": "~51.0.37", "expo-application": "~5.9.1", "expo-auth-session": "~5.5.2", "expo-av": "~14.0.7", "expo-blur": "~13.0.2", "expo-build-properties": "~0.12.5", "expo-checkbox": "~3.0.0", "expo-clipboard": "~6.0.3", "expo-crypto": "~13.0.2", "expo-dev-client": "~4.0.28", "expo-device": "~6.0.2", "expo-file-system": "~17.0.1", "expo-font": "~12.0.10", "expo-image-picker": "~15.1.0", "expo-linking": "~6.3.1", "expo-media-library": "~16.0.5", "expo-notifications": "~0.28.19", "expo-secure-store": "~13.0.2", "expo-splash-screen": "~0.27.6", "expo-status-bar": "~1.12.1", "expo-updates": "~0.25.27", "expo-web-browser": "~13.0.3", "i18next": "23.11.3", "jcore-react-native": "^2.2.9", "jpush-react-native": "^3.1.6", "lottie-react-native": "6.7.0", "moment": "2.30.1", "moment-timezone": "0.5.45", "react": "18.2.0", "react-dom": "18.2.0", "react-i18next": "14.1.1", "react-native": "0.74.5", "react-native-animatable": "1.4.0", "react-native-document-picker": "8.2.2", "react-native-dropdown-picker": "5.4.2", "react-native-gesture-handler": "~2.16.1", "react-native-get-random-values": "~1.11.0", "react-native-google-cast": "^4.8.3", "react-native-htmlview": "^0.17.0", "react-native-image-viewing": "^0.2.2", "react-native-image-zoom-viewer": "3.0.1", "react-native-linear-gradient": "^2.8.3", "react-native-modal": "^13.0.1", "react-native-pager-view": "6.3.0", "react-native-phone-number-input": "2.1.0", "react-native-radio-buttons-group": "2.2.10", "react-native-reanimated": "~3.10.1", "react-native-recaptcha-that-works": "2.0.0", "react-native-render-html": "6.3.4", "react-native-safe-area-context": "4.10.5", "react-native-screens": "3.31.1", "react-native-share": "12.0.3", "react-native-star-rating-widget": "1.7.3", "react-native-svg": "15.2.0", "react-native-tab-view": "3.1.1", "react-native-toast-message": "2.2.0", "react-native-web": "~0.19.6", "react-native-webview": "13.8.6", "react-redux": "7.2.6", "reanimated-bottom-sheet": "1.0.0-alpha.22", "redux": "4.1.2", "rn-placeholder": "3.0.3", "underscore": "1.13.4", "uri-scheme": "1.2.1", "viem": "^2.29.3", "wagmi": "^2.15.3"}, "devDependencies": {"@babel/core": "^7.20.0", "@eslint/compat": "^1.1.1", "@eslint/js": "^9.7.0", "@react-native-community/eslint-config": "^3.2.0", "@types/eslint__js": "^8.42.3", "@types/react": "~18.2.79", "@types/react-native-htmlview": "^0.16.5", "@typescript-eslint/eslint-plugin": "^8.20.0", "@typescript-eslint/parser": "^8.20.0", "babel-plugin-inline-import": "^3.0.0", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^9.7.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jest": "^28.11.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-native": "^4.1.0", "prettier": "^3.3.3", "ts-migrate": "^0.1.35", "ts-node": "^10.9.2", "typescript": "~5.3.3"}, "resolutions": {"@walletconnect/ethereum-provider": "2.19.4", "@walletconnect/universal-provider": "2.19.4", "@walletconnect/types": "2.19.4", "@walletconnect/utils": "2.19.4", "@walletconnect/core": "2.19.4", "@walletconnect/sign-client": "2.19.4"}, "private": true}