import {
  withApp<PERSON><PERSON><PERSON>rad<PERSON>,
  withAndroidManifest,
  withSettingsGradle,
  AndroidConfig,
  ConfigPlugin,
  withMainApplication,
  withAppDelegate,
  withDangerousMod,
  withProjectBuildGradle,
} from '@expo/config-plugins'
import { mergeContents } from '@expo/config-plugins/build/utils/generateCode'
import type { ManifestMetaData } from '@expo/config-plugins/build/android/Manifest'
import path from 'path'
import fs from 'fs'

const { addMetaDataItemToMainApplication, getMainApplicationOrThrow } = AndroidConfig.Manifest

type ExtendedManifestMetaDataAttributes = ManifestMetaData['$'] & {
  'tools:replace'?: string
}

const withJPush: ConfigPlugin<{
  appKey: string
  channel: string
  keyStoreFile: string
  keyStorePassword: string
  keyAlias: string
  keyPassword: string
  huaweiAppId: string
  huaweiAppCpId: string
  honorAppId: string
  xiaomiAppId: string
  xiaomiAppKey: string
  isProduction: string
  identifier: string
}> = (
  config,
  {
    appKey,
    channel,
    keyStoreFile,
    keyStorePassword,
    keyAlias,
    keyPassword,
    huaweiAppId,
    huaweiAppCpId,
    honorAppId,
    xiaomiAppId,
    xiaomiAppKey,
    isProduction,
    identifier,
  },
) => {
  const jpushAppKey = appKey || 'e69db25ec269f10e457c8646'
  const jpushChannel = channel || 'defaultChannel'
  const jpushIsProduction = isProduction || 'NO'
  const hwAppId = huaweiAppId || '114233783'
  const hwAppCpId = huaweiAppCpId || '30852000032859916'

  config = withDangerousMod(config, [
    'android',
    async (config) => {
      // for FCM
      const fcmSourceFile = path.join(config.modRequest.projectRoot, 'google-services.json')
      const fcmTargetFile = path.join(
        config.modRequest.platformProjectRoot,
        'app',
        'google-services.json',
      )

      if (fs.existsSync(fcmSourceFile)) {
        fs.copyFileSync(fcmSourceFile, fcmTargetFile)
      }

      // for Huawei
      const huaweiSourceFile = path.join(config.modRequest.projectRoot, 'agconnect-services.json')
      const huaweiTargetFile = path.join(
        config.modRequest.platformProjectRoot,
        'app',
        'agconnect-services.json',
      )

      if (fs.existsSync(huaweiSourceFile)) {
        fs.copyFileSync(huaweiSourceFile, huaweiTargetFile)
      }

      // for honor service - HiPush SDK
      const libsDir = path.join(config.modRequest.platformProjectRoot, 'modules', 'libs')
      if (!fs.existsSync(libsDir)) {
        fs.mkdirSync(libsDir, { recursive: true })
      }

      const hiPushSourceFile = path.join(config.modRequest.projectRoot, 'HiPushSDK-8.0.12.307.aar')
      const hiPushTargetFile = path.join(libsDir, 'HiPushSDK-8.0.12.307.aar')

      if (fs.existsSync(hiPushSourceFile) && !fs.existsSync(hiPushTargetFile)) {
        fs.copyFileSync(hiPushSourceFile, hiPushTargetFile)
      }

      // for honor service
      const honorSourceFile = path.join(config.modRequest.projectRoot, 'mcs-services.json')
      const honorTargetFile = path.join(
        config.modRequest.platformProjectRoot,
        'app',
        'mcs-services.json',
      )

      if (fs.existsSync(honorSourceFile) && !fs.existsSync(honorTargetFile)) {
        fs.copyFileSync(honorSourceFile, honorTargetFile)
      }

      return config
    },
  ])

  config = withProjectBuildGradle(config, (config) => {
    let contents = config.modResults.contents

    // 1. Add Huawei repo to buildscript repositories
    const buildscriptReposPattern =
      /(buildscript\s*{[\s\S]*?repositories\s*{[\s\S]*?mavenCentral\(\))/
    if (
      !contents.includes('https://developer.huawei.com/repo/') &&
      buildscriptReposPattern.test(contents)
    ) {
      contents = contents.replace(
        buildscriptReposPattern,
        `$1
        maven { url 'https://developer.huawei.com/repo/' }`,
      )
    }

    // 2. Update Gradle version to 8.6.0
    if (!contents.includes('com.android.tools.build:gradle:8.6.0')) {
      contents = contents.replace(
        /classpath\('com\.android\.tools\.build:gradle(?::[^']*)?'\)/,
        "classpath('com.android.tools.build:gradle:8.6.0')",
      )
    }

    // 3. Add classpath dependencies to buildscript
    // Place dependencies after kotlin-gradle-plugin
    if (
      !contents.includes('com.huawei.agconnect:agcp:1.9.1.301') ||
      !contents.includes('com.google.gms:google-services:4.3.8')
    ) {
      contents = contents.replace(
        /(buildscript\s*{[\s\S]*?dependencies\s*{[\s\S]*?classpath\('org\.jetbrains\.kotlin:kotlin-gradle-plugin'\))/,
        `$1
        classpath 'com.huawei.agconnect:agcp:1.9.1.301'
        classpath 'com.google.gms:google-services:4.3.8'`,
      )
    }

    // 4. Add both repos after jitpack.io in one operation
    if (
      !contents.includes('https://developer.huawei.com/repo/') ||
      !contents.includes('https://maven.google.com')
    ) {
      contents = contents.replace(
        /(allprojects\s*{[\s\S]*?repositories\s*{[\s\S]*?maven\s*{\s*url\s*'https:\/\/www\.jitpack\.io'\s*})/,
        `$1
        maven { url 'https://developer.huawei.com/repo/' }
        maven { url "https://maven.google.com" }`,
      )
    }

    config.modResults.contents = contents
    return config
  })

  config = withAppBuildGradle(config, (config) => {
    // Add plugin applications at the top (after existing plugins)
    if (!config.modResults.contents.includes("apply plugin: 'com.huawei.agconnect'")) {
      const pluginPattern = /apply plugin: ['"]com\.facebook\.react['"]/
      config.modResults.contents = config.modResults.contents.replace(pluginPattern, (match) => {
        return `${match}
apply plugin: 'com.huawei.agconnect'
apply plugin: 'com.google.gms.google-services'`
      })
    }

    if (!config.modResults.contents.includes('JPUSH_APPKEY')) {
      const defaultConfigPattern = /defaultConfig\s*{[^}]*}/g
      config.modResults.contents = config.modResults.contents.replace(
        defaultConfigPattern,
        (match) => {
          if (match.includes('manifestPlaceholders')) {
            return match.replace(
              /manifestPlaceholders\s*=\s*\[[^\]]*\]/,
              `manifestPlaceholders = [
                  JPUSH_APPKEY: "${jpushAppKey}",
                  JPUSH_CHANNEL: "${jpushChannel}",
                  HUAWEI_APP_ID: "${hwAppId}",
                  HUAWEI_APP_CPID: "${hwAppCpId}",
                  HONOR_APP_ID: "${honorAppId}",
                  XIAOMI_APPKEY: "${xiaomiAppKey}",
                  XIAOMI_APPID: "${xiaomiAppId}",
              ]`,
            )
          } else {
            return match.replace(
              /defaultConfig\s*{/,
              `defaultConfig {
                  manifestPlaceholders = [
                      JPUSH_APPKEY: "${jpushAppKey}",
                      JPUSH_CHANNEL: "${jpushChannel}",
                      HUAWEI_APP_ID: "${hwAppId}",
                      HUAWEI_APP_CPID: "${hwAppCpId}",
                      HONOR_APP_ID: "${honorAppId}",
                      XIAOMI_APPKEY: "${xiaomiAppKey}",
                      XIAOMI_APPID: "${xiaomiAppId}",
                  ]`,
            )
          }
        },
      )

      // Modify existing signingConfigs instead of adding new ones
      if (config.modResults.contents.includes('signingConfigs {') && keyStoreFile) {
        // Update debug config
        const debugInSigningConfigsPattern =
          /(signingConfigs\s*{[\s\S]*?)debug\s*{[\s\S]*?}([\s\S]*?})/
        config.modResults.contents = config.modResults.contents.replace(
          debugInSigningConfigsPattern,
          (match, before, after) => {
            return `${before}debug {
            storeFile file("${keyStoreFile}")
            storePassword "${keyStorePassword}"
            keyAlias "${keyAlias}"
            keyPassword "${keyPassword}"
        }${after}`
          },
        )

        // Add release config to existing signingConfigs block
        const signingConfigsPattern = /signingConfigs\s*{([\s\S]*?)}/
        config.modResults.contents = config.modResults.contents.replace(
          signingConfigsPattern,
          (match) => {
            return `${match}
        release {
            storeFile file("${keyStoreFile}")
            storePassword "${keyStorePassword}"
            keyAlias "${keyAlias}"
            keyPassword "${keyPassword}"
        }`
          },
        )
      }

      const jpushDependency = "implementation project(':jpush-react-native')"
      const jcoreDependency = "implementation project(':jcore-react-native')"
      const huaweiDependency = "implementation 'com.huawei.hms:push:6.12.0.300'"
      const huaweiJpushDependency = "implementation 'cn.jiguang.sdk.plugin:huawei:5.7.0'"
      const honorDependency = "implementation 'cn.jiguang.sdk.plugin:honor:5.7.0'"
      const honorLibDependency = "implementation fileTree(include: ['*.jar','*.aar'], dir: 'libs')"
      const fcmDependency = "implementation 'com.google.firebase:firebase-messaging:23.0.5'"
      const fcmJpushDependency = "implementation 'cn.jiguang.sdk.plugin:fcm:5.7.0'"
      const xiaomiDependency = "implementation 'cn.jiguang.sdk.plugin:xiaomi:5.7.0'"
      const dependenciesPattern = /dependencies\s*{[^}]*}/g

      config.modResults.contents = config.modResults.contents.replace(
        dependenciesPattern,
        (match) => {
          let updated = match
          updated = updated.replace(
            /{/,
            `{\n // JPush dependencies
    ${jpushDependency}
    ${jcoreDependency}
    ${huaweiDependency}
    ${huaweiJpushDependency}
    ${honorLibDependency}
    ${fcmDependency}
    ${fcmJpushDependency}
    ${honorDependency}
    ${xiaomiDependency}
            `,
          )
          return updated
        },
      )
    }
    return config
  })

  config = withAndroidManifest(config, (config) => {
    const mainApplication = getMainApplicationOrThrow(config.modResults)

    // Add JPush custom permission definition
    if (!config.modResults.manifest.permission) {
      config.modResults.manifest.permission = []
    }

    const customPermissions = config.modResults.manifest.permission
    const hasJPushPermissionDef = customPermissions.some(
      (permission: any) =>
        permission.$['android:name'] === `${identifier}.permission.JPUSH_MESSAGE`,
    )

    if (!hasJPushPermissionDef) {
      customPermissions.push({
        $: {
          'android:name': `${identifier}.permission.JPUSH_MESSAGE`,
          'android:protectionLevel': 'signature',
        },
      })
    }

    // notification permission for Android
    if (!config.modResults.manifest['uses-permission']) {
      config.modResults.manifest['uses-permission'] = []
    }

    // permission usage
    const permissions = config.modResults.manifest['uses-permission']
    const missingPermissions = [`${identifier}.permission.JPUSH_MESSAGE`]

    missingPermissions.forEach((permissionName) => {
      const hasPermission = permissions.some(
        (permission: any) => permission.$['android:name'] === permissionName,
      )
      if (!hasPermission) {
        permissions.push({
          $: {
            'android:name': permissionName,
          },
        })
      }
    })

    addMetaDataItemToMainApplication(mainApplication, 'JPUSH_CHANNEL', '${JPUSH_CHANNEL}')
    addMetaDataItemToMainApplication(mainApplication, 'JPUSH_APPKEY', '${JPUSH_APPKEY}')

    addMetaDataItemToMainApplication(
      mainApplication,
      'com.huawei.hms.client.appid',
      '${HUAWEI_APP_ID}',
    )
    addMetaDataItemToMainApplication(
      mainApplication,
      'com.huawei.hms.client.cpid',
      '${HUAWEI_APP_CPID}',
    )

    addMetaDataItemToMainApplication(
      mainApplication,
      'com.google.firebase.messaging.default_notification_icon',
      '@mipmap/ic_launcher',
    )

    // Add Honor app ID
    if (!mainApplication['meta-data']) {
      mainApplication['meta-data'] = []
    }

    mainApplication['meta-data'].push({
      $: {
        'android:name': 'com.hihonor.push.app_id',
        'android:value': '${HONOR_APP_ID}',
        'tools:replace': 'android:value',
      } as ExtendedManifestMetaDataAttributes,
    })

    if (!mainApplication.receiver) {
      mainApplication.receiver = []
    }

    return config
  })

  config = withSettingsGradle(config, (config) => {
    if (!config.modResults.contents.includes(':jpush-react-native')) {
      config.modResults.contents = `${config.modResults.contents}
        include ':jpush-react-native'
        project(':jpush-react-native').projectDir = new File(rootProject.projectDir, '../node_modules/jpush-react-native/android')
        include ':jcore-react-native'
        project(':jcore-react-native').projectDir = new File(rootProject.projectDir, '../node_modules/jcore-react-native/android')
      `
    }
    return config
  })

  config = withMainApplication(config, async (config) => {
    const mainApplication = config.modResults.contents

    // Find the last import statement to append our import after it
    const importEnd = findLastImportStatement(mainApplication)

    // Add import for JPushModule
    const importResult = mergeContents({
      tag: 'jpush-import',
      src: mainApplication,
      newSrc: 'import cn.jiguang.plugins.push.JPushModule',
      anchor: importEnd,
      offset: 1,
      comment: '//',
    })

    if (!importResult.didMerge) {
      console.warn('Failed to add JPush import to MainApplication.kt')
    }

    // Add JPushModule.registerActivityLifecycle call to onCreate method
    const lifecycleResult = mergeContents({
      tag: 'jpush-lifecycle',
      src: importResult.contents,
      newSrc:
        '    //调用此方法：点击通知让应用从后台切到前台\n    JPushModule.registerActivityLifecycle(this)',
      anchor: /super\.onCreate\(\)/,
      offset: 1,
      comment: '//',
    })

    if (!lifecycleResult.didMerge) {
      console.warn('Failed to add JPush lifecycle registration to MainApplication.kt')
    }

    config.modResults.contents = lifecycleResult.contents
    return config
  })

  // Modify AppDelegate.mm for iOS JPush integration using direct manipulation for more reliability
  config = withAppDelegate(config, (config) => {
    let appDelegate = config.modResults.contents

    // Step 1: Add JPush imports
    if (!appDelegate.includes('<RCTJPushModule.h>')) {
      const importSection = `// @generated begin jpush-imports - expo prebuild (DO NOT MODIFY) sync-${generateHash()}
#import <RCTJPushModule.h>
#import "JPUSHService.h"

#ifdef NSFoundationVersionNumber_iOS_9_x_Max
#import <UserNotifications/UserNotifications.h>
#endif
// @generated end jpush-imports`

      // Find position after RCTLinkingManager import
      const linkingImportIndex = appDelegate.indexOf('#import <React/RCTLinkingManager.h>')
      if (linkingImportIndex !== -1) {
        const insertPosition = linkingImportIndex + '#import <React/RCTLinkingManager.h>'.length
        appDelegate =
          appDelegate.slice(0, insertPosition) +
          '\n' +
          importSection +
          appDelegate.slice(insertPosition)
      } else {
        console.warn('Failed to add JPush imports to AppDelegate.mm')
      }
    }

    // Step 2: Add JPUSHRegisterDelegate protocol
    if (!appDelegate.includes('<JPUSHRegisterDelegate>')) {
      const protocolSection = `// @generated begin jpush-protocol - expo prebuild (DO NOT MODIFY) sync-${generateHash()}
@interface AppDelegate () <JPUSHRegisterDelegate>
@end
// @generated end jpush-protocol`

      // Find position before @implementation AppDelegate
      const implementationIndex = appDelegate.indexOf('@implementation AppDelegate')
      if (implementationIndex !== -1) {
        appDelegate =
          appDelegate.slice(0, implementationIndex) +
          protocolSection +
          '\n\n' +
          appDelegate.slice(implementationIndex)
      }

      // Find position before @implementation AppDelegate
      const implementationEndIndex = appDelegate.lastIndexOf('@end')
      if (implementationEndIndex !== -1) {
        const notificationMethodsSection = `// @generated begin jpush-notification-methods - expo prebuild (DO NOT MODIFY) sync-${generateHash()}
        
  //************************************************JPush start************************************************

  //iOS 10 foreground received message
  - (void)jpushNotificationCenter:(UNUserNotificationCenter *)center willPresentNotification:(UNNotification *)notification withCompletionHandler:(void (^)(NSInteger))completionHandler {
    NSDictionary * userInfo = notification.request.content.userInfo;
    if([notification.request.trigger isKindOfClass:[UNPushNotificationTrigger class]]) {
      // Apns
      NSLog(@"iOS 10 APNS foreground received message");
      [JPUSHService handleRemoteNotification:userInfo];
      [[NSNotificationCenter defaultCenter] postNotificationName:J_APNS_NOTIFICATION_ARRIVED_EVENT object:userInfo];
    }
    else {
      // Local notification
      NSLog(@"iOS 10 Local notification foreground received message");
      [[NSNotificationCenter defaultCenter] postNotificationName:J_LOCAL_NOTIFICATION_ARRIVED_EVENT object:userInfo];
    }
    //Need to execute this method, choose whether to alert the user, can select Badge, Sound, Alert
    completionHandler(UNNotificationPresentationOptionAlert);
  }

  //iOS 10 message event callback
  - (void)jpushNotificationCenter:(UNUserNotificationCenter *)center didReceiveNotificationResponse:(UNNotificationResponse *)response withCompletionHandler: (void (^)(void))completionHandler {
    NSDictionary * userInfo = response.notification.request.content.userInfo;
    if([response.notification.request.trigger isKindOfClass:[UNPushNotificationTrigger class]]) {
      // Apns
      NSLog(@"iOS 10 APNS message event callback");
      [JPUSHService handleRemoteNotification:userInfo];
      // Ensure notification click events are received when app is killed
      [[RCTJPushEventQueue sharedInstance]._notificationQueue insertObject:userInfo atIndex:0];
      [[NSNotificationCenter defaultCenter] postNotificationName:J_APNS_NOTIFICATION_OPENED_EVENT object:userInfo];
    }
    else {
      // Local notification
      NSLog(@"iOS 10 Local notification message event callback");
      // Ensure notification click events are received when app is killed
      [[RCTJPushEventQueue sharedInstance]._localNotificationQueue insertObject:userInfo atIndex:0];
      [[NSNotificationCenter defaultCenter] postNotificationName:J_LOCAL_NOTIFICATION_OPENED_EVENT object:userInfo];
    }
    // System requires calling this method
    completionHandler();
  }

  //Custom message
  - (void)networkDidReceiveMessage:(NSNotification *)notification {
    NSDictionary * userInfo = [notification userInfo];
    [[NSNotificationCenter defaultCenter] postNotificationName:J_CUSTOM_NOTIFICATION_EVENT object:userInfo];
  }

  //************************************************JPush end************************************************
// @generated end jpush-notification-methods`

        appDelegate =
          appDelegate.slice(0, implementationEndIndex) +
          notificationMethodsSection +
          '\n\n' +
          appDelegate.slice(implementationEndIndex)
      } else {
        console.warn('Failed to add JPUSHRegisterDelegate protocol to AppDelegate.mm')
      }
    }

    // Step 3: Add JPush initialization in didFinishLaunchingWithOptions
    if (!appDelegate.includes('JPUSHService setupWithOption:launchOptions')) {
      const initSection = `// @generated begin jpush-init - expo prebuild (DO NOT MODIFY) sync-${generateHash()}
  // JPush initialization with provided app key and channel
    [JPUSHService setupWithOption:launchOptions appKey:@"${jpushAppKey}"
                      channel:@"${jpushChannel}" apsForProduction:${jpushIsProduction}];

    // APNS registration
    JPUSHRegisterEntity * entity = [[JPUSHRegisterEntity alloc] init];
    if (@available(iOS 12.0, *)) {
      entity.types = JPAuthorizationOptionAlert|JPAuthorizationOptionBadge|JPAuthorizationOptionSound;
    }
    [JPUSHService registerForRemoteNotificationConfig:entity delegate:self];
// @generated end jpush-init`

      // Find position after self.initialProps = @{};
      const propsIndex = appDelegate.indexOf('self.initialProps = @{};')
      if (propsIndex !== -1) {
        const insertPosition = propsIndex + 'self.initialProps = @{};'.length
        appDelegate =
          appDelegate.slice(0, insertPosition) +
          '\n' +
          initSection +
          '\n' +
          appDelegate.slice(insertPosition)
      } else {
        console.warn('Failed to add JPush initialization to AppDelegate.mm')
      }
    }

    // Step 4: Replace notification handlers
    if (!appDelegate.includes('@generated begin jpush-modify-handlers')) {
      const handlerSection = `// @generated begin jpush-modify-handlers - expo prebuild (DO NOT MODIFY) sync-${generateHash()}
// Modified handlers to integrate JPush functionality
- (void)application:(UIApplication *)application didRegisterForRemoteNotificationsWithDeviceToken:(NSData *)deviceToken
{
  [JPUSHService registerDeviceToken:deviceToken];
  return [super application:application didRegisterForRemoteNotificationsWithDeviceToken:deviceToken];
}

- (void)application:(UIApplication *)application didFailToRegisterForRemoteNotificationsWithError:(NSError *)error
{
  return [super application:application didFailToRegisterForRemoteNotificationsWithError:error];
}

- (void)application:(UIApplication *)application didReceiveRemoteNotification:(NSDictionary *)userInfo fetchCompletionHandler:(void (^)(UIBackgroundFetchResult))completionHandler
{
  [JPUSHService handleRemoteNotification:userInfo];
  [[NSNotificationCenter defaultCenter] postNotificationName:J_APNS_NOTIFICATION_ARRIVED_EVENT object:userInfo];
  completionHandler(UIBackgroundFetchResultNewData);
  return [super application:application didReceiveRemoteNotification:userInfo fetchCompletionHandler:completionHandler];
}
// @generated end jpush-modify-handlers`

      // Find and replace the existing notification handlers
      const startPattern = /\/\/ Explicitly define remote notification delegates/
      const startMatchResult = appDelegate.match(startPattern)

      if (startMatchResult && startMatchResult.index !== undefined) {
        // TypeScript safe version with proper type checking
        const startIndex = startMatchResult.index
        const appDelegateSubstring = appDelegate.substring(startIndex)
        const endPattern =
          /- \(void\)application[\s\S]*?didReceiveRemoteNotification[\s\S]*?fetchCompletionHandler[\s\S]*?}/
        const endMatchResult = appDelegateSubstring.match(endPattern)

        if (endMatchResult && endMatchResult.index !== undefined) {
          const endIndex = startIndex + endMatchResult.index + endMatchResult[0].length
          appDelegate =
            appDelegate.substring(0, startIndex) + handlerSection + appDelegate.substring(endIndex)
        } else {
          // If we can't find the end pattern, just append the handlers before @end
          const endImpl = appDelegate.lastIndexOf('@end')
          if (endImpl !== -1) {
            appDelegate =
              appDelegate.substring(0, endImpl) +
              '\n' +
              handlerSection +
              '\n\n' +
              appDelegate.substring(endImpl)
          }
        }
      } else {
        // If we can't find the start pattern, just append the handlers before @end
        const endImpl = appDelegate.lastIndexOf('@end')
        if (endImpl !== -1) {
          appDelegate =
            appDelegate.substring(0, endImpl) +
            '\n' +
            handlerSection +
            '\n\n' +
            appDelegate.substring(endImpl)
        }
      }
    }

    config.modResults.contents = appDelegate
    return config
  })

  return config
}

// Helper function to find the last import statement in a file
function findLastImportStatement(fileContent: string): RegExp {
  const lines = fileContent.split('\n')
  let lastImportLine = ''

  // Find the last line that starts with "import"
  for (const line of lines) {
    if (line.trim().startsWith('import ')) {
      lastImportLine = line
    }
  }

  if (lastImportLine) {
    // Create a regex that will match this exact line
    const escapedLine = lastImportLine.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
    return new RegExp(escapedLine, 'm')
  }

  // Fallback if no imports are found (unlikely)
  return /package [^;]+;/m
}

// Function to generate a random hash
function generateHash(): string {
  return Math.random().toString(36).substring(2, 15)
}

module.exports = withJPush
