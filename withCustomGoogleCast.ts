import {
  ConfigPlugin,
  withMainActivity,
  withMainApplication,
  withPlugins,
} from '@expo/config-plugins'

// import { ConfigPlugin, withMainActivity } from '@expo/config-plugins'

const withCustomGoogleCast: ConfigPlugin = (config) => {
  return withMainActivity(config, async (config) => {
    const { contents, path } = config.modResults

    // Check if even the import exists
    if (contents.includes('import com.google.android.gms.cast.framework.CastContext')) {
      console.log('Found CastContext import')
    } else {
      console.log('CastContext import not found')
    }

    // Look for the initialization code
    if (contents.includes('CastContext.getSharedInstance')) {
      const lineToWrap = 'CastContext.getSharedInstance\\(this\\)'
      const regex = new RegExp(
        `(// @generated begin react-native-google-cast-onCreate.*\\n\\s*)(${lineToWrap})(\\s*// @generated end react-native-google-cast-onCreate)`,
        'gs',
      )

      config.modResults.contents = contents.replace(
        regex,
        `    $1\n    try {\n      CastContext.getSharedInstance(this)\n    } catch (e: Exception) {\n      e.printStackTrace();\n    }\n    $3`,
      )
    } else {
      console.log('CastContext initialization not found')
    }

    return config
  })
}

export default withCustomGoogleCast
