import type { CompositeScreenProps, NavigatorScreenParams } from '@react-navigation/native'
import type { NativeStackScreenProps } from '@react-navigation/native-stack'
import type { BottomTabScreenProps } from '@react-navigation/bottom-tabs'
import { DrawerScreenProps } from '@react-navigation/drawer'
import { PayloadAction } from '@reduxjs/toolkit'
import { HubSearchFilter, NFTItems } from './hubDetailState'
import { JoinedBidData } from './auction'
import { Address } from '@/redux/slices/user/address/getMyAddress'
import { OrderProduct } from '@/redux/slices/user/orderDetails/orderDetails'

export type RootStackParamList = {
  AccountCreated: undefined
  About: undefined
  AddNewAddress: { isUpdate: boolean; item?: Address; index?: number }
  AddressBook: undefined
  Announcement: undefined
  AuctionListing: undefined
  ExhibitionAccount: undefined
  EditHubProfile: undefined
  AssetList: undefined
  StatisticList: undefined
  HallList: undefined
  EditHall: { id?: number }
  Artworks: { id: number }
  AuctionDetail: { id: number }
  AuctionPayNow: { bidId: number }
  AuctionConfirmed: { bidData: JoinedBidData; lotId: number }
  AuctionSuccess: { bidId: number }
  AuctionFail: { bidId: number }
  BackNavigation: undefined
  ChangePassword: undefined
  ContactUs: undefined
  ConfirmEmail: { data: { email: string }; title: string; nextScreen: string; url: string }
  Discover: undefined
  DiscoverDetail: { postId: string }
  DrawerStack: NavigatorScreenParams<DrawerParamList>
  EditProfile: undefined
  EnquiryForm: undefined
  Event: undefined
  EventDetail: { title: string; date: string; description: string }
  AnnouncementDetail: { title: string; date: string; description: string }
  Faqs: undefined
  FeatureDisplay: { name: string; url: string }
  FilterHome: undefined
  ForgetPassword: undefined
  Hub: undefined
  HubDetail: {
    item: NFTItems
    isShowRoom: boolean
    isSharable: boolean
    canDownload: boolean
    canDownloadImage: boolean
    canDownloadVideo: boolean
    screen: string
    exhibitionHallId: number
  }
  Guide: undefined
  Login: undefined
  Map: undefined
  MyAuctionListing: undefined
  MyAuctionDetail: { id: number }
  MyBidDetail: { bidId: number }
  MyLotDetail: { auctionId: number }
  OrderDetails: { itemId: string }
  OrderHistory: undefined
  PasswordResetDone: undefined
  PaymentAndDelivery: undefined
  PaymentWebview: { url: string }
  PrivateMuseum: undefined
  PrivacyPolicy: undefined
  ProductDetails: { productId: string }
  SelectCurrency: undefined
  SelectLanguage: undefined
  Setting: undefined
  TermsAndConditions: undefined
  Registration: undefined
  ResetPassword: { token: string }
  Vendor: undefined
  RepayOrder: { orderNo: string }
  ExhibitionDetail: { hallId: number }
  CreateAuction: { products: OrderProduct[] }
  CreateAuctionSuccess: undefined
  NotificationSetting: undefined
}

export type DrawerParamList = {
  HomeScreen: NavigatorScreenParams<HomeTabParamList>
  Hub: NavigatorScreenParams<HubTabParamList>
}

export type HomeTabParamList = {
  Home: undefined
  ProductListing: { sort?: string; searchText?: string } | undefined
  Cart: undefined
  'My Account': undefined
}

export type HubTabParamList = {
  PublicMuseum: undefined
  ExhibitionHall: undefined
  Showroom: undefined
  ExhibitionAccount: undefined
}

// Define composite props for the Home screen
export type HomeTabScreenProps<T extends keyof HomeTabParamList> = CompositeScreenProps<
  BottomTabScreenProps<HomeTabParamList, T>,
  CompositeScreenProps<
    DrawerScreenProps<DrawerParamList>,
    NativeStackScreenProps<RootStackParamList>
  >
>

export type HubBottomTabScreenProps<T extends keyof HubTabParamList> = CompositeScreenProps<
  BottomTabScreenProps<HubTabParamList, T>,
  CompositeScreenProps<
    DrawerScreenProps<DrawerParamList>,
    NativeStackScreenProps<RootStackParamList>
  >
>

export type ProductListingScreenProps = CompositeScreenProps<
  BottomTabScreenProps<HomeTabParamList, 'ProductListing'>,
  CompositeScreenProps<
    DrawerScreenProps<DrawerParamList>,
    NativeStackScreenProps<RootStackParamList>
  >
>

export type CartScreenProps = CompositeScreenProps<
  BottomTabScreenProps<HomeTabParamList, 'Cart'>,
  CompositeScreenProps<
    DrawerScreenProps<DrawerParamList>,
    NativeStackScreenProps<RootStackParamList>
  >
>

export type RootStackScreenProps<T extends keyof RootStackParamList> = NativeStackScreenProps<
  RootStackParamList,
  T
>

declare module '@react-navigation/native' {
  export type RootParamList = RootStackParamList
}
