import 'ts-node/register'
import { ExpoConfig, ConfigContext } from 'expo/config'

export default ({ config }: ConfigContext): ExpoConfig => ({
  ...config,
  name: process.env.APP_NAME || 'TMR Arts',
  slug: process.env.APP_SLUG || 'tmrarts',
  icon: process.env.ICON || './assets/icon.png',
  scheme: process.env.SCHEME || 'com.tmrarts',
  splash: {
    ...config.splash,
    image: process.env.SPLASH || './assets/splash.png',
    backgroundColor: process.env.SPLASH_BACKGROUND_COLOR || '#FFFFFF',
  },
  plugins: [
    [
      'expo-font',
      {
        fonts: [
          'node_modules/@expo-google-fonts/dm-sans/DMSans_500Medium.ttf',
          'node_modules/@expo-google-fonts/dm-sans/DMSans_700Bold.ttf',
          'node_modules/@expo-google-fonts/dm-sans/DMSans_400Regular.ttf',
          'node_modules/@expo-google-fonts/roboto/Roboto_400Regular.ttf',
          'node_modules/@expo-google-fonts/roboto/Roboto_500Medium.ttf',
          'node_modules/@expo-google-fonts/roboto/Roboto_500Medium_Italic.ttf',
          'node_modules/@expo-google-fonts/eb-garamond/EBGaramond_400Regular.ttf',
          'node_modules/@expo-google-fonts/eb-garamond/EBGaramond_500Medium.ttf',
          'node_modules/@expo-google-fonts/eb-garamond/EBGaramond_700Bold.ttf',
        ],
      },
    ],
    'expo-secure-store',
    './queries.js',
    [
      'react-native-share',
      {
        ios: ['fb', 'instagram', 'twitter'],
        android: ['com.facebook.katana', 'com.instagram.android', 'com.twitter.android'],
      },
    ],
    './withCustomGoogleCast.ts',
    ['react-native-google-cast', { androidPlayServicesCastFrameworkVersion: '22.0.0' }],
    ['expo-build-properties', { ios: { deploymentTarget: '14.0' } }],
    [
      'expo-media-library',
      {
        savePhotosPermission: 'Allow ${PRODUCT_NAME} App to save photos.',
      },
    ],
    [
      './jpushPlugin.ts',
      {
        appKey: process.env.JPUSH_APP_KEY || '',
        channel: process.env.JPUSH_CHANNEL || '',
        keyStoreFile: process.env.ANDROID_KEYSTORE_FILE || '',
        keyStorePassword: process.env.ANDROID_KEYSTORE_PASSWORD || '',
        keyAlias: process.env.ANDROID_KEY_ALIAS || '',
        keyPassword: process.env.ANDROID_KEY_PASSWORD || '',
        huaweiAppId: process.env.HUAWEI_APP_ID || '',
        huaweiAppCpId: process.env.HUAWEI_APP_CPID || '',
        honorAppId: process.env.HONOR_APP_ID || '',
        xiaomiAppId: process.env.XIAOMI_APP_ID || '',
        xiaomiAppKey: process.env.XIAOMI_APP_KEY || '',
        isProduction: process.env.JPUSH_IS_PRODUCTION || 'NO',
        identifier: process.env.IDENTIFIER || 'com.tmrarts.dev',
      },
    ],
  ],
  ios: {
    ...config.ios,
    bundleIdentifier: process.env.IDENTIFIER || 'com.tmrarts.dev',
  },
  android: {
    ...config.android,
    package: process.env.IDENTIFIER || 'com.tmrarts.dev',
    adaptiveIcon: {
      ...config.android!.adaptiveIcon,
      foregroundImage: process.env.ANDROID_FOREGROUND_IMAGE || './assets/adaptive-icon.png',
      backgroundColor: process.env.ANDROID_ADAPTIVE_BACKGROUND_COLOR || '#FFFFFF',
    },
    permissions: ['ACCESS_NETWORK_STATE', 'POST_NOTIFICATION'],
  },
  extra: {
    eas: {
      projectId: 'dae3b059-a7c2-47af-9639-f55831c848f8',
    },
  },
  updates: {
    url: 'https://u.expo.dev/dae3b059-a7c2-47af-9639-f55831c848f8',
  },
  runtimeVersion: {
    policy: 'appVersion',
  },
})
