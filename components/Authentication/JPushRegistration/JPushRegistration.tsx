import React, { useEffect } from 'react'
import JPush from 'jpush-react-native'
import * as Notifications from 'expo-notifications'
import { setJPushRegId } from '@/redux/slices/user/updateProfile/getUpdateProfile'
import { useAppDispatch } from '@/hooks/reduxHooks'

const JPushRegistration = () => {
  const dispatch = useAppDispatch()

  async function registerForPushNotificationsAsync() {
    const { status: existingStatus } = await Notifications.getPermissionsAsync()
    let finalStatus = existingStatus
    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync()
      finalStatus = status
    }
    if (finalStatus !== 'granted') {
      console.error('Permission not granted to get push token for push notification!')
      return
    }
    return finalStatus
  }

  useEffect(() => {
    try {
      registerForPushNotificationsAsync().then((finalStatus) => {
        console.log('permission status: ', finalStatus)
        if (finalStatus === 'granted') {
          console.log('Before setting logger enable')
          JPush.setLoggerEnable(true)
          console.log('After setting logger enable')

          JPush.init({
            appKey: 'e69db25ec269f10e457c8646',
            channel: 'defaultChannel',
            production: false,
          })

          JPush.setBadge({ badge: 0, appBadge: 0 })

          JPush.addConnectEventListener((r) => {
            console.log('JPush connection status:', r)
            if (r.connectEnable) {
              JPush.getRegistrationID((registrationId) => {
                console.log('JPush Registration ID:', registrationId)
              })
            } else {
              console.log('JPush connection disabled')
            }
          })

          JPush.addNotificationListener((r) => {
            console.log('notification listener: ')
            console.log(JSON.stringify(r))
          })

          setTimeout(() => {
            console.log('Checking JPush status after delay')
            JPush.getRegistrationID((registrationId) => {
              console.log('Delayed JPush Registration check:', registrationId || 'not available')
              dispatch(setJPushRegId(registrationId.registerID))
            })
          }, 5000)
        }
      })
    } catch (error) {
      console.error('JPush initialization failed: ', error)
    }
  }, [])
  return null
}
export default JPushRegistration
