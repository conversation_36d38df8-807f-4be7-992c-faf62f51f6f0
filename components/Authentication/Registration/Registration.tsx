import React, { useEffect, useState } from 'react'
import {
  Text,
  View,
  ScrollView,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
} from 'react-native'
import { useFocusEffect, useNavigation } from '@react-navigation/native'
import { NativeStackScreenProps } from '@react-navigation/native-stack'
import { loginStyles } from '../Login/login.style'
import { formValidation } from './formValidation'
import PasswordIcon from '../Login/PasswordIcon'
import { registrationStyle } from './registration.style'
import useCustomToast from '@/hooks/useCustomToast'

import { registrationUsingEmailAndPassword } from '@/redux/slices/auth/registration'
import { sendVerificationCode } from '@/redux/slices/auth/sendVerification'
import useLangTranslation from '@/hooks/useLangTranslation'
import UserIcon from '@/assets/svgs/tabNaviagtion/profile.svg'
import EmailIcon from '@/assets/svgs/settings/email.svg'
import PhoneIcon from '@/assets/svgs/settings/phone.svg'
import WalletIcon from '@/assets/svgs/settings/payment.svg'
import { RootStackParamList } from '@/types/navigations'
import CustomSVG from '@/src/components/CustomSVG/CustomSVG'
import { commonStyles, formStyle, mb5 } from '@/screens/Utilities/CommonStyles/common.styles'
import CustomTextInput from '@/src/components/CustomInput/CustomTextInput/CustomTextInput'
import TncCheckbox from '@/screens/ShoppingCart/TncCheckbox'
import { useAppDispatch, useAppSelector } from '@/hooks/reduxHooks'
import CustomButton from '@/src/components/CustomButton/CustomButton'
import { saveRecaptchaToken } from '@/redux/slices/auth/reCaptcha'
import TurnstileCaptcha from '@/src/components/TurnstileCaptcha/TurnstileCaptcha'
import { regEmail } from '@/components/Authentication/Registration/formValidation'
import { resetUserEntryTabIndex } from '@/redux/slices/user/userEntry/userEntrySlice'

type Props = NativeStackScreenProps<RootStackParamList, 'Registration'>

interface FormError {
  firstname?: string
  lastname?: string
  email?: string
  email_code?: string
  password?: string
  confirm?: string
  telephone?: string
  wallet_address?: string
  agree?: string
}

export interface RegistrationForm {
  firstname: string
  lastname: string
  email: string
  email_code: string
  password: string
  confirm: string
  telephone: string
  wallet_address: string
  agree: number
}

const initialValue: RegistrationForm = {
  firstname: '',
  lastname: '',
  email: '',
  email_code: '',
  password: '',
  confirm: '',
  telephone: '',
  wallet_address: '',
  agree: 0,
}

const Registration = (props: Props) => {
  const { trans } = useLangTranslation()
  const navigation = useNavigation()

  const { loading } = useAppSelector((state) => state.registrationReducer)
  const { reCaptchaToken } = useAppSelector((state) => state.reCaptchaReducer)
  const [seePassword, setSeePassword] = useState(false)
  const [formValue, setFormValue] = useState(initialValue)
  const [formError, setFormError] = useState({} as FormError)
  const dispatch = useAppDispatch()
  const [reCaptchaError, setReCaptchaError] = useState(false)
  const [countdown, setCountdown] = useState(0)
  const [isCounting, setIsCounting] = useState(false)
  const showToast = useCustomToast()

  const handleChange = (field: string, text: string | number | boolean) => {
    setFormValue({
      ...formValue,
      [field]: text,
    })
  }

  const handleSignUp = async () => {
    const error = formValidation({
      value: formValue,
      passwordPreferences: null,
      trans,
    })

    if (Object.keys(error)?.length > 0) {
      setFormError(error)
      return
    }
    setFormError({})

    if (!reCaptchaToken) {
      setReCaptchaError(true)
      return
    }
    setReCaptchaError(false)

    Keyboard.dismiss()
    await signUpApiRequest()
  }

  const signUpApiRequest = async () => {
    const inputData = {
      ...formValue,
      'g-recaptcha-response': reCaptchaToken,
    }
    try {
      const { payload } = await dispatch(registrationUsingEmailAndPassword({ data: inputData }))
      const { success, error } = payload || {}
      if (success === 1) {
        setFormValue(initialValue)
        setFormError({} as FormError)
        dispatch(resetUserEntryTabIndex())
        showToast({
          text1: trans('Successfully created account'),
          type: 'common',
          position: 'bottom',
          props: { variant: 'success' },
        })
        navigation.navigate('AccountCreated')
      } else {
        if (error.length > 0) {
          let err = ''
          error.map((message: string) => {
            err += trans(message) + '\n'
          })
          showToast({
            text1: err,
            type: 'common',
            position: 'bottom',
            props: { variant: 'error' },
          })
        }
      }
    } catch (error) {
      showToast({
        text1: trans('Something wrong! Please retry later'),
        type: 'common',
        position: 'bottom',
        props: { variant: 'error' },
      })
      console.error(error)
    }
  }

  const handleSeePassword = () => {
    setSeePassword(!seePassword)
  }

  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      dispatch(saveRecaptchaToken(''))
    })
    return unsubscribe
  }, [navigation])

  const navigateToPrivacyPolicy = () => {
    navigation.navigate('PrivacyPolicy')
  }

  const handleCaptcha = (token: string) => {
    dispatch(saveRecaptchaToken(token))
  }

  useFocusEffect(
    React.useCallback(() => {
      return () => {
        // clean up when screen is unfocused
        setFormValue(initialValue)
      }
    }, []),
  )

  const handleVerificationCode = async () => {
    if (isCounting) return
    if (regEmail.test(formValue.email) === false) {
      setFormError({
        email: trans('Enter a valid email address'),
      })
      return
    }

    try {
      const { payload } = await dispatch(sendVerificationCode({ data: { email: formValue.email } }))
      const { success, error } = payload || {}
      if (success === 1) {
        setCountdown(120)
        setIsCounting(true)
      }
    } catch (error) {
      setIsCounting(false)
      showToast({
        text1: trans('Something wrong! Please retry later'),
        type: 'common',
        position: 'bottom',
        props: { variant: 'error' },
      })
      console.error(error)
    }
  }

  useEffect(() => {
    let timer: NodeJS.Timeout
    if (isCounting) {
      if (countdown > 0) {
        timer = setTimeout(() => setCountdown((c) => c - 1), 1000)
      } else {
        setIsCounting(false)
      }
    }
    return () => clearTimeout(timer)
  }, [countdown, isCounting])

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={{ flex: 1 }}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={loginStyles.container}>
          <View style={[loginStyles.body, registrationStyle.body]}>
            <View style={registrationStyle.fidleRow}>
              <CustomTextInput
                label={
                  <Text style={formStyle.labelText}>
                    <Text style={commonStyles.aterisk}>*</Text> {trans('First Name')}
                  </Text>
                }
                isError={!!formError?.firstname}
                error={formError?.firstname}
                autoComplete="given-name"
                autoCapitalize="none"
                isFirst={true}
                value={formValue.firstname}
                placeholder={trans('e.g. Tai Man')}
                onChangeText={(text) => handleChange('firstname', text)}
                rightIcon={<CustomSVG svgIcon={UserIcon} />}
              />
            </View>
            <View style={registrationStyle.fidleRow}>
              <CustomTextInput
                label={
                  <Text style={formStyle.labelText}>
                    <Text style={commonStyles.aterisk}>*</Text> {trans('Last Name')}
                  </Text>
                }
                isError={!!formError?.lastname}
                error={formError?.lastname}
                isFirst={true}
                autoComplete="family-name"
                value={formValue.lastname}
                placeholder={trans('e.g. Chan')}
                onChangeText={(text) => handleChange('lastname', text)}
                rightIcon={<CustomSVG svgIcon={UserIcon} />}
              />
            </View>
          </View>
          <CustomTextInput
            label={
              <Text style={formStyle.labelText}>
                <Text style={commonStyles.aterisk}>*</Text> {trans('Email Address')}
              </Text>
            }
            isError={!!formError?.email}
            error={formError?.email}
            autoComplete="email"
            value={formValue.email}
            placeholder={trans('e.g. <EMAIL>')}
            onChangeText={(text) => handleChange('email', text)}
            autoCapitalize="none"
            style={{ flex: 1 }}
            rightButton={
              <Text style={formStyle.verifiText}>
                {isCounting ? `(${countdown}s) ${trans('Resend')}` : trans('Send Code')}
              </Text>
            }
            onRightButtonPress={handleVerificationCode}
          />
          <CustomTextInput
            label={
              <Text style={formStyle.labelText}>
                <Text style={commonStyles.aterisk}>*</Text> {trans('Email Verification Code')}
              </Text>
            }
            isError={!!formError?.email_code}
            error={formError?.email_code}
            autoComplete="tel"
            value={formValue.email_code}
            onChangeText={(text) => handleChange('email_code', text)}
          />

          <CustomTextInput
            label={<Text style={formStyle.labelText}>{trans('Telephone')}</Text>}
            isError={!!formError?.telephone}
            error={formError?.telephone}
            autoComplete="tel"
            value={formValue.telephone}
            placeholder={trans('e.g. 92334455')}
            onChangeText={(text) => handleChange('telephone', text)}
            rightIcon={<CustomSVG svgIcon={PhoneIcon} />}
          />

          <CustomTextInput
            label={<Text style={formStyle.labelText}>{trans('Wallet Address')}</Text>}
            isError={!!formError?.wallet_address}
            error={formError?.wallet_address}
            value={formValue.wallet_address}
            placeholder={trans('e.g. 0xd3bv3...2123')}
            onChangeText={(text) => handleChange('wallet_address', text)}
            rightIcon={<CustomSVG svgIcon={WalletIcon} />}
          />

          <CustomTextInput
            label={
              <Text style={formStyle.labelText}>
                <Text style={commonStyles.aterisk}>*</Text> {trans('Password')}
              </Text>
            }
            isError={!!formError?.password}
            error={formError?.password}
            value={formValue.password}
            secureTextEntry={!seePassword ? true : false}
            onChangeText={(text) => handleChange('password', text)}
            rightIcon={
              <PasswordIcon seePassword={seePassword} handleSeePassword={handleSeePassword} />
            }
          />

          <CustomTextInput
            label={
              <Text style={formStyle.labelText}>
                <Text style={commonStyles.aterisk}>*</Text> {trans('Confirm Password')}
              </Text>
            }
            isError={!!formError?.confirm}
            error={formError?.confirm}
            value={formValue.confirm}
            secureTextEntry={true}
            onChangeText={(text) => handleChange('confirm', text)}
          />

          <View style={{ marginTop: 20, minHeight: 90 }}>
            <TurnstileCaptcha handleToken={handleCaptcha} reCaptchaError={reCaptchaError} />
          </View>

          <TncCheckbox
            agree={!!formValue.agree}
            handleChange={(value) => handleChange('agree', value)}
            onPress={navigateToPrivacyPolicy}
          />
          <Text style={[formStyle.noteText, mb5(formError?.agree)]}>{formError?.agree}</Text>

          <CustomButton onPress={handleSignUp} loading={loading} text={trans('Create Account')} />
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  )
}

export default Registration
