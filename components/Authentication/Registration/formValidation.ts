import { TFunction } from 'i18next'
import { RegistrationForm } from './Registration'
import { ResetPasswordForm } from '../ResetPassword/ResetPassword'
import { ChangePasswordForm } from '../ChangePassword/ChangePassword'

export const regEmail = /^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/
export const regPhone = /^[0-9-]{6,20}$/
export const regWalletAddress = /^0x[a-fA-F0-9]{40}$/i

interface FormError {
  firstname?: string
  lastname?: string
  email?: string
  email_code?: string
  telephone?: string
  wallet_address?: string
  password?: string
  confirm?: string
  agree?: string
}

export const formValidation = ({
  value,
  passwordPreferences,
  trans,
}: {
  value: RegistrationForm
  passwordPreferences: null
  trans: TFunction<'translation', undefined>
}) => {
  const error = {} as FormError
  if (!value.firstname) {
    error.firstname = trans('First name is required')
  } else if (value.firstname.length > 32) {
    error.firstname = trans('First name is too large')
  } else if (value.firstname.length < 1) {
    error.firstname = trans('First name should be at least 1 characters')
  }

  if (!value.lastname) {
    error.lastname = trans('Last name is required')
  } else if (value.lastname.length > 32) {
    error.lastname = trans('Last name is too large')
  } else if (value.lastname.length < 1) {
    error.lastname = trans('Last name should be at least 1 characters')
  }

  if (!value.email) {
    error.email = trans('Email is required')
  } else if (regEmail.test(value.email) === false) {
    error.email = trans('Enter a valid email address')
  }
  if (!value.email_code) {
    error.email_code = trans('Email verification is required')
  }

  if (!value.telephone) {
    error.telephone = trans('Phone is required')
  } else if (
    regPhone.test(value.telephone.toString()) === false ||
    value.telephone.length < 3 ||
    value.telephone.length > 32
  ) {
    error.telephone = trans('Enter a valid phone format')
  }

  if (
    value.wallet_address &&
    value.wallet_address.length > 0 &&
    regWalletAddress.test(value.wallet_address) === false
  ) {
    error.wallet_address = trans('Enter a valid wallet address format')
  }

  if (!value.password) {
    error.password = trans('Password is required')
  } else if (value.password.length < 6) {
    error.password = trans('Password should be at least 8 characters')
  }

  if (!value.confirm) {
    error.confirm = trans('Confirm password is required')
  } else if (value.confirm !== value.password) {
    error.confirm = trans('Password does not match')
  }

  if (!value.agree) {
    error.agree = trans('You must agree to the terms and conditions.')
  }

  const err = passwordValidation(value, passwordPreferences, trans)
  return { ...error, ...err }
}

export function passwordValidation(
  value: RegistrationForm | ResetPasswordForm | ChangePasswordForm,
  passwordPreferences: null,
  trans: TFunction<'translation', undefined>,
) {
  const error: { password?: string; old_password?: string; confirm?: string } = {}
  if (!value.password) {
    error.password = trans('Password is required')
  } else if (value.password.length < 6 || value.password.length > 20) {
    error.password = trans('Password must contain 6 to 20 characters')
  }

  if ('old_password' in value && !value.old_password) {
    error.old_password = trans('Password is required')
  } else if (value.password.length < 6 || value.password.length > 20) {
    error.old_password = trans('Password must contain 6 to 20 characters')
  }

  if ('confirm' in value && !value.confirm) {
    error.confirm = trans('Password is required')
  } else if (value.password.length < 6 || value.password.length > 20) {
    error.confirm = trans('Password must contain 6 to 20 characters')
  }
  return error

  // const checkRegex = (v) => {
  //   const r = new RegExp(v)
  //   return r.test(value.password)
  // }
  // for (const preference in passwordPreferences) {
  //   if (preference == 'lowercase' && passwordPreferences[preference]) {
  //     regPassword = regPassword + `(?=.*[a-z])`
  //     passwordError = passwordError + `lowercase `
  //   }
  //   if (preference == 'uppercase' && passwordPreferences[preference]) {
  //     regPassword = regPassword + `(?=.*[A-Z])`
  //     passwordError = passwordError + `uppercase `
  //   }
  //   if (preference == 'number' && passwordPreferences[preference]) {
  //     regPassword = regPassword + `(?=.*[0-9])`
  //     passwordError = passwordError + `numbers `
  //   }
  //   if (preference == 'symbol' && passwordPreferences[preference]) {
  //     regPassword = regPassword + `(?=.*[!@#$%^&*()_+=-?<>{}~])`
  //     passwordError = passwordError + `symbols `
  //   }
  // }
  // let str = passwordError.split(' ').slice(0, -1).join(', ')
  // const lastCommaIndex = str.lastIndexOf(',')
  // if (lastCommaIndex !== -1) {
  //   const beforeComma = str.substring(0, lastCommaIndex)
  //   const afterComma = str.substring(lastCommaIndex + 1)
  //   str = 'with ' + beforeComma + ' and' + afterComma
  // } else if (lastCommaIndex == -1) {
  //   str = str ? `with ${str}` : str
  // }
  // passwordError = str
}
