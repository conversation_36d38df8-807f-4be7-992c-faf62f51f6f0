import { StyleSheet, ViewStyle } from 'react-native'
import dpr from '../../../screens/Utilities/CustomStyleAttribute/dpr'
import { themeColor } from '@/theme/theme'
import { Styles } from '@/types/style'

export const loginStyles = StyleSheet.create({
  body: {
    marginTop: dpr(30),
  },
  container: {
    backgroundColor: themeColor.primaryBackground,
    paddingBottom: dpr(30),
    paddingHorizontal: dpr(20),
  },
  forgetPassword: {
    color: themeColor.secondText,
    fontFamily: 'DMSans_500Medium',
    fontWeight: '500',
    marginTop: dpr(8),
  },
  inputField: {
    flex: 1,
    fontFamily: 'Roboto_400Regualr',
    fontSize: dpr(16),
  },
  inputText: {
    color: themeColor.primaryText,
    fontFamily: 'Roboto_500Medium',
    fontSize: dpr(16),
    marginBottom: dpr(10),
  },
  inputTextContainer: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  loginButton: {
    alignItems: 'center',
    backgroundColor: themeColor.secondaryBackground,
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: dpr(25),
    padding: dpr(10),
    width: dpr('wf') - dpr(40),
  },
  loginButtonText: {
    color: themeColor.primaryColoredText,
    fontFamily: 'DMSans_500Medium',
    fontSize: dpr(16),
  },
  loginText: {
    fontFamily: 'DMSans_700Bold',
    fontSize: dpr(24),
    marginBottom: dpr(4),
    marginTop: dpr(45),
  },
  newAccount: {
    flexDirection: 'row',
    fontSize: dpr(16),
    justifyContent: 'center',
    marginBottom: dpr(20),
    marginTop: dpr(25),
  },
  noteText: {
    color: themeColor.primaryWarning,
    fontFamily: 'Roboto_500Medium_Italic',
    fontSize: dpr(13),
    marginTop: dpr(5),
  },
  or: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    marginVertical: dpr(18),
    width: dpr('wf') - dpr(40),
  },
  orText: {
    color: themeColor.secondText,
    fontFamily: 'Roboto_500Medium',
    fontSize: dpr(18),
    lineHeight: dpr(26),
    marginHorizontal: dpr(10),
  },
  passIconCont: {
    height: 18,
    padding: 10,
    position: 'relative',
    width: 20,
  },
  passIconSubCont: {
    bottom: 0,
    position: 'absolute',
  },
  register: {
    color: themeColor.primaryText,
    fontFamily: 'Roboto_500Medium',
    fontSize: dpr(16),
    lineHeight: dpr(26),
  },
})

export const inputFieldContainer = (error: boolean): ViewStyle => ({
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'space-between',
  borderWidth: 1,
  borderColor: error ? themeColor.primaryWarning : themeColor.primaryText,
  paddingVertical: dpr(6),
  paddingHorizontal: dpr(10),
})
