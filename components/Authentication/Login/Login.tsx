import React, { useState } from 'react'
import { Text, View, ScrollView, Pressable, Keyboard, Platform } from 'react-native'
import { useNavigation } from '@react-navigation/native'
import { NativeStackNavigationProp } from '@react-navigation/native-stack'
import { loginFormValidation } from './loginFormValidation'

import { loginStyles } from './login.style'
import PasswordIcon from './PasswordIcon'
import { storeUserInfoInSecureStore } from './ssoFunctions'
import RecaptchaCheckbox from '../RecaptchaCheckbox/RecaptchaCheckbox'
import useLangTranslation from '@/hooks/useLangTranslation'
import useCustomToast from '@/hooks/useCustomToast'
import { getUserProfile, signInUsingEmailAndPassword } from '@/redux/slices/auth/signIn'
import useAuth from '@/hooks/useAuth'
import CustomTextInput from '@/src/components/CustomInput/CustomTextInput/CustomTextInput'
import { useAppDispatch, useAppSelector } from '@/hooks/reduxHooks'
import { RootStackParamList } from '@/types/navigations'
import CustomButton from '@/src/components/CustomButton/CustomButton'

interface LoginError {
  email?: string
  password?: string
}

export interface LoginForm {
  email: string
  password: string
  reg_id: string
  device_type: 'ios' | 'android' | 'web'
}

const initialValue: LoginForm = {
  email: '',
  password: '',
  reg_id: '',
  device_type: Platform.OS === 'ios' ? 'ios' : Platform.OS === 'android' ? 'android' : 'web',
}

const Login = () => {
  const { trans } = useLangTranslation()
  const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList, 'Login'>>()
  const dispatch = useAppDispatch()
  const { jPushRegId } = useAppSelector((state) => state.updateUserProfile)
  const { loading } = useAppSelector((state) => state.signInReducer)

  const [seePassword, setSeePassword] = useState(false)
  const [formValue, setFormValue] = useState({
    ...initialValue,
    reg_id: jPushRegId,
  })
  const [formError, setFormError] = useState<LoginError>({})
  const [reCaptchaError, setReCaptchaError] = useState(false)
  const showToast = useCustomToast()
  const { lng } = useAuth()

  const handleLogin = async () => {
    const error = loginFormValidation(formValue, trans)
    if (Object.keys(error).length > 0) {
      setFormError(error)
      return
    }
    setFormError(initialValue)

    // if (!reCaptchaToken) {
    //   setReCaptchaError(true)
    //   return
    // }
    // setReCaptchaError(false)

    Keyboard.dismiss()
    await signInApiRequest()
  }

  const signInApiRequest = async () => {
    try {
      const { payload } = await dispatch(signInUsingEmailAndPassword({ data: formValue }))
      const { success, error, data } = payload || {}
      if (success === 1) {
        await storeUserInfoInSecureStore(data)
      } else {
        if (error.length > 0) {
          if (error[0] === 'User is logged.') {
            const { payload } = await dispatch(getUserProfile({ lng }))
            console.debug('error relogin payload', payload)
            await storeUserInfoInSecureStore(payload?.data)
            return false
          }

          let errorMessage = ''
          error.map((message: string) => {
            errorMessage += trans(message)
          })
          showToast({
            text1: errorMessage,
            type: 'common',
            position: 'bottom',
            props: { variant: 'error' },
          })
        }
      }
    } catch (err) {}
  }

  const handleChange = (name: string, text: string) => {
    setFormValue({
      ...formValue,
      [name]: text,
    })
  }
  const handleSeePassword = () => {
    setSeePassword(!seePassword)
  }

  return (
    <ScrollView showsVerticalScrollIndicator={false} keyboardShouldPersistTaps="always">
      <View style={loginStyles.container}>
        <View style={loginStyles.body}>
          <CustomTextInput
            autoCapitalize="none"
            label={trans('Email Address')}
            value={formValue.email}
            onChangeText={(text) => handleChange('email', text)}
            isError={!!formError?.email}
            error={formError?.email ? trans(formError?.email) : ''}
            isFirst={true}
          />
          <CustomTextInput
            label={trans('Password')}
            value={formValue.password}
            onChangeText={(text) => handleChange('password', text)}
            rightIcon={
              <PasswordIcon seePassword={seePassword} handleSeePassword={handleSeePassword} />
            }
            secureTextEntry={!seePassword ? true : false}
            isError={!!formError?.password}
            error={formError?.password ? trans(formError.password) : ''}
          />
        </View>
        <View>
          <Pressable onPress={() => navigation.navigate('ForgetPassword')}>
            <Text style={loginStyles.forgetPassword}>{trans('Forgot Password')}?</Text>
          </Pressable>
        </View>
        {/* <RecaptchaCheckbox reCaptchaError={reCaptchaError} /> */}
        <CustomButton
          onPress={handleLogin}
          loading={loading}
          disabled={loading}
          text={trans('Login')}
        />
      </View>
    </ScrollView>
  )
}

export default Login
