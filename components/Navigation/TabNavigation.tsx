import React from 'react'
import { View, Text } from 'react-native'
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs'
import { createNativeStackNavigator } from '@react-navigation/native-stack'
import PrivateRoute from './PrivateRoute/PrivateRoute'
import { styles } from './tabNavigation.style'
import Cart from '@/screens/ShoppingCart/ShoppingCart'
import Home from '@/screens/Home/Home'
import Profile from '@/screens/Profile/Profile'
import dpr from '@/screens/Utilities/CustomStyleAttribute/dpr'

import useLangTranslation from '@/hooks/useLangTranslation'
import CustomSVG from '@/src/components/CustomSVG/CustomSVG'
import ProfileFocusIcon from '@/assets/svgs/tabNaviagtion/profile_focus.svg'
import ProfileIcon from '@/assets/svgs/tabNaviagtion/profile.svg'
import CartFocusIcon from '@/assets/svgs/tabNaviagtion/cart_focus.svg'
import CartIcon from '@/assets/svgs/tabNaviagtion/cart.svg'
import CategoryFocusIcon from '@/assets/svgs/tabNaviagtion/categories_focus.svg'
import CategoryIcon from '@/assets/svgs/tabNaviagtion/categories.svg'
import HomeFocusIcon from '@/assets/svgs/tabNaviagtion/home_focus.svg'
import HomeIcon from '@/assets/svgs/tabNaviagtion/home.svg'
import ProductListing from '@/screens/Arts/ProductListing'
import GuestBillingInformation from '@/screens/ShoppingCart/BillingInformation/GuestBillingInformation'
import GuestAddNewAddress from '@/screens/Profile/Address/AddNewAddress/GuestAddNewAddress'
import GuestOrderSummary from '@/screens/ShoppingCart/OrderSummary/GuestOrderSummary'
import BillingInformation from '@/screens/ShoppingCart/BillingInformation/BillingInformation'
import OrderSummary from '@/screens/ShoppingCart/OrderSummary/OrderSummary'
import OrderConfirmed from '@/screens/ShoppingCart/OrderConfirmed/OrderConfirmed'
import OrderFailed from '@/screens/ShoppingCart/OrderFailed/OrderFailed'

const tabIconHeight = dpr(24)
const tabIconWidth = dpr(20)

const Tab = createBottomTabNavigator()

const TabNavigation = () => {
  const { trans } = useLangTranslation()

  return (
    <Tab.Navigator
      screenOptions={{
        tabBarHideOnKeyboard: true,
        headerShown: false,
        tabBarShowLabel: false,
        tabBarStyle: styles.tabBarStyle,
        unmountOnBlur: true,
      }}>
      <Tab.Screen
        name="Home"
        component={Home}
        options={{
          tabBarIcon: ({ focused }) => (
            <View style={styles.singleTabContainer}>
              <CustomSVG
                svgIcon={focused ? HomeFocusIcon : HomeIcon}
                width={tabIconWidth}
                height={tabIconHeight}
                fill={focused ? '#252525' : '#898989'}
              />
              <Text style={styles.tabBarLabel(focused)} numberOfLines={1}>
                {trans('Home')}
              </Text>
            </View>
          ),
        }}
      />
      <Tab.Screen
        name="ProductListing"
        component={ProductListing}
        options={{
          headerLeft: () => <></>,
          tabBarIcon: ({ focused }) => (
            <View style={styles.singleTabContainer}>
              <CustomSVG
                svgIcon={focused ? CategoryFocusIcon : CategoryIcon}
                width={tabIconWidth}
                height={tabIconHeight}
                fill={focused ? '#252525' : '#898989'}
              />
              <Text style={styles.tabBarLabel(focused)} numberOfLines={1}>
                {trans('Arts')}
              </Text>
            </View>
          ),
        }}
      />
      <Tab.Screen
        name="Cart"
        component={CartStack}
        options={{
          tabBarIcon: ({ focused }) => {
            const tabBarLabelStyle = styles.tabBarLabel(focused)
            return (
              <View style={styles.singleTabContainer}>
                <View style={styles.labelArea}>
                  <CustomSVG
                    svgIcon={focused ? CartFocusIcon : CartIcon}
                    width={tabIconWidth}
                    height={tabIconHeight}
                    fill={focused ? '#252525' : '#898989'}
                  />
                  {/* {!cartLoading && cartData?.length > 0 && (
                    <Animatable.View animation="zoomIn" duration={500} style={styles.label}>
                      <Text style={styles.labelText}>{cartData?.length}</Text>
                    </Animatable.View>
                  )} */}
                </View>
                <Text style={tabBarLabelStyle} numberOfLines={1}>
                  {trans('Cart')}
                </Text>
              </View>
            )
          },
        }}
      />
      <Tab.Screen
        name="My Account"
        options={{
          tabBarIcon: ({ focused }) => (
            <View style={styles.singleTabContainer}>
              <CustomSVG
                svgIcon={focused ? ProfileFocusIcon : ProfileIcon}
                width={tabIconWidth}
                height={tabIconHeight}
                fill={focused ? '#252525' : '#898989'}
              />
              <Text style={styles.tabBarLabel(focused)} numberOfLines={1}>
                {trans('Profile')}
              </Text>
            </View>
          ),
        }}>
        {(props) => (
          <PrivateRoute>
            <Profile {...props} />
          </PrivateRoute>
        )}
      </Tab.Screen>
    </Tab.Navigator>
  )
}

const CartStack = () => {
  const Stack = createNativeStackNavigator()

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
      }}>
      <Stack.Screen name="MyCart" component={Cart} />
      <Stack.Screen name="BillingInfo">
        {(props) => (
          <PrivateRoute>
            <BillingInformation {...props} />
          </PrivateRoute>
        )}
      </Stack.Screen>
      <Stack.Screen name="OrderSummary">
        {(props) => (
          <PrivateRoute>
            <OrderSummary {...props} />
          </PrivateRoute>
        )}
      </Stack.Screen>
      <Stack.Screen name="GuestBillingInfo" component={GuestBillingInformation} />
      <Stack.Screen name="GuestAddNewAddress" component={GuestAddNewAddress} />
      <Stack.Screen name="GuestOrderSummary" component={GuestOrderSummary} />
      <Stack.Screen name="OrderDone" component={OrderConfirmed} />
      <Stack.Screen name="OrderFailed" component={OrderFailed} />
    </Stack.Navigator>
  )
}

export default TabNavigation
