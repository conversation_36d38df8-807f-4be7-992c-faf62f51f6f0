import React, { useMemo } from 'react'
import UserEntry from '@/screens/Profile/UserEntry/UserEntry'
import useGuestUser from '@/hooks/useGuestUser'
import { useAppSelector } from '@/hooks/reduxHooks'

const PrivateRoute = React.memo(function PrivateRoute({
  children,
}: {
  children: React.ReactElement
}) {
  const isGuest = useGuestUser()
  const { user } = useAppSelector((state) => state.signInReducer)

  const renderedComponent = useMemo(() => {
    if (Object.keys(user).length === 0 && isGuest) {
      return <UserEntry {...children?.props} />
    } else {
      return children
    }
  }, [user, isGuest, children])

  return renderedComponent
})

export default PrivateRoute
