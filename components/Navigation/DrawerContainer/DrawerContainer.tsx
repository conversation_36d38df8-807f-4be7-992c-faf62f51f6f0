import React, { useEffect } from 'react'
import { View, Pressable, Text, Image } from 'react-native'
import {
  DrawerContentComponentProps,
  DrawerContentScrollView,
  DrawerItem,
} from '@react-navigation/drawer'
import * as Linking from 'expo-linking'
import { useAccount } from 'wagmi'
import { drawerStyle } from './drawerContainer.style'
import DrawerItemLabel from './DrawerItemLabel'
import useAuth from '@/hooks/useAuth'
import dpr from '@/screens/Utilities/CustomStyleAttribute/dpr'
import useLangTranslation from '@/hooks/useLangTranslation'
import CustomSVG from '@/src/components/CustomSVG/CustomSVG'
import TimesIcon from '@/assets/svgs/drawer/cancel.svg'
import UserIcon from '@/assets/svgs/drawer/user.svg'
import SettingsIcon from '@/assets/svgs/drawer/settings.svg'
import AboutIcon from '@/assets/svgs/drawer/about.svg'
import DiscoverIcon from '@/assets/svgs/drawer/discover.svg'
import GuideIcon from '@/assets/svgs/drawer/guide.svg'
import ContactUsIcon from '@/assets/svgs/drawer/contact.svg'
import HubIcon from '@/assets/svgs/drawer/hub.svg'
import HubGreenIcon from '@/assets/svgs/drawer/hub_green.svg'
import TmrxGreenIcon from '@/assets/svgs/drawer/tmrx_green.svg'
import TwitterIcon from '@/assets/svgs/drawer/twitter.svg'
import FacebookIcon from '@/assets/svgs/drawer/facebook.svg'
import InstagramIcon from '@/assets/svgs/drawer/instagram.svg'
import WhatsappIcon from '@/assets/svgs/drawer/whatsapp.svg'
import DiscordIcon from '@/assets/svgs/drawer/discord.svg'
import HubTmrArtsIcon from '@/assets/svgs/drawer/hub_tmr_arts.svg'
import AnnouncementIcon from '@/assets/svgs/drawer/announcement.svg'
import EventIcon from '@/assets/svgs/drawer/event.svg'
import HubDiscoverIcon from '@/assets/svgs/drawer/hub_discover.svg'
import { useAppDispatch, useAppSelector } from '@/hooks/reduxHooks'
import useGuestUser from '@/hooks/useGuestUser'
import { gapStyle } from '@/screens/Utilities/CommonStyles/gap.style'
import { DEFAULT_LANG } from '@/src/constants/languageConst'
import i18n from '@/src/language/i18n'
import { getLanguages, setLanguage } from '@/redux/slices/language/language'
import { dynamicTextColor } from '@/screens/Utilities/CommonStyles/common.styles'
import { themeColor } from '@/theme/theme'
import AuctionIcon from '@/assets/svgs/drawer/auction.svg'
import PrivateMuseumIcon from '@/assets/svgs/drawer/private_museum_focus.svg'

const iconSize = dpr(16)

const DrawerContainer = (props: DrawerContentComponentProps) => {
  const { state } = props
  const { trans } = useLangTranslation()
  const isGuest = useGuestUser()
  const { user } = useAppSelector((state) => state.signInReducer)
  const { lng, languages } = useAppSelector((state) => state.languageReducer)
  const dispatch = useAppDispatch()
  const { address } = useAccount()
  const { jPushRegId } = useAppSelector((state) => state.updateUserProfile)

  const handleSelectLanguage = (langCode = DEFAULT_LANG) => {
    i18n.changeLanguage(langCode)
    dispatch(setLanguage(langCode))
  }

  useEffect(() => {
    dispatch(getLanguages())
  }, [dispatch])

  return (
    <View style={drawerStyle.container}>
      <DrawerContentScrollView {...props} showsVerticalScrollIndicator={false}>
        <View style={drawerStyle.header}>
          <View>
            <Image style={drawerStyle.compIcon} source={require('@/assets/images/menu_logo.png')} />
          </View>
          <Pressable onPress={props.navigation.closeDrawer}>
            <View style={drawerStyle.closeIconCont}>
              <CustomSVG svgIcon={TimesIcon} width={iconSize} height={iconSize} fill={'#FFFFFF'} />
            </View>
          </Pressable>
        </View>
        <Pressable style={drawerStyle.user} onPress={() => props.navigation.navigate('My Account')}>
          <View style={drawerStyle.userIcoin}>
            {/* {accessToken ? (
              <>
                {(user?.image || user?.picture_url) && (
                  <Image
                    source={{
                      uri: user?.image || user?.picture_url,
                    }}
                    style={drawerStyle.userImage}
                  />
                )}
              </>
            ) : ( */}
            <CustomSVG svgIcon={UserIcon} width={dpr(18)} height={dpr(18)} />
            {/* )} */}
          </View>
          {state.routes[state.index].name === 'Hub' ? (
            <View>
              <Text style={drawerStyle.noAccount}>
                {address ? trans('Address') : trans('Name')}
              </Text>
              <Text style={drawerStyle.createAccount}>
                {address ? (
                  `${address.toString().substring(0, 5)}...${address.slice(-5)}`
                ) : (
                  <Text>{trans('Wallet ID')}</Text>
                )}
              </Text>
            </View>
          ) : (
            <View>
              <Text style={drawerStyle.noAccount}>
                {!isGuest && Object.keys(user).length > 0
                  ? `${user?.firstname} ${user?.lastname}`
                  : trans('No Account')}
              </Text>
              <Text style={drawerStyle.createAccount}>
                {!isGuest && Object.keys(user).length > 0 ? (
                  user?.email
                ) : (
                  <Text>{trans('Create or login now')}</Text>
                )}
              </Text>
            </View>
          )}
        </Pressable>
        {state.routes[state.index].name === 'Hub' ? (
          <>
            <DrawerItem
              style={[drawerStyle.drawerItem, drawerStyle.lastDrawerItem]}
              label={() => (
                <DrawerItemLabel
                  svgIcon={HubTmrArtsIcon}
                  label={'TMRX'}
                  isFirst={true}
                  iconSize={dpr(20)}
                />
              )}
              onPress={() => props.navigation.navigate('Home')}
            />
            <View style={drawerStyle.hrLine} />
            <DrawerItem
              style={drawerStyle.drawerItem}
              label={() => (
                <DrawerItemLabel
                  svgIcon={HubGreenIcon}
                  label={trans('Hub')}
                  isFirst={true}
                  isHeader={true}
                />
              )}
              onPress={() => null}
            />
            <DrawerItem
              style={drawerStyle.drawerItem}
              label={() => <DrawerItemLabel svgIcon={PrivateMuseumIcon} label={'Private Museum'} />}
              onPress={() => props.navigation.navigate('PrivateMuseum')}
            />
            <DrawerItem
              style={drawerStyle.drawerItem}
              label={() => <DrawerItemLabel svgIcon={AnnouncementIcon} label={'Announcement'} />}
              onPress={() => props.navigation.navigate('Announcement')}
            />
            <DrawerItem
              style={drawerStyle.drawerItem}
              label={() => <DrawerItemLabel svgIcon={EventIcon} label={'Event'} />}
              onPress={() => props.navigation.navigate('Event')}
            />
            <DrawerItem
              style={drawerStyle.drawerItem}
              label={() => <DrawerItemLabel svgIcon={HubDiscoverIcon} label={'Discover'} />}
              onPress={() => props.navigation.navigate('Discover')}
            />
          </>
        ) : (
          <>
            <DrawerItem
              style={drawerStyle.drawerItem}
              label={() => (
                <DrawerItemLabel
                  svgIcon={TmrxGreenIcon}
                  label={'TMRX'}
                  isFirst={true}
                  isHeader={true}
                  iconSize={dpr(20)}
                />
              )}
              onPress={() => null}
            />
            <DrawerItem
              style={drawerStyle.drawerItem}
              label={() => <DrawerItemLabel svgIcon={AboutIcon} label={trans('About')} />}
              onPress={() => props.navigation.navigate('About')}
            />
            <DrawerItem
              style={drawerStyle.drawerItem}
              label={() => <DrawerItemLabel svgIcon={DiscoverIcon} label={trans('Discover')} />}
              onPress={() => props.navigation.navigate('Discover')}
            />
            <DrawerItem
              style={drawerStyle.drawerItem}
              label={() => <DrawerItemLabel svgIcon={AuctionIcon} label={trans('Auction')} />}
              onPress={() => props.navigation.navigate('AuctionListing')}
            />
            <DrawerItem
              style={drawerStyle.drawerItem}
              label={() => <DrawerItemLabel svgIcon={GuideIcon} label={trans('Guide')} />}
              onPress={() => props.navigation.navigate('Guide')}
            />
            <DrawerItem
              style={drawerStyle.drawerItem}
              label={() => <DrawerItemLabel svgIcon={ContactUsIcon} label={trans('Contact Us')} />}
              onPress={() => props.navigation.navigate('ContactUs')}
            />
            <DrawerItem
              style={[drawerStyle.drawerItem, drawerStyle.lastDrawerItem]}
              label={() => <DrawerItemLabel svgIcon={SettingsIcon} label={trans('Settings')} />}
              onPress={() => props.navigation.navigate('Setting')}
            />
            <View style={drawerStyle.hrLine} />
            <DrawerItem
              style={drawerStyle.drawerItem}
              label={() => (
                <DrawerItemLabel svgIcon={HubIcon} label={trans('Hub')} isFirst={true} />
              )}
              onPress={() => props.navigation.navigate('Hub')}
            />
            <View style={drawerStyle.socialSection}>
              <Pressable
                style={drawerStyle.socialItem}
                onPress={() => Linking.openURL('https://x.com/home')}>
                <CustomSVG svgIcon={TwitterIcon} width={iconSize} height={iconSize} />
              </Pressable>
              <Pressable
                style={drawerStyle.socialItem}
                onPress={() => Linking.openURL('https://facebook.com/')}>
                <CustomSVG svgIcon={FacebookIcon} width={iconSize} height={iconSize} />
              </Pressable>
              <Pressable
                style={drawerStyle.socialItem}
                onPress={() => Linking.openURL('https://www.instagram.com/')}>
                <CustomSVG svgIcon={InstagramIcon} width={iconSize} height={iconSize} />
              </Pressable>
              <Pressable
                style={drawerStyle.socialItem}
                onPress={() =>
                  Linking.openURL('https://web.whatsapp.com/send?phone=+85297207991&text=')
                }>
                <CustomSVG svgIcon={WhatsappIcon} width={iconSize} height={iconSize} />
              </Pressable>
              <Pressable
                style={drawerStyle.socialItem}
                onPress={() => Linking.openURL('https://discord.com/')}>
                <CustomSVG svgIcon={DiscordIcon} width={iconSize} height={iconSize} />
              </Pressable>
            </View>
          </>
        )}
        <View style={gapStyle.mt20}>
          <View style={{ flexDirection: 'row', alignItems: 'center', columnGap: 18 }}>
            {languages?.map((item) => (
              <Pressable onPress={() => handleSelectLanguage(item.code)} key={item?.code}>
                <Text
                  style={dynamicTextColor(
                    item.code === lng ? themeColor.secondyButton : themeColor.primaryColoredText,
                  )}>
                  {item?.name}
                </Text>
              </Pressable>
            ))}
          </View>
        </View>
      </DrawerContentScrollView>
    </View>
  )
}

export default DrawerContainer
