import React, { useEffect, useState } from 'react'
import { createNativeStackNavigator } from '@react-navigation/native-stack'
import { I18nManager } from 'react-native'
import * as SecureStore from 'expo-secure-store'
import DrawerStack from './DrawerStack'
import PrivateRoute from './PrivateRoute/PrivateRoute'
import { checkInternet } from '@/components/Navigation/NavigationHelper/checkInternet'
import { appFirstLaunch } from '@/components/Navigation/NavigationHelper/appFirstLaunch'
import Login from '@/components/Authentication/Login/Login'
import ForgetPassword from '@/components/Authentication/ForgetPassword/ForgetPassword'
import ResetPassword from '@/components/Authentication/ResetPassword/ResetPassword'
import PasswordChanged from '@/components/Authentication/PasswordChanged/PasswordChanged'
import Registration from '@/components/Authentication/Registration/Registration'
import ConfirmEmail from '@/components/Authentication/ConfirmEmail/ConfirmEmail'
import AccountCreated from '@/components/Authentication/AccountCreated/AccountCreated'
import ChangePassword from '@/components/Authentication/ChangePassword/ChangePassword'
import ProductDetails from '@/screens/Home/FeaturedProducts/ProductDetails/ProductDetails'
import Setting from '@/screens/Setting/Setting'
import SelectLanguage from '@/screens/Setting/SelectLanguage'
import EditProfile from '@/screens/Profile/EditProfile/EditProfile'
import Address from '@/screens/Profile/Address/Address'
import AddNewAddress from '@/screens/Profile/Address/AddNewAddress/AddNewAddress'
import OrderHistory from '@/screens/Profile/OrderHistory/OrderHistory'
import OrderDetails from '@/screens/Profile/OrderHistory/OrderDetails/OrderDetails'
import PaymentView from '@/screens/Payment/PaymentView'
import { RootStackParamList } from '@/types/navigations'
import SelectCurrency from '@/screens/Setting/SelectCurrency'
import Faqs from '@/screens/Setting/Faqs'
import PrivacyPolicy from '@/screens/Setting/PrivacyPolicy'
import TermsAndConditions from '@/screens/Setting/TermsAndConditions'
import PaymentAndDelivery from '@/screens/Setting/PaymentAndDelivery'
import Announcement from '@/screens/Hub/Announcement/Announcement'
import Event from '@/screens/Hub/Event/Event'
import Discover from '@/screens/Discover/Discover'
import HubDetails from '@/screens/Home/FeaturedProducts/ProductDetails/HubDetail'
import DiscoverDetail from '@/screens/Discover/DiscoverDetail'
import EventDetail from '@/screens/Hub/Event/EventDetail'
import AnnouncementDetail from '@/screens/Hub/Announcement/AnnouncementDetail'
import About from '@/screens/About/About'
import Map from '@/screens/Setting/Map'
import { useAppDispatch } from '@/hooks/reduxHooks'
import { login } from '@/redux/slices/auth/signIn'
import i18n from '@/src/language/i18n'
import { getLanguages, setLanguage } from '@/redux/slices/language/language'
import EnquiryForm from '@/screens/Setting/EnquiryForm/EnquiryForm'
import RepayOrder from '@/screens/Profile/OrderHistory/RepayOrder'
import AuctionListing from '@/screens/Auction/AuctionListing'
import AuctionDetail from '@/screens/Auction/AuctionDetail'
import AuctionPayNow from '@/screens/Auction/AuctionPayNow'
import AuctionConfirmed from '@/screens/Auction/AuctionConfirmed'
import AuctionSuccess from '@/screens/Auction/AuctionSuccess'
import AuctionFail from '@/screens/Auction/AuctionFail'
import MyAuctionListing from '@/screens/Profile/MyAuction/MyAuctionListing'
import MyBidDetail from '@/screens/Profile/MyAuction/MyBidDetail'
import ExhibitionAccount from '@/screens/Hub/ExhibitionAccount/ExhibitionAccount'
import EditHubProfile from '@/screens/Hub/ExhibitionAccount/EditHubProfile/EditHubProfile'
import MyLotDetail from '@/screens/Profile/MyAuction/MyLotDetail'
import AssetList from '@/screens/Hub/AssetList/AssetList'
import StatisticList from '@/screens/Hub/StatisticList/StatisticList'
import HallList from '@/screens/Hub/HallList/HallList'
import EditHall from '@/screens/Hub/HallList/EditHall/EditHall'
import ExhibitionDetail from '@/screens/Hub/ExhibitionHall/ExhibitionDetail'
import CreateAuction from '@/screens/Profile/OrderHistory/CreateAuction/CreateAuction'
import CreateAuctionSuccess from '@/screens/Profile/OrderHistory/CreateAuction/CreateAuctionSuccess'
import Artworks from '@/screens/Hub/Artworks/Artworks'
import Guide from '@/screens/Guide/Guide'
import ContactUs from '@/screens/Setting/ContactUs'
import PrivateMuseum from '@/screens/Hub/PrivateMuseum/PrivateMuseum'
import NotificationSetting from '@/screens/Setting/NotificationSetting'

const Stack = createNativeStackNavigator<RootStackParamList>()

const Welcome = () => {
  const [isFirstLaunch, setIsFirstLaunch] = useState<boolean | null>(null)
  const internetIsConnecter = checkInternet()
  const dispatch = useAppDispatch()

  const authCheck = async () => {
    const localAuth = await SecureStore.getItemAsync('isLoggedIn')
    if (localAuth) {
      const auth = JSON.parse(localAuth)
      if (auth?.user) {
        dispatch(
          login({
            user: auth.user,
          }),
        )
      }
    }

    // set application language
    const langCode = await SecureStore.getItemAsync('languageCode')
    if (langCode == null) {
      const res = await dispatch(getLanguages())
      const { success, data } = res?.payload
      if (success === 1) {
        dispatch(setLanguage(data[0].code))
      }
    } else {
      i18n.changeLanguage(langCode)
      await dispatch(setLanguage(langCode))
    }
  }

  useEffect(() => {
    let isMounted = true
    const initialize = async () => {
      if (isMounted) {
        const isFirst = await appFirstLaunch()
        setIsFirstLaunch(!!isFirst)
      }
    }

    initialize()
    authCheck()

    const unsubscribe = internetIsConnecter()
    return () => {
      isMounted = false
      unsubscribe()
    }
  }, [])

  if (isFirstLaunch === null) {
    return null
  }
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        animation: I18nManager.isRTL ? 'slide_from_left' : 'slide_from_right',
      }}
      initialRouteName={'DrawerStack'}>
      <Stack.Screen name="DrawerStack" component={DrawerStack} />
      <Stack.Screen name="ProductDetails" component={ProductDetails} />
      <Stack.Screen name="Setting" component={Setting} />
      <Stack.Screen name="SelectLanguage" component={SelectLanguage} />
      <Stack.Screen name="SelectCurrency" component={SelectCurrency} />
      <Stack.Screen name="Faqs" component={Faqs} />
      <Stack.Screen name="PrivacyPolicy" component={PrivacyPolicy} />
      <Stack.Screen name="TermsAndConditions" component={TermsAndConditions} />
      <Stack.Screen name="PaymentAndDelivery" component={PaymentAndDelivery} />
      <Stack.Screen name="EditProfile">
        {(props) => (
          <PrivateRoute>
            <EditProfile {...props} />
          </PrivateRoute>
        )}
      </Stack.Screen>
      <Stack.Screen name="AddressBook">
        {(props) => (
          <PrivateRoute>
            <Address {...props} />
          </PrivateRoute>
        )}
      </Stack.Screen>
      <Stack.Screen name="AddNewAddress">
        {(props) => (
          <PrivateRoute>
            <AddNewAddress {...props} />
          </PrivateRoute>
        )}
      </Stack.Screen>
      <Stack.Screen name="OrderHistory">
        {(props) => (
          <PrivateRoute>
            <OrderHistory {...props} />
          </PrivateRoute>
        )}
      </Stack.Screen>
      <Stack.Screen name="OrderDetails">
        {(props) => (
          <PrivateRoute>
            <OrderDetails {...props} />
          </PrivateRoute>
        )}
      </Stack.Screen>
      <Stack.Screen name="RepayOrder">
        {(props) => (
          <PrivateRoute>
            <RepayOrder {...props} />
          </PrivateRoute>
        )}
      </Stack.Screen>
      <Stack.Screen name="MyAuctionListing">
        {(props) => (
          <PrivateRoute>
            <MyAuctionListing {...props} />
          </PrivateRoute>
        )}
      </Stack.Screen>
      <Stack.Screen name="PaymentWebview" component={PaymentView} />
      <Stack.Screen name="Login" component={Login} />
      <Stack.Screen name="ForgetPassword" component={ForgetPassword} />
      <Stack.Screen name="ResetPassword" component={ResetPassword} />
      <Stack.Screen name="ChangePassword" component={ChangePassword} />
      <Stack.Screen name="PasswordResetDone" component={PasswordChanged} />

      <Stack.Screen name="Registration" component={Registration} />
      <Stack.Screen name="ConfirmEmail" component={ConfirmEmail} />
      <Stack.Screen name="AccountCreated" component={AccountCreated} />
      <Stack.Screen name="Announcement" component={Announcement} />
      <Stack.Screen name="Event" component={Event} />
      <Stack.Screen name="EventDetail" component={EventDetail} />
      <Stack.Screen name="AnnouncementDetail" component={AnnouncementDetail} />
      <Stack.Screen name="Discover" component={Discover} />
      <Stack.Screen name="DiscoverDetail" component={DiscoverDetail} />
      <Stack.Screen name="HubDetail" component={HubDetails} />
      <Stack.Screen name="About" component={About} />
      <Stack.Screen name="Guide" component={Guide} />
      <Stack.Screen name="ContactUs" component={ContactUs} />
      <Stack.Screen name="Map" component={Map} />
      <Stack.Screen name="EnquiryForm" component={EnquiryForm} />
      <Stack.Screen name="AuctionListing" component={AuctionListing} />
      <Stack.Screen name="AuctionDetail" component={AuctionDetail} />
      <Stack.Screen name="AuctionPayNow" component={AuctionPayNow} />
      <Stack.Screen name="AuctionConfirmed" component={AuctionConfirmed} />
      <Stack.Screen name="AuctionSuccess" component={AuctionSuccess} />
      <Stack.Screen name="AuctionFail" component={AuctionFail} />
      <Stack.Screen name="MyBidDetail" component={MyBidDetail} />
      <Stack.Screen name="MyLotDetail" component={MyLotDetail} />
      <Stack.Screen name="ExhibitionAccount" component={ExhibitionAccount} />
      <Stack.Screen name="EditHubProfile" component={EditHubProfile} />
      <Stack.Screen name="StatisticList" component={StatisticList} />
      <Stack.Screen name="AssetList" component={AssetList} />
      <Stack.Screen name="HallList" component={HallList} />
      <Stack.Screen name="EditHall" component={EditHall} />
      <Stack.Screen name="Artworks" component={Artworks} />
      <Stack.Screen name="PrivateMuseum" component={PrivateMuseum} />
      <Stack.Screen name="ExhibitionDetail" component={ExhibitionDetail} />
      <Stack.Screen name="CreateAuction" component={CreateAuction} />
      <Stack.Screen name="CreateAuctionSuccess" component={CreateAuctionSuccess} />
      <Stack.Screen name="NotificationSetting" component={NotificationSetting} />
    </Stack.Navigator>
  )
}

export default Welcome
