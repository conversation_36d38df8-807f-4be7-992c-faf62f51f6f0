import React, { useMemo } from 'react'
import * as Animatable from 'react-native-animatable'
import { View, Text } from 'react-native'
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs'
import { useAccount, useAccountEffect } from 'wagmi'
import { useAppKit } from '@reown/appkit-wagmi-react-native'
import { styles, tabBarLabel } from './hubNavigation.style'
import { WalletTabBarIcon } from './WalletTabBarIcon'
import dpr from '@/screens/Utilities/CustomStyleAttribute/dpr'

import useLangTranslation from '@/hooks/useLangTranslation'
import CustomSVG from '@/src/components/CustomSVG/CustomSVG'
import ExhibitionHallIcon from '@/assets/svgs/tabNaviagtion/exhibition_hall.svg'
import ExhibitionHallFocusIcon from '@/assets/svgs/tabNaviagtion/exhibition_hall_focus.svg'
import ShowroomFocusIcon from '@/assets/svgs/tabNaviagtion/showroom_focus.svg'
import ShowroomIcon from '@/assets/svgs/tabNaviagtion/showroom.svg'
import PublicMuseumFocusIcon from '@/assets/svgs/tabNaviagtion/public_museum_focus.svg'
import PublicMuseumIcon from '@/assets/svgs/tabNaviagtion/public_museum.svg'
import { useAppDispatch, useAppSelector } from '@/hooks/reduxHooks'
import PublicMuseum from '@/screens/Hub/PublicMuseum/PublicMuseum'
import Showroom from '@/screens/Hub/Showroom/Showroom'
import { resetWalletToken } from '@/redux/slices/auth/basicAuth'
import ExhibitionAccount from '@/screens/Hub/ExhibitionAccount/ExhibitionAccount'
import ExhibitionHall from '@/screens/Hub/ExhibitionHall/ExhibitionHall'

const tabIconHeight = dpr(24)
const tabIconWidth = dpr(20)

const Tab = createBottomTabNavigator()

const HubNavigation = () => {
  const { trans } = useLangTranslation()
  const { loading: cartLoading, cartData } = useAppSelector((state) => state.cartProductSlice)
  const dispatch = useAppDispatch()

  const { open } = useAppKit()
  const { isConnected } = useAccount()

  const toggleWalletModal = () => {
    return open({ view: isConnected ? 'Account' : 'Connect' })
  }

  const WalletTab = ({ focused }: { focused: boolean }) =>
    useMemo(() => <WalletTabBarIcon focused={focused} onPress={toggleWalletModal} />, [focused])

  useAccountEffect({
    onDisconnect() {
      console.log('on disconnect')
      dispatch(resetWalletToken())
    },
  })

  return (
    <Tab.Navigator
      screenOptions={{
        tabBarHideOnKeyboard: true,
        headerShown: false,
        tabBarShowLabel: false,
        tabBarStyle: styles.tabBarStyle,
      }}>
      <Tab.Screen
        name="PublicMuseum"
        component={PublicMuseum}
        options={{
          tabBarIcon: ({ focused }) => (
            <View style={styles.singleTabContainer}>
              <CustomSVG
                svgIcon={focused ? PublicMuseumFocusIcon : PublicMuseumIcon}
                width={tabIconWidth}
                height={tabIconHeight}
                fill={focused ? '#252525' : '#898989'}
              />
              <Text style={tabBarLabel(focused)} numberOfLines={1}>
                Public
              </Text>
            </View>
          ),
        }}
      />
      <Tab.Screen
        name="Showroom"
        component={Showroom}
        options={{
          headerLeft: () => <></>,
          tabBarIcon: ({ focused }) => (
            <View style={styles.singleTabContainer}>
              <CustomSVG
                svgIcon={focused ? ShowroomFocusIcon : ShowroomIcon}
                width={tabIconWidth}
                height={tabIconHeight}
                fill={focused ? '#252525' : '#898989'}
              />
              <Text style={tabBarLabel(focused)} numberOfLines={1}>
                Showroom
              </Text>
            </View>
          ),
        }}
      />
      <Tab.Screen
        name="ExhibitionHall"
        component={ExhibitionHall}
        options={{
          tabBarIcon: ({ focused }) => {
            const tabBarLabelStyle = tabBarLabel(focused)
            return (
              <View style={styles.singleTabContainer}>
                <View style={styles.labelArea}>
                  <CustomSVG
                    svgIcon={focused ? ExhibitionHallFocusIcon : ExhibitionHallIcon}
                    width={tabIconWidth}
                    height={tabIconHeight}
                    fill={focused ? '#252525' : '#898989'}
                  />
                  {!cartLoading && cartData?.length > 0 && (
                    <Animatable.View animation="zoomIn" duration={500} style={styles.label}>
                      <Text style={styles.labelText}>{cartData?.length}</Text>
                    </Animatable.View>
                  )}
                </View>
                <Text style={tabBarLabelStyle} numberOfLines={1}>
                  Exhibition
                </Text>
              </View>
            )
          },
        }}
      />
      <Tab.Screen
        name="Account"
        options={{
          tabBarIcon: WalletTab,
        }}
        component={ExhibitionAccount}
      />
    </Tab.Navigator>
  )
}

export default HubNavigation
